$color-announcement-status-configuration: #F7BE73FF;
$color-announcement-status-active:        #58C543FF;
$color-announcement-status-in-progress:   #53DF52FF;
$color-announcement-status-finished:      #009BDBFF;
$color-announcement-status-inactive:      #FA8500FF;
$color-announcement-status-archived:      #E4534EFF;

.Announcement
{
  &--status
  {
    border-radius: 3px;
    padding: 2px 10px;
    white-space: nowrap;

    &--CONFIGURATION
    {
      color: #FFFFFFFF;
      background-color: $color-announcement-status-configuration;
    }

    &--ACTIVE
    {
      color: #FFFFFFFF;
      background-color: $color-announcement-status-active;
    }


    &--IN_PROGRESS
    {
      color: #FFFFFFFF;
      background-color: $color-announcement-status-in-progress;
    }

    &--FINISHED
    {
      color: #FFFFFFFF;
      background-color: $color-announcement-status-finished;
    }

    &--INACTIVE
    {
      color: #212121FF;
      background-color: $color-announcement-status-inactive;
    }

    &--ARCHIVED
    {
      color: #FFFFFFFF;
      background-color: $color-announcement-status-archived;
    }
  }
}
.color-neutral-darkest {
  color: var(--color-neutral-darkest);
}
