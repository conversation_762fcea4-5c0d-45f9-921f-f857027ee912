import axios from "axios";
export default {
    namespaced: true,
    state: {
        loading: true
    },
    getters: {
        isLoading(state) { return state.loading; }
    },
    mutations: {
        SET_LOADING(state, isLoading) { state.loading = isLoading; }
    },
    actions: {
        loadChatData({ }, payload) {
            if (payload.channelId == null) return Promise.resolve({
                error: true,
                data: []
            });
            return axios.post(`/admin/chat/channel/messages`, payload)
              .then((result) => result.data);
        },

        getUserChat({}, { parentId, userId, name = null, entityId = null, entityType = null }) {
            return axios.post('/admin/chat/channel', { parentId, userId, name, entityId, entityType })
              .then((result) => result.data);
        },

        sendMessage({}, { channelId, message}) {
            return axios.post('/admin/chat/message/send', {channelId, message})
              .then((result) => result.data);
        },

        getUnseenMessages({}, { channelId = null, serverId = null}) {
            return axios.post('/admin/chat/unseen-messages', { channelId, serverId })
                .then(result => result.data)
        }
    }
}
