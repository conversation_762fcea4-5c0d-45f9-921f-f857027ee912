import axios from "axios";
import {make} from "vuex-pathify";

const state = {
    loading: true,
    selectedForumThread: null
};

export default {
    namespaced: true,
    state,
    getters: {
        ...make.getters(state),
        isLoading(state) { return state.loading; }
    },
    mutations: {
        ...make.mutations(state)
    },
    actions: {
        setSelectedForumThread({ commit }, thread) {
            commit('SET_SELECTED_FORUM_THREAD', thread);
        },
        async saveAnnouncementForum({}, { formData }) {
            try {
                const url = `/admin/chat/channel/create`;
                const result = await axios.post(url, formData);
                return result.data;
            } finally {

            }
        },

        async updateAnnouncementForum({}, { formData }) {
            try {
                const url = `/admin/chat/channel/update`;
                const result = await axios.post(url, formData);
                return result.data;
            } finally {

            }
        },

        async getAnnouncementForum({ commit }, id) {
            commit('SET_LOADING', true);

            try {
                const result = await axios.post('/admin/chat/channels', { channelId: id });
                return result.data;
            } finally {
                commit('SET_LOADING', false);
            }
        },

        async removeAnnouncementForum({}, { channelId, parentId }) {
            const result = await axios.post(`/admin/chat/channel/remove`, { channelId, parentId });
            return result.data;
        },

        async sendReaction({}, messageId ) {
            return axios.post('/admin/chat/message/like', { messageId }).then((result) => result.data);
        },

        async sendReport({}, messageId ) {
            return axios.post('/admin/chat/message/report', { messageId }).then((result) => result.data);
        },

        async loadForumPostFullData({ }, channelId) {
            try {
                const result = await axios.post(`/admin/chat/channel/messages`, { channelId });
                return result.data;
            } finally {
            }
        },

        async sendReply({}, { channelId, replyTo = null, message}) {
            const result = await axios.post('/admin/chat/message/send', {channelId, replyTo, message});
            return result.data;
        }
    }
}
