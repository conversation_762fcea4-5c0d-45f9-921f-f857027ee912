<template>
  <div class="ForumPost">
    <div class="col-12 d-flex flex-column align-items-center justify-content-center" v-if="saving">
      <loader :is-loaded="saving"></loader>
      <span>{{ $t('SAVING') }}</span>
    </div>
    <form class="ForumPost--form" :class="{'edit': update}" :id="formId" action="" @submit.prevent="submit()" v-show="!saving">
      <div class="w-100">
        <div class="ForumPost--form--title">
          <span>{{ $t('FORUM.NEW_THREAD_CONFIGURATION') }}</span> <button type="button" class="--button-close" @click="$emit('cancel')"><i class="fa fa-times-circle"></i></button>
        </div>
        <div class="ForumPost--form--body">
          <input type="text" id="title" name="name" class="form-control" required v-model="title" :placeholder="$t('FORUM.PLACEHOLDER.TITLE')">
          <textarea name="message" id="message" rows="5" class="form-control" :required="!update" v-model="message" :placeholder="$t('FORUM.PLACEHOLDER.MESSAGE')" v-show="!update"></textarea>
        </div>
        <div class="ForumPost--form--date">
          <div class="form-group p-0">
            <label for="from">{{ $t('DATE_TIME_TITLE.FROM') }}</label>
            <input type="datetime-local" v-model="from">
          </div>
          <div class="form-group p-0">
            <label for="to">{{ $t('DATE_TIME_TITLE.TO') }}</label>
            <input type="datetime-local" v-model="to">
          </div>
        </div>
      </div>
      <button class="send-form" type="submit"><i class="fa fa-send"></i></button>
    </form>
  </div>
</template>

<script>
import Loader from "../../../admin/components/Loader.vue";

export default {
  name: "ForumPost",
  components: {Loader},
  props: {
    /**
     * If is a new forum, id is the announcement id, otherwise is the forum id
     */
    id: {
      type: Number|String,
      required: true
    },

    formId: {
      type: String,
      default: 'form-forum-post'
    },

    parentId: {
      type: Number|String,
      default: null
    },

    emitEvents: {
      type: Boolean,
      default: false
    },

    update: {
      type: Boolean,
      default: false
    },

    elementProp: null
  },
  data() {
    return {
      saving: false,
      title: this.elementProp?.title ?? '',
      message: this.elementProp?.message ?? '',
      from: null,
      to: null
    };
  },
  methods: {
    submit() {
      this.$alertify.confirmWithTitle(
          this.$t('FORUM.SAVE.CONFIRM.TITLE'),
          this.$t('FORUM.SAVE.CONFIRM.DESCRIPTION'),
          () => {
            this.save(this.update)
          },
          () => {},
      )
    },

    save(update = false) {
      if (this.saving) return;
      const self = this;
      async function saveForum() {
        if (update) {
          return self.$store.dispatch('forumModule/updateAnnouncementForum',  {
            formData: {
              name: self.title,
              channelId: self.id,
              parentId: self.parentId,
              from: self.from,
              to: self.to
            }
          });
        }

        return self.$store.dispatch('forumModule/saveAnnouncementForum',  {
          formData: {
            name: self.title,
            message: self.message,
            parentId: self.parentId,
            from: self.from,
            to: self.to
          }
        });
      }

      this.saving = true;
      saveForum().then(res => {
        const { error, data } = res;
        this.$toast.info(this.$t('SAVING') || '');
        if (error) {
          this.$toast.clear();
          this.$toast.error(this.$t('CHAPTER.CONTENT.SAVE.FAILED') || '');
          if (this.emitEvents) {
            this.$emit('failed');
          }
        }
        else {
          this.$toast.clear();
          this.$toast.success(this.$t('CHAPTER.CONTENT.SAVE.SUCCESS') || '');
          if (this.emitEvents) {
            this.$emit('success', data);
          }
        }
      }).finally(() => {
        this.saving = false;
      })
    }
  }
}
</script>

 <style scoped lang="scss"> 
.ForumPost {
  width: 100%;
  margin: 0.25rem 0;
  background-color: $forums-active-thread-footer-bg-color;
  position: absolute;
  z-index: 2;
  inset: auto;
  padding: 1rem 0 1rem 1rem;
  border: solid var(--color-neutral-lighter);
  border-width: 0 0 2px;

  &--form {
    display: grid;
    //grid-template-columns: 200px auto calc(2rem + 40px);
    grid-template-columns: 1fr calc(2rem + 40px);
    padding: 0 0.25rem 0 0;
    column-gap: 0.15rem;

    .ForumPost--form--body {
      width: 100%;
      display: grid;
      grid-template-columns: 200px 1fr;
    }

    .ForumPost--form--date {
      display: grid;
      width: 100%;
      grid-template-columns: 1fr 1fr;
    }

    &--title {
      @include mixin-post-reply;
      border-radius: unset;
      width: 100%;
      font-size: 16px;
      color: #9e9e9e;
      grid-column-start: 1;
      grid-column-end: 3;
    }

    .form-control {
      border-radius: 0 0 5px 5px !important;
    }

    input {
      @include mixin-forum-input;
    }

    //&.edit #title {
    //  grid-column-end: 3;
    //}

    textarea {
      resize: none;
      @include mixin-forum-input;
    }

    button.send-form {
      @include mixin-forum-button;
      width: 40px !important;
      height: 40px !important;
      background-color: #019DDF !important;
      color: #FFFFFF !important;
      margin: auto;
      &:hover {
        color: #FFFFFF !important;
      }
    }
  }
}
</style>
