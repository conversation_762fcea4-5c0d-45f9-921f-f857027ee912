<template>
  <div class="AnnouncementConfig" v-if="id !== undefined">
    <div class="mb-2 col-12 d-flex align-items-center justify-content-end">
      <span class="mr-auto">
        <i class="fa fa-users"></i>
          <span
              class="counter"
              v-html="$t('ANNOUNCEMENT.NUMBER_OF_USER_CALLED', [users.length, maxUsers])" />
      </span>
      <button type="button" class="btn btn-primary" @click="edit = !edit"><i class="fa fa-plus"></i> {{ edit ? $t('CANCEL') : $t('ANNOUNCEMENT.MODIFY_USERS') }}</button>
    </div>

    <user-filter
        v-if="edit"
        v-model="users"
        prefix="ANNOUNCEMENT"
        :url-source="`/admin/announcement/${id}/available-users`"
        :url-add="`/admin/announcement/${id}/add-called-user`"
        :url-add-all="`/admin/announcement/${id}/add-users`"
        :url-remove="`/admin/announcement/${id}/remove-called-user`"
        :url-remove-all="`/admin/announcement/${id}/remove-users`"
        source-property="users"
        @refresh="loadCalledUsers()"/>

    <div class="AnnouncementConfig--users">
      <div
        class="d-flex align-items-center justify-content-center"
        v-if="loading"
      >
        <loader :is-loaded="loading" />
      </div>
      <table class="table" v-else>
        <thead>
          <tr>
            <th></th>
            <th>{{ $t("USER.TITLE") }}</th>
            <th>{{ $t("USER.EMAIL") }}</th>
            <th>{{ $t("DIPLOMA") }}</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="user in usersPaged" :key="user.id">
            <td style="width: 60px">
              <button
                class="btn btn-sm btn-primary"
                @click="activeChatUser = user"
              >
                <i class="fas fa-sms"></i>
              </button>
            </td>
            <td>
              <router-link
                :to="{
                  name: 'ViewAnnouncementUserDetail',
                  params: { id: id, user: user.id },
                }"
              >
                {{ user.firstName }} {{ user.lastName }}
              </router-link>
            </td>
            <td>{{ user.email }}</td>
            <td>
              <button
                type="button"
                class="btn btn-primary btn-sm"
                @click="downloadDiploma(user)"
              >
                <i class="fa fa-download"></i>
              </button>
            </td>
            <td style="width: 110px" class="text-right">
              <button
                type="button"
                class="btn btn-info btn-sm"
                :disabled="user.notified"
                @click="notifyCalledUser(user)"
              >
                <i class="fa fa-envelope"></i>
              </button>
              <button
                type="button"
                class="btn btn-danger btn-sm"
                @click="deleteCalledUser(user)"
              >
                <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <div class="w-100">
        <pagination
          :page-size="10"
          :items="users"
          :total-items="totalItems"
          :prop-current-page="page"
          @current-page="onCurrentPageChange"
          @items-page="usersPaged = $event"
        />
      </div>
    </div>

    <div class="user-chat" v-if="activeChatUser">
      <forum
        :show-header="false"
        :show-threads="false"
        v-model="chat"
        :loading-messages="loadingChat"
        @reply="sendMessage"
      >
        <template v-slot:top-content>
          <div class="CloseChat">
            <span class="title"
              ># {{ activeChatUser?.firstName }}
              {{ activeChatUser?.lastName }}</span
            >
            <button @click="activeChatUser = null">
              <i class="fa fa-close"></i>
            </button>
          </div>
        </template>
      </forum>
    </div>
  </div>
</template>

<script>
import { get, sync } from "vuex-pathify";
import Forum from "../../common/components/forum/Forum.vue";
import Loader from "../../admin/components/Loader.vue";
import UserFilter from "../../common/components/filter/UserFilter.vue";
import Pagination from "../../admin/components/Pagination.vue";

export default {
  name: "AnnouncementConfig",
  components: { Pagination, Forum, Loader, UserFilter },
  props: {
    id: {
      type: Number | String,
      required: true,
    },
    maxUsers: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      activeChatUser: null,

      loadingChat: false,
      chat: [],
      edit: false,
      usersPaged: []
    };
  },
  computed: {
    users: sync("announcementModule/calledUsers"),
    loading: get("announcementModule/loadingCalledUsers"),
    totalItems: get("announcementModule/calledUsersPagination@totalItems"),
    page: sync("announcementModule/calledUsersPagination@page"),
  },
  watch: {
    // id(newValue) {
    //   if (newValue) this.loadCalledUsers();
    // },
    activeChatUser() {
      if (this.activeChatUser) this.loadChat();
      else this.chat = [];
    },
  },
  mounted() {
    // if (this.id) this.loadCalledUsers();
  },
  methods: {
    userDiploma(user) {
      this.$store
        .dispatch("announcementModule/downloadUserDiploma", {
          announcementId: this.id,
          userId: user.id,
        })
        .then((res) => {
          const { data, error } = res;
          const link = document.createElement("a");
          link.href = data;
          link.download = `${user.name}.pdf`;
          link.click();
        });
    },

    async downloadDiploma(user) {
      const data = {
        idUser: user.id,
        idAnnouncement: this.id,
      };

      const pdf = await this.$store.dispatch(
        "announcementModule/downloadDiploma",
        data
      );

      const link = document.createElement("a");
      link.href = pdf.data;
      link.download = `${pdf.nombre}.pdf`;
      link.click();
    },

    onCurrentPageChange(page) {
      this.page = page;
      this.loadCalledUsers();
    },

    async loadCalledUsers() {
      await this.$store.dispatch(
        "announcementModule/loadAnnouncementCalledUsers",
        this.id
      );
    },

    notifyCalledUser(user, index) {
      this.$alertify.confirmWithTitle(
        this.$t("ANNOUNCEMENT.NOTIFICATION.CONFIRM.TITLE"),
        this.$t("ANNOUNCEMENT.NOTIFICATION.CONFIRM.DESCRIPTION"),
        async () => {
          const result = await this.$store.dispatch(
            "announcementModule/notifyCalledUser",
            { announcementId: this.id, userId: user.id, callId: user.callId }
          );
          const { error, data } = result;
          if (error) {
            this.$toast.error(
              this.$t("ANNOUNCEMENT.NOTIFICATION.RESULT.FAILED") + ""
            );
          } else {
            const index = this.users.findIndex(u => u.id === user.id);
            if (index >= 0) this.users[index].notified = true;
            this.$toast.success(
              this.$t("ANNOUNCEMENT.NOTIFICATION.RESULT.SUCCESS") + ""
            );
          }
        },
        () => {}
      );
    },

    deleteCalledUser(user) {
      this.$alertify.confirmWithTitle(
        this.$t("ANNOUNCEMENT.DELETE_CALLED_USER.CONFIRM.TITLE"),
        this.$t("ANNOUNCEMENT.DELETE_CALLED_USER.CONFIRM.DESCRIPTION", [
          user.email,
        ]),
        () => {
          this.$store
            .dispatch("announcementModule/deleteCalledUser", {
              announcementId: this.id,
              callId: user.callId,
            })
            .then((res) => {
              const { error, data } = res;
              if (error)
                this.$toast.error(
                  this.$t("ANNOUNCEMENT.DELETE_CALLED_USER.RESULT.FAILED", [
                    user.email,
                  ]) + ""
                );
              else {
                this.$toast.success(
                  this.$t("ANNOUNCEMENT.DELETE_CALLED_USER.RESULT.SUCCESS", [
                    user.email,
                  ]) + ""
                );
                const index = this.users.findIndex(
                  (item) => item.id === user.id
                );
                this.users.splice(index, 1);
              }
            });
        },
        () => {}
      );
    },

    loadChat() {
      if (this.activeChatUser == null) return;
      this.loadingChat = true;
      this.$store
        .dispatch("announcementModule/getUserChat", {
          announcementId: this.id,
          userId: this.activeChatUser.id,
        })
        .then((res) => {
          const { data } = res;
          this.chat = data;
        })
        .finally(() => {
          this.loadingChat = false;
        });
    },

    sendMessage(data) {
      this.$store
        .dispatch("announcementModule/sendUserChat", {
          announcementId: this.id,
          userId: this.activeChatUser.id,
          formData: data,
        })
        .then((res) => {
          const { error, data } = res;
          if (error) {
            this.$toast.error(this.$t("SEND_MESSAGE.ERROR") + "");
          } else this.chat.push(data);
        });
    },
  },
};
</script>

 <style scoped lang="scss"> 
.AnnouncementConfig {
  .counter {
    :deep(span) {
      display: inline-block;
      padding: 0.25em 0.4em;
      font-size: 75%;
      font-weight: 700;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: 0.25rem;
      transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      color: #ffffff;
      background-color: #6c757d !important;
    }
  }

  .user-chat {
    width: 100%;
    position: fixed;
    bottom: 0;
    right: 0;

    @media #{min-small-screen()} {
      width: 420px;
    }

    .CloseChat {
      width: 100%;
      padding: 1rem 1rem 1rem 1rem;
      background-color: $forums-active-thread-title-bg-color;
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: flex-start;

      .title {
        color: #019ddf;
        font-size: 18px;
        flex-grow: 1;
      }
      button {
        @include mixin-forum-button;
      }
    }
  }
}
</style>
