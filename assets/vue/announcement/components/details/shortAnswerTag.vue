<template>
  <div class="shortAnswerTag">
    <span>{{question}}</span>
    <a v-if="(href || '').length" :href="href" rel="noreferrer noopener" target="_blank">{{answer}}</a>
    <b v-else>{{answer}}</b>
  </div>
</template>

<script>
export default {
  name: "shortAnswerTag",
  props: {
    question: { type: String, default: '' },
    answer: { type: String | Number, default: '--' },
    href: { type: String, default: '' }
  }
}
</script>

 <style scoped lang="scss"> 
.shortAnswerTag {
  display: flex;
  gap: 0.5rem;

  a {
    color: var(--color-primary);
    text-decoration: underline;
  }
}
</style>
