<template>
<div class="iconNotification">
  <div class="d-flex justify-content-center align-items-center position-relative">
    <i class="mainIcon fa" :class="icon"></i>
    <div class="notification d-flex justify-content-center align-items-center"
         :class="className">
      <i v-if="showSuccessIcon" class="fa fa-check"></i>
      <i v-if="showErrorIcon" class="fa fa-times"></i>
      <b v-if="showValue">{{ value }}</b>
    </div>
  </div>
</div>
</template>

<script>
export default {
  name: "iconNotification",
  props: {
    icon: {
      type: String, default: ''
    },
    value: {
      type: String|Number, default: 0
    },
    showValue: {
      type: Boolean, default: false
    },
  },
  computed: {
    showSuccessIcon() {
      return (!this.showValue && !this.value)
    },
    showErrorIcon() {
      return (!this.showValue && !!this.value)
    },
    className() {
      return !this.value ? 'bg-success' : 'bg-danger';
    }
  }
}
</script>

 <style scoped lang="scss"> 
.iconNotification {
  border-radius: 5px;
  background-color: var(--color-primary);
  color: white;
  user-select: none;

  > div {
    width: 2.5rem;
    height: 2.6rem;
  }
  .mainIcon {
    font-size: 1.2rem;
  }

  .notification {
    position: absolute;
    right: -0.6rem;
    top: -0.2rem;
    border: 1px solid white;
    border-radius: 100%;
    width: 1.7rem;
    height: 1.7rem;
    font-size: 0.7rem;
  }

  span {
    font-size: 0.8rem;
  }
}
</style>
