<script>
import { get, sync } from "vuex-pathify";

import { configurationClientAnnouncement } from "../mixins/configurationClientAnnouncement";
import {
  COURSE_TYPE_ONLINE,
  COURSE_TYPE_MIXED,
  COURSE_TYPE_ON_SITE,
  COURSE_TYPE_VIRTUAL_CLASSROOM,
  COURSE_TYPE_MIXED_EXTERN,
  COURSE_TYPE_ON_SITE_EXTERN,
  COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN,
} from "../../course/mixins/constants";


import FileAsText from "../../common/components/file/FileAsText.vue";
import Spinner from "../../base/BaseSpinner.vue";
import AccordionContent from "./details/accordionContent.vue";
import LabelWithInfo from "../../common/components/ui/LabelWithInfo.vue";
import ModalMeetings from "./announcementForm/modalMeetings";
import Multiselect from "vue-multiselect";
import TutorFormModal from "./tutor/TutorFormModal.vue";
import TutorComponent from "./announcementForm/AnnouncementGroups/TutorComponent.vue";

export default {
  name: "UserGroup",
  components: {
    TutorComponent,
    TutorFormModal,
    ModalMeetings,
    LabelWithInfo,
    AccordionContent,
    Spinner,
    FileAsText,
    Multiselect,
  },
  mixins: [configurationClientAnnouncement],
  props: {
    value: {
      type: Object | Array,
      default: () => ({
        id: -1,
        code: "",
        numberOfSessions: 0,
        place: "",
        sessions: [],
      }),
    },
    tutors: {
      type: Object | Array,
      default: () => [],
    },
    tag: {
      type: String | Number,
      default: "",
    },

    title: {
      type: String,
      default: "Group",
    },

    opened: {
      type: Boolean,
      default: false,
    },

    announcementType: {
      type: String,
      required: true,
    },

    allowDelete: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      sessionSelectedIndex: null,
      loadingProvider: false,
      tempTutor: null,
      tutor: {
        tutorId: -1,
        name: "",
        email: "",
        telephone: "",
        cv: null,
        tutoringTime: "",
        identificationValue: "",
        identification: {
          identification: null,
          value: null,
        },
      },
      savingTutor: false,
    };
  },
  computed: {
    virtualClassroomTypes: get("announcementFormModule/virtualClassroomTypes"),
    timezones: get("announcementFormModule/timezones"),
    defaultEntryMargin: get(
      "announcementFormModule/fundaeConfiguration@default_entry_margin"
    ),
    defaultExitMargin: get(
      "announcementFormModule/fundaeConfiguration@default_exit_margin"
    ),
    isNotified: get("announcementFormModule/isNotified"),
    typeMoneys: get("announcementFormModule/typeMoneys"),
    typeSessions: get("announcementFormModule/typeSessions"),
    typeIdentifications: get("announcementFormModule/typeIdentifications"),
    mainIdentification: get("announcementFormModule/mainIdentification"),
    companies: get("announcementFormModule/companies"),
    modalities: get("announcementFormModule/modalities"),
    configAnnouncement: sync(
      "announcementFormModule/announcement@configAnnouncement"
    ),
    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit("input", newValue);
      },
    },
    numberOfSessions: {
      get() {
        return this.value.numberOfSessions;
      },
      set(newValue) {
        this.innerValue.numberOfSessions = newValue;
      },
    },
    sessions: {
      get() {
        return this.value.sessions;
      },
      set(newValue) {
        const inner = structuredClone(this.innerValue);
        inner.sessions = newValue;
        this.innerValue = inner;
      },
    },
    froalaConfig() {
      return {
        ...this.$store.getters["froalaEditorModule/getDefaultConfiguration"],
        height: 400,
        pluginsEnabled: ["align", "lists", "paragraphStyle", "paragraphFormat"],
      };
    },

    configAnnouncementWatch() {
      return Object.assign({}, this.configAnnouncement);
    },

    hasAnnouncementCost() {
      const costConfig = this.configurationsClientInThisPage?.find(
        (conf) => conf.type === "COST"
      );

      if (!costConfig) return false;

      const constConfigAnnoucement =
        this.configAnnouncementWatch?.[`configuration-` + costConfig.id];
      if (!constConfigAnnoucement) return false;

      return true;
    },

    configurationsClientInThisPage() {
      const configurations = [];

      configurations.push({
        type: "COST",
      });

      return this.fetchConfigurationByComponent(configurations);
    },

    typeMoney: {
      get() {
        return this.innerValue.typeMoney;
      },
      set(newValue) {
        this.innerValue.typeMoney = newValue;
      },
    },

    typeSession: {
      get() {
        return this.value.typeSessions;
      },
      set(newValue) {
        this.innerValue.typeSessions = newValue;
      },
    },

    costTotalInTheSession() {
      if (this.sessions?.length === 0) return 0;
      return this.sessions?.reduce(
        (acc, session) => acc + parseFloat(session.cost || 0),
        0
      );
    },
  },
  watch: {
    costTotalInTheSession(newValue) {
      this.innerValue.cost = newValue;
    },
    numberOfSessions: {
      handler: function (val, oldVal) {
        if (this.announcementType === COURSE_TYPE_VIRTUAL_CLASSROOM) return; // Avoid calling auto fill when is virtual classroom
        if (val instanceof String || typeof val === "string") return;

        let sessions = this.sessions;
        if (sessions.length === this.numberOfSessions) return;
        if (sessions.length > this.numberOfSessions) {
          sessions = sessions.slice(0, this.numberOfSessions);
        } else {
          const pending = this.numberOfSessions - sessions.length;
          for (let i = 0; i < pending; i++) {
            sessions.push({
              id: -1,
              startAt: "",
              finishAt: "",
              timezone: "",
              url: "",
              session_number: sessions.length + 1,
              entryMargin: this.defaultEntryMargin,
              exitMargin: this.defaultExitMargin,
              cost: 0,
              type: "PRESENTIAL",
              modality: "",
            });
          }
        }

        sessions = sessions.sort((a, b) => a - b);

        let number = 0;
        for (let i = 0; i < sessions.length; i++) {
          if (sessions[i].session_number >= 9999) {
            number++;
            sessions[i].session_number = number;
          } else {
            number = sessions[i].session_number;
          }
        }

        this.sessions = sessions;
      },
    },
  },
  methods: {
    setTutorData(data) {
      this.innerValue.tutor = data;
    },
    isSessionEditable(session) {
      if (!this.isNotified) return true;
      if (session.id == null || session.id < 0) return true;
      const currentDateTimeString = new Date().toLocaleDateString("es-ES", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        timeZoneName: "short",
        hour12: false,
        timeZone: session.timezone,
      });
      const finishAtTimestamp = Date.parse(
        `${session.finishAt}${session.offset}`
      );
      const currentTimestamp = Date.parse(currentDateTimeString);
      return currentTimestamp < finishAtTimestamp;
    },

    setSelected(index) {
      this.sessionSelectedIndex = index;
      const el = document.getElementById(
        `group-${this.innerValue.id}session-button`
      );
      if (el) el.click();
    },

    copyURL(index) {
      if (this.sessions[index].url?.length) {
        navigator.clipboard.writeText(this.sessions[index].url).then(() => {
          this.$toast.clear();
          this.$toast.success(`URL Copied: ${this.sessions[index].url}`);
        });
      }
    },

    virtualClassroom_addSession() {
      const sessions = structuredClone(this.sessions); // To allow the change to be emitted to upper components
      sessions.push({
        id: -1,
        startAt: "",
        finishAt: "",
        timezone: "",
        url: "",
        session_number: sessions.length + 1,
        providerId: "",
      });

      this.sessions = sessions;
    },

    virtualClassroom_removeSession(index) {
      /** Call endpoint to remove session **/
      const sessions = structuredClone(this.sessions);

      const session = sessions[index];
      if ("id" in session && session.id > 0) {
        this.$alertify.confirmWithTitle(
          this.$t("WARNING") + "",
          this.$t("ANNOUNCEMENT.FORM.GROUP.SESSION_DELETE_WARNING"),
          () => {
            this.$store
              .dispatch(
                "announcementFormModule/deleteSessionFromGroup",
                session.id
              )
              .then((res) => {
                const { error } = res;
                if (!error) {
                  sessions.splice(index, 1);
                  this.sessions = sessions;
                }
              });
          },
          () => {}
        );
      } else {
        sessions.splice(index, 1);
        this.sessions = sessions;
      }
    },

    async setSelectedProvider(value) {
      if (this.sessionSelectedIndex == null || this.sessionSelectedIndex < 0)
        return;
      const sessions = structuredClone(this.sessions);
      const session = sessions[this.sessionSelectedIndex];
      session.providerId = value;
      sessions[this.sessionSelectedIndex] = session;

      try {
        this.loadingProvider = true;
        const { error, data } = await this.$store.dispatch(
          "announcementFormModule/saveGroupSession",
          {
            groupId: this.innerValue.id,
            session,
          }
        );
        if (!error) {
          sessions[this.sessionSelectedIndex] = data;
          this.sessions = sessions;
          document
            .getElementById(`group-${this.innerValue.id}-meetings_close`)
            ?.click(); // Close modal
          this.sessionSelectedIndex = null;
        }
        else{
          this.$toast.error(Array.isArray(data) ?
            this.$t(data[0]) :
            this.$t(data));
        }
      } finally {
        this.loadingProvider = false;
      }
    },
  },
};
</script>

<template>
  <accordion-content
    :title="title"
    class="UserGroup"
    :id="tag"
    :badge="innerValue.data.length + ' personas'"
    :group-id="innerValue.id"
    icon="fa-users"
    :show-report-button="false"
    :opened="opened"
  >
    <div class="UserGroup--body">
      <div class="UserGroup--body--info">
        <div class="form-group col-xs-12 col-md-4">
          <label for=""
            >{{ $t("ANNOUNCEMENT.FORM.ENTITY.GROUP.GROUP_ID") }}
          </label>
          <input
            type="text"
            class="form-control"
            v-model="innerValue.id"
            disabled
          />
        </div>

        <div
          class="form-group col-xs-12 col-md-4"
          :id="`UserGroup-${innerValue.id}-code`"
        >
          <label for="">{{ $t("ANNOUNCEMENT.FORM.ENTITY.GROUP.CODE") }}</label>
          <input
            type="text"
            class="form-control"
            v-model="innerValue.code"
            :disabled="isNotified"
          />
        </div>

        <div
          class="form-group col-xs-12 col-md-4"
          :id="`UserGroup-${innerValue.id}-typeMoney`"
          v-if="hasAnnouncementCost"
        >
          <label>{{ $t("ANNOUNCEMENT.GROUP_SESSION_INFO.TYPE_MONEY") }} </label>
          <Multiselect
            v-model="innerValue.typeMoney"
            :options="typeMoneys"
            :searchable="true"
            :placeholder="$t('MULTISELECT.PLACEHOLDER')"
            :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
            track-by="fullName"
            label="fullName"
            selectLabel=""
            selectedLabel=""
            deselectLabel=""
            :disabled="isNotified"
          ></Multiselect>
        </div>

        <div
          class="form-group col-xs-12 col-md-4"
          :id="`UserGroup-${innerValue.id}-cost`"
          v-if="hasAnnouncementCost"
        >
          <label
            >{{ $t("ANNOUNCEMENT.GROUP_SESSION_INFO.COST") }}
            {{ typeMoney?.symbol }}
          </label>
          <input
            type="number"
            class="form-control"
            min="0"
            v-model.number="innerValue.cost"
            :disabled="isNotified || costTotalInTheSession > 0"
            step="0.01"
          />
        </div>
      </div>

      <tutor-component
          :group-id="innerValue.id"
          :tutor="innerValue.tutor"
          :tutors="tutors"
          @input="setTutorData"
      />
    </div>

    <div class="UserGroup--extra" v-if="announcementType !== COURSE_TYPE_ONLINE">
      <h1>
        <i class="fa fa-users"></i>
        {{ $t("ANNOUNCEMENT.FORM.GROUP.CONFIGURATION_FORMATION_ONSITE") }}
      </h1>
      <div class="col-12 d-flex flex-row flex-wrap mb-3">
        <div
          class="form-group col-xs-12 col-md-4 p-0 mr-1"
          v-if="announcementType !== COURSE_TYPE_VIRTUAL_CLASSROOM"
        >
          <label>{{ $t("ANNOUNCEMENT.FORM.GROUP.SESSION_NUMBER") }}</label>
          <input
            type="number"
            min="0"
            step="1"
            class="form-control"
            v-model.number="numberOfSessions"
          />
        </div>
      </div>

      <table class="table">
        <thead>
          <tr>
            <th>{{ $t("ANNOUNCEMENT.FORM.GROUP.SESSION") }}</th>
            <th v-if="announcementType === COURSE_TYPE_MIXED">
              {{ $t("ANNOUNCEMENT.GROUP_SESSION_INFO.TYPE_SESSION") }}
            </th>
            <th>{{ $t("START_AT") }}</th>
            <th>{{ $t("FINISH_AT") }}</th>
            <th>{{ $t("TIMEZONE") }}</th>
            <th v-if="hasAnnouncementCost">
              {{ $t("ANNOUNCEMENT.GROUP_SESSION_INFO.COST") }}
              {{ typeMoney?.symbol }}
            </th>
            <th
              v-if="
                announcementType === COURSE_TYPE_VIRTUAL_CLASSROOM ||
                announcementType === COURSE_TYPE_MIXED
              "
              class="text-center"
            >
              {{ $t("ANNOUNCEMENT.FORM.GROUP.LINK_SESSION") }}
            </th>
            <th
              v-if="
                announcementType === COURSE_TYPE_VIRTUAL_CLASSROOM ||
                announcementType === COURSE_TYPE_MIXED
              "
            ></th>
            <th colspan="2"></th>
          </tr>
        </thead>
        <tbody
          v-for="(session, index) in sessions"
          :key="index"
          :id="`UserGroup-${innerValue.id}-session-${index}`"
          class="SessionInfo"
        >
          <tr>
            <td>
              <span class="SessionInfo--title"
                >Sesión {{ session.session_number }}</span
              >
            </td>
            <td v-if="announcementType === COURSE_TYPE_MIXED">
              <Multiselect
                v-model="session.type"
                :options="typeSessions"
                :searchable="true"
                :placeholder="$t('MULTISELECT.PLACEHOLDER')"
                :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
                selectLabel=""
                selectedLabel=""
                deselectLabel=""
              ></Multiselect>
            </td>
            <td>
              <input
                type="datetime-local"
                class="form-control"
                v-model="session.startAt"
              />
            </td>
            <td>
              <input
                type="datetime-local"
                class="form-control"
                v-model="session.finishAt"
              />
            </td>
            <td>
              <Multiselect
                v-model="session.timezone"
                :options="timezones"
                :searchable="true"
                :placeholder="$t('MULTISELECT.PLACEHOLDER')"
                selectLabel=""
                selectedLabel=""
                deselectLabel=""
              ></Multiselect>
            </td>
            <td v-if="hasAnnouncementCost">
              <input
                type="number"
                class="form-control"
                v-model.number="session.cost"
                :disabled="isNotified"
                min="0"
                step="0.01"
              />
            </td>
            <td
              v-if="session.type === 'VIRTUAL'"
              class="d-flex justify-content-center"
            >
              <span
                type="url"
                class="form-control cursor-pointer mw-300"
                @click="copyURL(index)"
                ><i class="fa fa-link mr-2"></i>{{ session.url }}</span
              >
              <button
                class="btn btn-sm btn-primary px-3 ml-2"
                type="button"
                :id="`session-button-${innerValue.id}-${index}`"
                v-show="session.startAt?.length && session.finishAt?.length"
                @click="setSelected(index)"
              >
                <i class="fa fa-pencil"></i>
              </button>
              <button
                type="button"
                :id="`group-${innerValue.id}session-button`"
                data-bs-toggle="modal"
                :data-bs-target="`#group-${innerValue.id}-meetings`"
                style="display: none"
              ></button>
            </td>

            <td v-if="session.type === 'VIRTUAL'">
              <div class="w-100 d-flex justify-content-end">
                <button
                  type="button"
                  class="btn text-danger ml-1"
                  @click="virtualClassroom_removeSession(index)"
                >
                  <i class="fa fa-minus"></i>
                </button>
              </div>
            </td>
          </tr>
          <tr
            v-if="
              announcementType === COURSE_TYPE_ON_SITE || announcementType === COURSE_TYPE_MIXED ||
              announcementType === COURSE_TYPE_ON_SITE_EXTERN || announcementType === COURSE_TYPE_MIXED_EXTERN
            "
          >
            <td v-if="session.type === 'PRESENTIAL' && modalities && modalities.length > 0 ">
              <label>{{ $t("ANNOUNCEMENT.DEFAULT.MODALITY") }}</label>
              <Multiselect
                v-model="session.modality"
                :options="modalities"
                :allow-empty="false"
                required="required"
                track-by="id"
                label="name"
                value="id"
                :placeholder="$t('MULTISELECT.PLACEHOLDER')"
                :selectLabel="$t('MULTISELECT.SELECT_LABEL')"
              ></Multiselect>
            </td>
            <td v-else></td>
            <td v-if="session.type === 'PRESENTIAL'" colspan="2">
              <label class="fulltextLabel">{{ $t("ANNOUNCEMENT.FORM.GROUP.PLACE") }}</label>
              <input
                class="form-control"
                type="text"
                v-model="session.place"
                :readonly="!isSessionEditable(session)"
              />
            </td>
            <td v-if="session.type === 'PRESENTIAL'" colspan="2">
              <label class="fulltextLabel"
                >{{
                  $t("ANNOUNCEMENT.GROUP_SESSION_INFO.ENTRY_MARGIN")
                }}
                (%)</label
              >
              <input
                class="form-control"
                type="number"
                min="0"
                max="100"
                v-model.number="session.entryMargin"
                v-min-number:0
                v-max-number:50
                :readonly="!isSessionEditable(session)"
              />
            </td>
            <td v-if="session.type === 'PRESENTIAL'" colspan="2">
              <label
                >{{
                  $t("ANNOUNCEMENT.GROUP_SESSION_INFO.EXIT_MARGIN")
                }}
                (%)</label
              >
              <input
                class="form-control"
                type="number"
                min="0"
                max="50"
                v-min-number:0
                v-max-number:50
                v-model.number="session.exitMargin"
                :readonly="!isSessionEditable(session)"
              />
            </td>
          </tr>
        </tbody>
        <tbody>
          <tr
            v-if="
              announcementType === COURSE_TYPE_VIRTUAL_CLASSROOM ||
              announcementType === COURSE_TYPE_MIXED ||
              announcementType === COURSE_TYPE_VIRTUAL_CLASSROOM_EXTERN ||
              announcementType === COURSE_TYPE_MIXED_EXTERN
            "
          >
            <td colspan="6"></td>
            <td>
              <div class="w-100 d-flex">
                <button
                  type="button"
                  class="btn btn-primary ml-auto"
                  @click="virtualClassroom_addSession()"
                >
                  <i class="fa fa-plus"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <ModalMeetings
      :tag="`group-${innerValue.id}-`"
      :providers="virtualClassroomTypes"
      :loading="loadingProvider"
      @input="setSelectedProvider"
    />
  </accordion-content>
</template>

<style scoped lang="scss">
.UserGroup {
  width: 100%;
  display: flex;
  flex-flow: column;

  &.warning {
    .UserGroup--header {
      background-color: $color-secondary-light;

      & > * {
        color: #ffffff;
      }
    }
  }

  &--header,
  &--extra {
    margin: 0 -1rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  &--body {
    width: 100%;
    display: flex;
    flex-flow: column;
    padding: 1rem;

    @media #{min-small-screen()} {
      padding: 3rem;
    }

    @media #{min-small-screen()} {
      display: grid;
      grid-template-columns: 1fr 320px;
      gap: 1rem;
      align-items: center;
      justify-content: center;
    }

    & > div {
      width: 100%;
    }

    &--info {
      display: flex;
      flex-wrap: wrap;
    }

    //&--tutor {
    //  display: flex;
    //  flex-flow: column;
    //  align-items: center;
    //  justify-content: center;
    //  border: 1px solid var(--color-neutral-mid);
    //  padding: 1rem;
    //  border-radius: 5px;
    //  position: relative;
    //
    //  .EditButton {
    //    position: absolute;
    //    top: 1rem;
    //    right: 1rem;
    //  }
    //
    //  h1 {
    //    font-size: 22px;
    //    color: var(--color-neutral-darkest);
    //    width: 100%;
    //    text-align: left;
    //  }
    //
    //  span {
    //    width: 100%;
    //    font-size: 20px;
    //    color: var(--color-neutral-darkest);
    //    font-weight: 500;
    //  }
    //
    //  .avatar {
    //    @include avatar;
    //    width: 100px !important;
    //    height: 100px !important;
    //    background-color: unset;
    //  }
    //
    //  .Tutor {
    //    display: grid;
    //    grid-template-columns: 105px 1fr;
    //    gap: 1rem;
    //    align-items: center;
    //
    //    & > div:not(.avatar) {
    //      display: flex;
    //      flex-flow: row wrap;
    //      row-gap: 0.5rem;
    //    }
    //  }
    //}
  }

  &--extra {
    background-color: #ebf9ff;
    padding-top: 1rem;
    padding-bottom: 1rem;

    h1 {
      font-size: 18px;
      color: var(--color-neutral-darkest);

      i {
        margin-right: 1rem;
      }
    }

    /* .table > :not(caption) > * > * {
      border-bottom: none !important;
    }
 */
    table {
      width: 100%;

      thead th,
      tbody th {
        color: var(--color-neutral-darkest);
        font-weight: bold;
      }

      tbody tr {
        border-bottom: 1px solid var(--color-neutral-light);
      }
      > :not(caption) {
        border-bottom: none;
      }
    }

    input[type="url"] {
      color: var(--color-primary);
    }
  }

  .TutorSelection {
    padding: 1rem;

    @media #{min-small-screen()} {
      padding: 1rem 5rem;
    }

    .form-group {
      padding: 0.15rem 0;
    }

    .avatar {
      @include avatar;
      width: 100px;
      height: 100px;
      margin-bottom: 2rem;
    }
  }

  .TutorProfileModal--container {
    :deep(.modal-body) {
      padding: 0 !important;
    }

    :deep(.modal-dialog) {
      max-width: 720px;
    }
  }

  .TutorProfileModal {
    padding: 2rem 3rem;
    background-color: var(--color-neutral-mid);

    &--header {
      display: flex;
      flex-flow: column;
      align-items: flex-start;
      justify-content: center;

      h1,
      h4 {
        color: var(--color-neutral-darkest);
        width: 100%;
        text-align: center;
        margin-top: 1rem;
      }

      h1 {
        font-size: 22px;
      }

      h4 {
        font-size: 18px;
        margin-top: unset;
      }
    }

    &--profile {
      border: 1px solid var(--color-neutral-mid-darker);
      border-radius: 10px;
      padding: 1rem 3rem;
      background-color: #ffffff;
      margin-top: 1rem;

      table {
        width: 100%;

        tr {
          padding-top: 1.25rem;
        }

        th {
          color: var(--color-neutral-darkest);
          font-weight: bold;
        }
      }
    }
  }

  .fa.fa-tutor {
    background-image: url('data:image/svg+xml,<svg id="ICONOS" xmlns="http://www.w3.org/2000/svg" width="66.423" height="54.343" viewBox="0 0 66.423 54.343"><g id="persona_contacto" transform="translate(0 0)"><path id="Trazado_13809" data-name="Trazado 13809" d="M30.264,32.206a11.6,11.6,0,0,1-8.459-3.9,15.079,15.079,0,0,1-3.926-9.213,12.617,12.617,0,0,1,3.192-9.629,12.328,12.328,0,0,1,9.193-3.836,12.337,12.337,0,0,1,9.167,3.853,12.587,12.587,0,0,1,3.211,9.61A15.119,15.119,0,0,1,38.716,28.3,11.567,11.567,0,0,1,30.264,32.206ZM40.232,18.914Z" transform="translate(-8.244 -5.625)" fill="%230074a4"/><path id="Trazado_13810" data-name="Trazado 13810" d="M53.155,42H17.632a4.184,4.184,0,0,1-3.323-1.611,4.564,4.564,0,0,1-.795-3.894,19.908,19.908,0,0,1,9.109-12.245c4.163-2.578,7.312-4,12.77-4,5.115,0,7.736,1.148,12.305,3.662a3.435,3.435,0,0,1,.456.273A19.793,19.793,0,0,1,57.272,36.5a4.571,4.571,0,0,1-.8,3.894A4.18,4.18,0,0,1,53.155,42Z" transform="translate(-13.373 12.345)" fill="%230074a4"/><path id="Trazado_13820" data-name="Trazado 13820" d="M34.582,11.25,38.431,18.5l8.233,1-6.042,5.445L41.831,33l-7.249-4.53L27.333,33l1.209-8.055L22.5,19.5l8.459-1,3.626-7.249Z" transform="translate(19.758 7.416)" fill="%230074a4"/></g></svg>');
    height: 150px;
    width: 150px;
    background-repeat: no-repeat;
    background-size: 60%;
    background-color: #d9f0fa;
    border-radius: 50%;
    background-position: center center;
    margin: auto;
  }

  .mw-300 {
    max-width: 270px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .SessionInfo {
    &.warning {
      .SessionInfo--title {
        position: relative;

        &:after {
          content: "!";
          color: var(--color-tertiary);
          font-size: 18px;
          margin-left: 5px;
        }
      }
    }
  }
  .fulltextLabel {
    white-space: normal;
    display: inline-block;
    overflow: visible;
    word-wrap: break-word;
    word-break: break-word;
  }
}
</style>
