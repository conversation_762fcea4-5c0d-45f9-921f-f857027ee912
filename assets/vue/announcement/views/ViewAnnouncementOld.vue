<template>
  <div class="d-flex align-items-center justify-content-center" v-if="isLoading">
    <spinner />
  </div>
  <div class="ViewAnnouncement" v-else>
    <div class="ViewAnnouncement--info col-12">
      <div class="course--thumbnail">
        <picture>
          <img :src="announcement?.course?.image ? `/uploads/images/course/${ announcement.course.image }`
          : 'assets/chapters/default-image.svg'" alt=""
          onerror="this.onerror=null;this.src='assets/chapters/default-image.svg';"
          >
        </picture>
      </div>
      <div class="announcement--info">
        <h1 class="course-info">{{ announcement?.course?.code }} - {{ announcement?.course?.name }}</h1>
        <div class="course-description" v-html="announcement?.course?.description ?? ''"/>
        <h1 class="created-at">{{ announcement?.createdAtText }}</h1>
        <div class="announcement--dates">
          <div class="start-at">
            <h1>{{ $t('START_AT') }}</h1>
            <label>{{ announcement?.startAt }}</label>
          </div>
          <div class="finish-at">
            <h1>{{ $t('FINISH_AT') }}</h1>
            <label>{{ announcement?.finishAt }}</label>
          </div>
        </div>
      </div>
    </div>
    <div class="ViewAnnouncement--content">
      <ul class="nav nav-tabs">
        <li class="nav-item" role="presentation">
          <button class="nav-link" :class="activePane === 'config' ? 'active' : ''" id="config-tab" @click="activePane = 'config'">
            <i class="fa fa-gear"></i> {{ $t('ANNOUNCEMENT.CONFIG') }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" :class="activePane === 'tutors' ? 'active' : ''" id="tutors-tab" @click="activePane = 'tutors'">
            <i class="fa fa-user-tie"></i> {{ $t('ANNOUNCEMENT.TUTORS') }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="materialsEnabled">
          <button class="nav-link" :class="activePane === 'materials' ? 'active' : ''" id="materials-tab" @click="activePane = 'materials'">
            <i class="fa fa-book"></i> {{ $t('ANNOUNCEMENT.MATERIAL') }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="tasksEnabled">
          <button class="nav-link" :class="activePane === 'tasks' ? 'active' : ''" id="tasks-tab" @click="activePane = 'tasks'">
            <i class="fa fa-tasks"></i> {{ $t('ANNOUNCEMENT.TASKS') }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" :class="activePane === 'forum' ? 'active' : ''" id="forum-tab" @click="activePane = 'forum'">
            <i class="fa fa-comments"></i> {{ $t('ANNOUNCEMENT.FORUM') }}
          </button>
        </li>
        <li class="nav-item" role="presentation" v-if="announcement?.subsidized">
          <button class="nav-link" :class="activePane === 'opinions' ? 'active' : ''" id="opinions-tab" @click="activePane = 'opinions'">
            <i class="fa fa-star"></i> {{ $t('ANNOUNCEMENT.OPINIONS') }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" :class="activePane === 'observation' ? 'active' : ''" id="observation-tab" @click="activePane = 'observation'">
            <i class="fa fa-comment"></i> {{ $t('ANNOUNCEMENT.OBSERVATION') }}
          </button>
        </li>
      </ul>

      <div class="tab-content">
        <div class="tab-pane fade config" :class="activePane === 'config' ? 'active show' : ''">
          <announcement-config :id="$route.params.id" :max-users="announcement?.maxUsers ?? 0"></announcement-config>
        </div>

        <div class="tab-pane fade tutors" :class="activePane === 'tutors' ? 'active show' : ''">
          <announcement-tutors />
        </div>

        <div class="tab-pane fade materials" :class="activePane === 'materials' ? 'active show' : ''" v-if="materialsEnabled">
          <announcement-materials :id="$route.params.id" :announcement="announcement"></announcement-materials>
        </div>

        <div class="tab-pane fade tasks" :class="activePane === 'tasks' ? 'active show' : ''" v-if="tasksEnabled">
          <announcement-tasks :announcement-id="$route.params.id" :announcement="announcement"></announcement-tasks>
        </div>

        <div class="tab-pane fade forum" :class="activePane === 'forum' ? 'active show' : ''">
          <announcement-forum :announcement-id="$route.params.id"></announcement-forum>
        </div>

        <div class="tab-pane fade opinions" :class="activePane === 'opinions' ? 'active show' : ''">
          <announcement-opinions :announcement-id="$route.params.id" />
        </div>

        <div class="tab-pane fade observation" :class="activePane === 'observation' ? 'active show' : ''">
          <observations :id="$route.params.id"></observations>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {call, get} from "vuex-pathify";

import AnnouncementConfig    from "../components/AnnouncementConfig.vue";
import AnnouncementForum     from "./Details/AnnouncementForum.vue";
import AnnouncementMaterials from "./Details/AnnouncementMaterials.vue";
import AnnouncementOpinions  from "./Details/AnnouncementOpinions.vue";
import AnnouncementTasks     from "./Details/AnnouncementTasks.vue";
import AnnouncementTutors   from "./ViewTabs/AnnouncementTutors.vue";
import Observations from "../components/Observations.vue";
import Spinner from "../../admin/components/base/Spinner.vue";

export default {
  name: "ViewAnnouncement",
  components: {
    AnnouncementConfig,
    AnnouncementForum,
    AnnouncementMaterials,
    AnnouncementOpinions,
    AnnouncementTasks,
    AnnouncementTutors,
    Observations,
    Spinner,
  },

  data() {
    return {
      activePane: 'config',
      // announcement: undefined,
      startAt: undefined,
      finishAt: undefined,

      loadingTutors: true,
      tutors: [],

      loadingTasks: true,
      tasks: []
    }
  },

  computed: {
    announcement: get('announcementModule/announcement'),
    isLoading: get('announcementModule/isLoading'),
    useGlobalEventBus: get('contentTitleModule/getUseGlobalEventBus'),
    refresh: get('announcementModule/refresh'),
    materialsEnabled: get('configModule/config@materialsEnabled'),
    tasksEnabled: get('configModule/config@tasksEnabled'),
  },

  watch: {
    announcement: {
      handler: function (val, oldVal) {
        this.initView();
      },
      deep: true
    },
    refresh: {
      handler: function (val, oldVal) {
        this.handleRefresh();
      },
      deep: true,
      immediate: true
    }
  },

  created() {
    if (!this.announcement || this.announcement.id !== this.$route.params.id || this.refresh.action === 'announcement') {
      // Load only when required, to avoid multiple calls
      this.loadAnnouncement();
    }
  },

  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('onDeleteAnnouncement', e => {
        console.log('delete');
      });
      this.$eventBus.$on('onEditAnnouncement', e => {
        this.$router.push({ name: 'UpdateAnnouncement', params: { id: this.$route.params.id } });
      });
      this.$eventBus.$on('onReportAnnouncement', e => {
        this.downloadReport();
      });
    }
  },

  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('onDeleteAnnouncement');
    }
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('onEditAnnouncement');
    }
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('onReportAnnouncement');
    }
  },
  methods: {
    initView() {
      this.$store.dispatch('contentTitleModule/addRoute', {
        routeName: this.$route.name,
        params: {
          linkName: this.announcement.course.name,
          params: this.$route.params
        }
      });

      this.$store.dispatch('contentTitleModule/setActions', { route: this.$route.name, actions: [
          {
            name: this.$t('DELETE'),
            event: 'onDeleteAnnouncement',
            class: 'btn btn-danger'
          },
          {
            name: this.$t('EDIT'),
            event: 'onEditAnnouncement',
            class: 'btn btn-primary'
          },
          {
            name: this.$t('REPORT'),
            event: 'onReportAnnouncement',
            class: 'btn btn-success'
          },
        ]});

      this.loadAnnouncementDetails();
    },

    async handleRefresh() {
      if (!this.refresh.refresh) return;

      if (this.refresh.action === null && this.refresh.refresh) {
        await this.loadAnnouncement();
        this.initView();
      }
      else {
        switch (this.refresh.action) {
          case 'users':
            await this.$store.dispatch('announcementModule/loadAnnouncementCalledUsers', this.$route.params.id);
            break;
          case 'tutors':
            await this.$store.dispatch('announcementModule/loadAnnouncementTutors', this.$route.params.id);
            break;
          case 'materials':
            await this.$store.dispatch('materialCourseModule/getAnnouncementMaterials', this.$route.params.id);
            break;
          case 'tasks':
            await this.$store.dispatch('announcementModule/loadAnnouncementTasks', this.$route.params.id);
            break;
        }
      }
    },

    async loadAnnouncementDetails() {
      await this.$store.dispatch('announcementModule/loadAnnouncementCalledUsers', this.$route.params.id);
      await this.$store.dispatch('announcementModule/loadAnnouncementTutors', this.$route.params.id);
      if (this.materialsEnabled) await this.$store.dispatch('materialCourseModule/getAnnouncementMaterials', this.$route.params.id);
      if (this.tasksEnabled) await this.$store.dispatch('announcementModule/loadAnnouncementTasks', this.$route.params.id);
    },

    deleteAnnouncement() {
      this.$alertify.confirmWithTitle(
          this.$t('ANNOUNCEMENT.DELETE.CONFIRM.TITLE'),
          this.$t('ANNOUNCEMENT.DELETE.CONFIRM.DESCRIPTION'),
          () => {
            this.$store.dispatch('announcementModule/deleteAnnouncement', this.$route.params.id).then(res => {
              const { error } = res;
              if (error) this.$toast.error(this.$t('ANNOUNCEMENT.DELETE.FAILED') + '')
              else {
                this.$toast.error(this.$t('ANNOUNCEMENT.DELETE.SUCCESS') + '')
                if (this.useGlobalEventBus) {
                  this.$eventBus.$emit('go-back');
                }
              }
            })
          },
          () => {},
      );
    },

   async downloadReport() {
     const pdf = await this.$store.dispatch('announcementModule/downloadAnnouncementReport', this.$route.params.id);

      const link = document.createElement('a');
      link.href = pdf;
      link.download = `convocatoria.pdf`;
      link.click();
    },

    async loadAnnouncement() {
      if (this.$route.params.origin !== undefined) {
        this.$store.dispatch('contentTitleModule/addRoute', {
          routeName: 'Home',
          params: {
            linkName: this.$t('ANNOUNCEMENTS'),
            params: {}
          }
        });
      }

      await this.$store.dispatch('announcementModule/loadAnnouncement', this.$route.params.id);
    }
  }
}
</script>

 <style scoped lang="scss"> 
.ViewAnnouncement {
  &--info {
      display: grid;
      grid-template-columns: 320px auto;

    .course--thumbnail {
      width: 100%;
      padding: 0.5rem;
      picture, img {
        width: 100%;
      }
    }

    .announcement--info {
      display: flex;
      flex-flow: column;
      padding: 1rem 1rem 0 1rem;
     /*  background-color: #FFFFFF;
      border: 1px solid #CBD5E1; */

      .course-info {
        font-size: 20px;
        width: 100%;
      }

      .course-description {
        font-size: 16px;
        width: 100%;
      }

      .announcement--dates {
        display: flex;
        flex-flow: row nowrap;
        align-items: flex-end;
        margin-top: auto;

        .finish-at { margin-left: 0.5rem }

        h1 {
          font-size: 12px;
          margin: 0;
          padding: 0;
        }
        label {
          font-size: 14px;
          color: #707070;
          margin: 0;
          padding: 0;
        }
      }
      .created-at {
        font-size: 14px;
        color: #707070;
      }
    }
  }

  &--content {
    @include nav-bar-style;
    .tab-pane.forum {
      padding: 0;
    }
  }

  .ViewAnnouncement--content{
    margin-top: 2rem;
  }
}
</style>
