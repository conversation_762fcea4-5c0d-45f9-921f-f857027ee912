<template>
  <div id="printContent" class="AnnouncementInfo">
    <div class="text-right mt-2 hideOnPrint">
      <button class="btn btn-sm btn-primary" type="button" @click="printData">
        <i class="fa fa-download"></i> {{ $t('ANNOUNCEMENT.INFOTAB.DOWNLOAD') }}
      </button>
    </div>
    <div class="content mx-3">
      <div class="row mt-3">
        <div class="col-md-3">
          <object :data="imgSrc" class="w-100"><img :src="defaultImage" class="border w-100" alt="Banner Image"></object>
        </div>
        <div class="col-md-9 d-flex flex-column justify-content-between">
          <div>
            <h4 class="font-weight-bold mb-4"> {{ $t('ANNOUNCEMENT.INFOTAB.COURSE_DESC', [course.name]) }}</h4>
            <div class="course-description" v-html="course.description"/>
            <span class="text-detail mt-4">{{ announcement.createdAtText }}</span>
          </div>
          <div class="badges d-flex flex-wrap mt-3">
            <span class="badge bg-info text-white"><i :class="course.icon" class="mr-2"></i>{{ course.type }}</span>
            <span class="badge bg-info text-white" v-if="announcement.hasBonification">
              <i class="fa fa-coins mr-2"></i>{{ $t('ANNOUNCEMENT.INFOTAB.BONUS') }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <fragment-content
        tag="convocatoria"
        icon="fa-bullhorn"
        :title="$t('ANNOUNCEMENT.FORM.STEPS.ANNOUNCEMENT') || ''"
        class="mt-3"
        :details="basicInfoDetails"
    >
      <div class="row mt-3">
        <p class="font-weight-bold mb-2">{{ $t('ANNOUNCEMENT.FORM.ENTITY.OBJECTIVE_AND_CONTENT') }}:</p>
        <DataNotFound :hide-on="!(announcement.objectives || '').length" align="left"/>
        <div v-html="announcement.objectives" class="course-description mb-2"></div>
        <p v-if="announcement.type !== 'on_site'" class="font-weight-bold mb-2">{{ $t('ANNOUNCEMENT.INFOTAB.TEMPORIZATION') }}</p>
        <DataNotFound v-if="announcement.type !== 'on_site'" :hide-on="!(announcement.temporization).length" align="left"/>
        <div class="chapterContainer">
          <chapter-details
              v-for="(chapter, index) in (announcement.temporization)"
              :title="chapter.name"
              :date-from="getDate(chapter.startAt)"
              :date-to="getDate(chapter.finishAt)"
          />
        </div>
      </div>
    </fragment-content>
    <div id="criteria" class="bg-blue" v-show="criteria.length && announcement.course.typeID !== 2 ">
      <fragment-content
          tag="criteria"
          icon=""
          :title="$t('ANNOUNCEMENT.FORM.ENTITY.APPROVED_CRITERIA') || ''"
          :show-separator="false"
          :details="criteria"
      />
    </div>
    <div id="extra-details" v-if="extraDetails.length > 0">
      <fragment-content
        tag="extra-details"
        icon="fa-info-circle"
        :title="$t('ANNOUNCEMENT.EXTRA.TITLE')"
        :details="extraDetails"
      />
    </div>
    <fragment-content
        tag="bonus"
        icon="fa-coins"
        :title="$t('ANNOUNCEMENT.FORM.STEPS.SUBSIDIZED') || ''"
        class="mt-3"
        :show-alert="true"
        v-if="announcement.hasBonification"
    >
      <div class="row mt-4">
        <div class="col-md-4 col-sm-12 mt-3 align-self-center">
          <short-answer-tag
              v-for="(detail, index) in bonusData"
              :key="'bonus' + index"
              :question="detail.question + ':'"
              :answer="detail.answer"/>
        </div>
        <div class="col-md-4 col-sm-12 mt-3 text-center">
          <div class="d-flex justify-content-center border flex-column w-100 h-100">
            <span class="icon userAvatar"></span>
            <p class="mb-0">{{ $t('CONTACT_PERSON') }}</p>
            <p class="font-weight-bold mb-3">{{ contact.name }} </p>
            <p class="mb-0">{{ $t('ANNOUNCEMENT.TRAINING_CENTER_EMAIL') }}</p>
            <p class="font-weight-bold mb-3">{{ contact.email }}</p>
            <p class="mb-0">{{ $t('ANNOUNCEMENT.TRAINING_CENTER_PHONE') }}</p>
            <p class="font-weight-bold mb-3">{{ contact.tel }}</p>
          </div>
        </div>
        <div class="col-md-4 col-sm-12 mt-3">
          <div class="bg-info p-5">
            <h4 class="font-weight-bold text-primary text-center mb-5">
              {{ $t('ANNOUNCEMENT.FORM.ACCESS.INFO') }}
            </h4>
            <copy-input
                :label="$t('ANNOUNCEMENT.FORM.ACCESS.URL') || ''"
                :input="fundaeAccess.url"
            />
            <copy-input
                :label="$t('ANNOUNCEMENT.FORM.ACCESS.USER') || ''"
                :input="fundaeAccess.user"
            />
            <copy-input
                :label="$t('ANNOUNCEMENT.FORM.ACCESS.PASSWORD') || ''"
                :input="fundaeAccess.pass"
                :maskInput="true"
            />
          </div>
        </div>
      </div>
    </fragment-content>

    <fragment-content
        tag="students"
        :title="$t('ANNOUNCEMENT.FORM.STEPS.STUDENTS') || ''"
        class="oversize mt-3"
    >
      <div class="w-100 d-flex align-items-center justify-content-center flex-column" v-if="loadingUsers">
        <spinner />
      </div>
      <div class="row">
        <accordion-content
            v-for="(group, index) in groupStudents"
            icon="fa-users"
            :title="group.name || ''"
            :badge="`${$t('ANNOUNCEMENT.PEOPLE')}: ${group.total}`"
            :opened="!index">
          <students-table
              :student-list="group.users || []"
              class="col-12 pb-3"
              :show-details="false"
              :tag="'group' + (index + 1) + '-students'"/>

          <div class="w-100 AnnouncementInfo--students--assistance" v-if="getAssistanceSessions(group.groupInfo?.id).length > 0">
            <assistance-table
                :session-list="getAssistanceSessions(group.groupInfo?.id)"
                :min-view="true"
                class="col-12 pb-3"
                :group-id="group.groupInfo?.id"
                :tag="'group' + (index + 1) + 'info-assistance'"/>
          </div>
        </accordion-content>
      </div>
      <div>
        <DataNotFound :hide-on="!loadingUsers && !groupStudents.length"
                      :text="$t('ANNOUNCEMENT.STUDENTTAB.NOT_FOUND') || ''"
                      icon="fa-users"
                      :banner="true" />
      </div>
    </fragment-content>

    <fragment-content
      v-if="announcement.hasComunication"
        tag="comunication"
        icon="fa-envelope"
        :title="$t('ANNOUNCEMENT.FORM.STEPS.COMMUNICATION') || ''"
        class="mt-3"
        :details="[
            {question: $t('ANNOUNCEMENT.INFOTAB.CHAT'), answer: boolToYesOrNot(announcement.comunications.CHAT) },
            {question: $t('ANNOUNCEMENT.NOTIFICATIONS'), answer: boolToYesOrNot(announcement.comunications.NOTIFICATION) },
            {question: $t('ANNOUNCEMENT.INFOTAB.MESSAGES'), answer: boolToYesOrNot(announcement.comunications.MESSAGE) },
            {question: $t('ANNOUNCEMENT.FORUM'), answer: boolToYesOrNot(announcement.comunications.FORUM) },
        ]"
    />

    <fragment-content
        tag="certifications"
        :title="$t('ANNOUNCEMENT.FORM.STEPS.CERTIFICATE') || ''"
        icon="fa-graduation-cap"
        class="mt-3"
        :details="certificateDetails"
    >
      <div class="row mt-4" v-if="announcement.diploma.active">
        <div class="col-12">
          <p>{{ $t('ANNOUNCEMENT.INFOTAB.CERTIFICATE_PREVIEW') }}:</p>
          <iframe class="preview" :src="diplomaPreview"></iframe>
        </div>
      </div>
    </fragment-content>

  <!--   <fragment-content
        tag="survey"
        :title="$t('ANNOUNCEMENT.INFOTAB.SURVEY_TITLE') || ''"
        icon="fa-clipboard-check"
        class="my-3"
        :details="surveyDetails"
    >
      <div class="row mt-4" v-if="announcement.survey.sastifaction">
        <div class="col-12">
          <p>{{ $t('ANNOUNCEMENT.INFOTAB.SURVEY_PREVIEW') }}:</p>
          <object class="preview" :data="announcement.survey.preview"></object>
        </div>
      </div>
    </fragment-content> -->

    <fragment-content
        tag="alerts"
        :title="$t('ANNOUNCEMENT.FORM.STEPS.ALERTS') || ''"
        icon="fa-exclamation-circle"
        class="mt-3"
        :details="[{question: $t('ANNOUNCEMENT.INFOTAB.TUTOR_ALERTS'), answer: boolToYesOrNot(alerts.length)}]"
    >
      <div class="row mt-4">
        <p
            v-for="(alert, index) in alerts"
            :key="'alerts' + index"
            class="col-sm-6 col-xs-12 px-5 mt-3 mb-0"> {{ alert }}
        </p>
      </div>
    </fragment-content>
  </div>
</template>

<script>
import FragmentContent  from "../../components/details/fragmentContent";
import ChapterDetails   from "../../components/details/chapterDetails";
import ShortAnswerTag   from "../../components/details/shortAnswerTag";
import CopyInput        from "../../components/details/copyInput";
import StudentsTable    from "../../components/details/studentsTable";
import AccordionContent from "../../components/details/accordionContent";
import {get}            from "vuex-pathify";
import {UtilsMixin}     from "../../mixins/utilsMixin";
import DataNotFound     from "../../components/details/DataNotFound";
import dateTimeFormatterMixin from "../../../common/mixins/dateTimeFormatterMixin";
import Spinner from "../../../admin/components/base/Spinner.vue";
import {COURSE_TYPE_ONLINE} from "../../../course/mixins/constants";
import AssistanceTable from "../../components/details/assistanceTable.vue";

export default {
  name: "AnnouncementInfo",
  mixins: [UtilsMixin, dateTimeFormatterMixin],
  components: {
    AssistanceTable,
    Spinner,
    DataNotFound, AccordionContent, StudentsTable, CopyInput, ShortAnswerTag, ChapterDetails, FragmentContent},
  data() {
    return {
      defaultImage: 'assets/chapters/default-image.svg',
    }
  },
  computed: {
    announcement: get('announcementModule/announcement'),
    groupStudents: get('announcementModule/calledUsers'),
    assistanceUsers: get('announcementModule/assistanceUsers'),
    loadingUsers: get('announcementModule/loadingCalledUsers'),
    course() {
      return this.announcement.course || {}
    },
    imgSrc() {
      return this.course.image ? this.course.thumbnailUrl : this.defaultImage;
    },
    criteria() {
      return (this.announcement.aprovalCriteria || [])
          .map((criteria) => ({
            question: criteria.name,
            answer: criteria.value + (criteria.criteriaTypeKey === 3 ? ' min.' : '%')
          }));
    },
    alerts() {
      return (this.announcement.alertsTutor || []).map((alert) => alert.name);
    },
    fundaeAccess() {
      const { urlAccess, user, password } = this.announcement.inspector;
      return {
        url: urlAccess,
        user: user,
        pass: password
      }
    },
    bonusData() {
      const { bonification } = this.announcement;
      return [
        {question: this.$t('ANNOUNCEMENT.INFOTAB.BONUS'), answer: this.boolToYesOrNot(this.announcement.hasBonification) },
        {question: this.$t('ANNOUNCEMENT.FORM.ENTITY.BONIFICATION_TYPE'), answer: this.$t('ANNOUNCEMENT.FORM.ENTITY.FUNDAE_CONFIGURATION.' + bonification.typeAction) + '' },
        {question: this.$t('ANNOUNCEMENT.FORM.ENTITY.ACTION_CODE'), answer: bonification.codeAction },
        {question: this.$t('ANNOUNCEMENT.DEFAULT.MODALITY'), answer: bonification.modalidad },
        {question: this.$t('ANNOUNCEMENT.FORM.ENTITY.DENOMINATION'), answer: bonification.denomination },
        {question: this.$t('ANNOUNCEMENT.FORM.ENTITY.MAX_GROUP_SIZE'), answer: this.announcement.usersPerGroup },
      ]
    },
    contact() {
      const { bonification } = this.announcement;
      return {
        name: bonification.contactPerson || '--',
        email: bonification.contactPersonEmail || '--',
        tel: bonification.contactPersonTelephone || '--',
      }
    },

    diplomaPreview() {
      const { diploma } = this.announcement;
      return diploma.preview ? diploma.preview + '#toolbar=0&navpanes=0&scrollbar=0' : '';
    },

    basicInfoDetails() {
      let details = [
        {question: this.$t('ANNOUNCEMENT.FORM.ENTITY.START_AT'), answer: this.getDateTimeFormatted(this.announcement.startAt, 'es-ES', { timezone: this.announcement.timezone, hour12: false })},
        {question: this.$t('ANNOUNCEMENT.FORM.ENTITY.DIDACTIC_GUIDE'), answer: this.announcement.guideTitle || '--', href: this.announcement.guideURL},
      ];

      if (this.announcement.hasBonification) {
        details.push({question: this.$t('ANNOUNCEMENT.FORM.ENTITY.MAX_GROUP_SIZE'), answer: this.announcement.usersPerGroup || 0});
      }

      details = [...details, ...[
        {question: this.$t('ANNOUNCEMENT.FORM.ENTITY.FINISH_AT'), answer: this.getDateTimeFormatted(this.announcement.finishAt, 'es-ES', { timezone: this.announcement.timezone, hour12: false })},
        {question: this.$t('TIMEZONE'), answer: this.announcement.timezone},
        {question: this.$t('ANNOUNCEMENT.FORM.ENTITY.FORMATION_TIME'), answer: this.formatHoursToTime(this.announcement.totalHours || 0)},
      ]];

      if(this.announcement.type !== COURSE_TYPE_ONLINE) {
        details.push({
          question: this.$t('ANNOUNCEMENT.SESSIONS'),
          answer: this.announcement.numberOfSessions
        });
      }

      return details;
    },

    certificateDetails() {
      let details = [];
      details.push({question: this.$t('ANNOUNCEMENT.INFOTAB.CERTIFICATE_ACTIVE'), answer: this.boolToYesOrNot(this.announcement.diploma.active) });
      if (this.announcement.diploma.active)
      {
        details = [
            ...details,
          ...[
            {question: this.$t('ANNOUNCEMENT.INFOTAB.CERTIFICATE_CONFIRM'), answer: this.boolToYesOrNot(this.announcement.diploma.confirmation_obtaining) },
            {question: this.$t('ANNOUNCEMENT.INFOTAB.CERTIFICATE_SURVEY'), answer: this.boolToYesOrNot(this.announcement.diploma.complete_survey) }
          ]
        ];
      }
      return details;
    },

    surveyDetails() {
      let details = [{question: this.$t('ANNOUNCEMENT.INFOTAB.SURVEY_TITLE'), answer: this.boolToYesOrNot(this.announcement.survey.sastifaction) }];
      if (this.announcement.survey.sastifaction)
      {
        details = [
            ...details,
            ...[
              {question: this.$t('ANNOUNCEMENT.INFOTAB.SURVEY_TYPE'), answer: this.boolToYesOrNot(this.announcement.survey.typeSurvey) },
              {question: this.$t('ANNOUNCEMENT.INFOTAB.SURVEY_ON_COMPLETE'), answer: this.$t('YES') }
            ]
        ]
      }
      return details;
    },
    extra() {
      return this.announcement.extra || {};
    },
    extraDetails() {
      return Object.values(this.announcement.extra || {}).map(({ id, name, value }) => ({
        question: name,
        answer: value || '--',
      }));
    },
  },
  methods: {
    boolToYesOrNot(text) {
      return this.$t(text ? 'YES': 'NO');
    },
    printData() {
      window.print()
    },
    getAssistanceSessions(groupId) {
      const group = this.assistanceUsers.find(g => g.groupInfo?.id === groupId);
      return group?.sessions ?? [];
    }
  }
}
</script>

 <style scoped lang="scss"> 
.AnnouncementInfo {
  .badge {
    font-size: 1rem;
    font-weight: normal;
    padding: 0.5rem 1rem;
    white-space: nowrap;
    user-select: none;

    i {
      margin-right: 0.5rem;
    }
  }

  .chapterContainer {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-gap: 1rem;
  }

  .icon, .bg-blue {
    background-color: var(--color-primary-lighter);
  }

  #criteria {
    margin: 2rem -1.5rem;
    padding: 0.5rem 1rem;

    ::v-deep {
      .fragementHeader.mt-5 { margin-top: 0 !important; }
      .row.mb-4 { margin-bottom: 0 !important; }
    }

    @media (max-width: 576px) {
      margin: 2rem auto;
    }
  }

  .icon {
    width: 3rem;
    height: 3rem;
    border-radius: 100%;
    display: grid;
    align-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
  }

  .badges {
    gap: 0.5rem;
  }

  .badge + .badge {
    margin-left: 0;
  }

  object.preview, iframe.preview {
    background-color: #E5E5E5;
    width: clamp(250px, 100%, 400px);
    aspect-ratio: 1.4;
    border-radius: 5px;
  }

  .AnnouncementInfo--students--assistance {
    background-color: var(--color-primary-lightest);
  }
}
</style>
