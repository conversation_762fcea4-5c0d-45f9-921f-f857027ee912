<template>
  <div class="d-flex align-items-center justify-content-center" v-if="loading">
    <spinner />
  </div>
  <div class="AnnouncementUserDetails" v-else>
    <div class="p-1">
      <h3>{{ user.firstName }} {{ user.lastName }}</h3>
      <div class="row p-3">
        <div class="col-md-4 d-flex flex-column">
          <span><b>{{ $t('NOTIFIED') }}:</b> {{ notified }}</span>
          <span><b>{{ $t('STARTED') }}:</b> {{ userCourse.started }}</span>
          <span><b>{{ $t('FINISHED') }}:</b> {{ userCourse.finished }}</span>
          <span><b>{{ $t('ANNOUNCEMENT.TUTORS') }}</b></span>
          <div>
            <b v-for="(tutor, index) in tutors" :key="tutor.id">
              {{ tutor.name }}{{ (index + 1) === tutors.length ? '' : ',' }}
            </b>
          </div>
        </div>
        <div class="col-md-4 d-flex flex-column">
          <span><b>{{ $t('COURSE.TIME_SPENT') }}:</b> {{ getTimeSpentInCourse() }}</span>
          <span><b>{{ $t('CONTENTS_VIEWED') }}:</b> {{ userChapters.length / courseChapters.length * 100 }}% ({{ userChapters.length }}/{{ courseChapters.length }})</span>
          <span><b>{{ $t('TEACHER_INTERACTIONS') }}:</b> {{ messages.length }}</span>
        </div>
        <div class="col-md-4">
          <span><b>{{ $t('APT') }}:</b> <span :class="userCourse && userCourse.finished ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'"></span></span>
        </div>
      </div>
    </div>
    <div class="p-1 mt-3">
      <b>{{ $t('COURSE.CONTENT') }}</b>
      <table class="table chapter">
        <thead>
        <tr>
          <th></th>
          <th>{{ $t('CHAPTER') }}</th>
          <th>{{ $t('CONTENT_TYPE') }}</th>
          <th>{{ $t('STARTED') }}</th>
          <th>{{ $t('FINISHED') }}</th>
          <th>{{ $t('COURSE.TIME_SPENT') }}</th>
          <th>{{ $t('FINISHED') }}</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="chapter in courseChapters" :key="chapter.id">
          <td><img :src="`${chapterImagePath}/${chapter.image}`" alt="" @error="replaceImageByDefault"></td>
          <td>{{ chapter.title }}</td>
          <td>{{ $t(getTypeI18n(chapter.chapterType)) }}</td>
          <td class="text-nowrap">{{ userChapterInfo(chapter).startedAt }}</td>
          <td class="text-nowrap">{{ userChapterInfo(chapter).finishedAt }}</td>
          <td>{{ getFormatFromSeconds(userChapterInfo(chapter).timeSpent) }}</td>
          <td><i class="fa fa-check-circle" :class="isFinished(userChapterInfo(chapter)) ? 'finished' : ''"></i></td>
          <td>
            <a role="button" @click="showResults(userChapterInfo(chapter), chapter.typeId)" v-if="typeIncluded(chapter.typeId)"><i class="fa fa-eye"></i></a>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="TimeByDay p-1 mt-3">
      <b>{{ $t('TIME_SPENT_BY_DAY') }}</b>
      <table class="table">
        <thead>
        <tr>
          <th>{{ $t('DATE') }}</th>
          <th>{{ $t('COURSE.TIME_SPENT') }}</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(time, day) in timeByDay" :key="day">
          <td>{{ day }}</td>
          <td>{{ getFormatFromSeconds(time, true) }}</td>
        </tr>
        </tbody>
      </table>
    </div>

    <div class="modal fade" id="results-modal" tabindex="-1" aria-labelledby="results-modal-label" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="message-modal-label">{{ $t('RESULTS') }}</h5>
            <button type="button" class="close" data-dismiss="modal"
                    aria-label="{% trans %}Close{% endtrans %}">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <result-quiz :quiz="quiz" :number="index+1" v-for="(quiz, index) in results.quizzes" :key="'quiz'+index"/>
            <result-roulette :questions="results.roulette" v-show="results.roulette.length"></result-roulette>
            <result-puzzle :puzzle="puzzle" :number="index+1" v-for="(puzzle, index) in results.puzzles"
                            :key="'puzzle'+index"></result-puzzle>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary"
                    data-dismiss="modal">{{ $t('CLOSE') }}</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery';
import 'bootstrap';

import {get} from "vuex-pathify";
import Spinner from "../../admin/components/base/Spinner.vue";
import { formatFromSeconds } from "../../common/utils/i18nTime";
import ResultQuiz from "../../common/components/results/ResultQuiz.vue";
import ResultRoulette from "../../common/components/results/ResultRoulette.vue";
import ResultPuzzle from "../../common/components/results/ResultPuzzle.vue";

export default {
  name: "AnnouncementUserDetails",
  components: {ResultPuzzle, ResultRoulette, ResultQuiz, Spinner},
  $,
  data() {
    return {
      loading: true,
      user: {},
      userChapters: [],
      timeByDay: [],
      notified: null,
      userCourse: {},
      messages: [],
      resultsTypes: [],

      results: {
        quizzes: [],
        puzzles: [],
        roulette: [],
      }
    };
  },
  computed: {
    announcement: get('announcementModule/announcement'),
    tutors: get('announcementModule/announcementTutors'),
    courseChapters: get('announcementModule/courseChapters'),
    chapterImagePath: get('configModule/config@chapterImagePath'),
    defaultImageB64: get('configModule/defaultImageB64'),
    useGlobalEventBus: get('contentTitleModule/getUseGlobalEventBus'),
  },
  async created() {
    if (this.announcement === null) {
      await this.$store.dispatch('announcementModule/loadAnnouncement', this.$route.params.id);
      await this.$store.dispatch('announcementModule/loadAnnouncementTutors', this.$route.params.id);
      await this.$store.dispatch('announcementModule/refreshAction');
    }

    if (this.courseChapters.length === 0)
      await this.$store.dispatch('announcementModule/getAnnouncementCourseChapter', this.$route.params.id);
    this.userInfo();
  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('onUserReport', this.downloadUserReport);
    }
  },
  beforeDestroy() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$off('onUserReport');
    }
  },
  methods: {
    showResults(userCourseChapter, chapterTypeId) {
      this.results = {
        quizzes: [],
        puzzles: [],
        roulette: [],
      };
      this.$store.dispatch('announcementModule/getUserChapterResults', userCourseChapter.id).then(res => {
        switch (chapterTypeId) {
          case 5://Quiz
              this.results.quizzes = res.data.quizzes;
            break;
          case 3: //Roulette
              this.results.roulette = res.data.questions;
                break;
          case 6:// Puzzle
              this.results.puzzles = res.data.puzzles;
            break;
        }
        $('#results-modal').modal('show');
      })
    },
    typeIncluded(id) {
      return this.resultsTypes.includes(id);
    },
    getFormatFromSeconds(timeInSeconds, full = false) {
      return formatFromSeconds(timeInSeconds, this.$i18n, full);
    },
    getTimeSpentInCourse() {
      const totalTimeSpent = this.userChapters.reduce((accumulator, currentValue) => accumulator + currentValue.timeSpent, 0);
      return this.getFormatFromSeconds(totalTimeSpent, true);
    },
    replaceImageByDefault(event) {
      event.target.src = this.defaultImageB64;
    },

    userInfo() {
      this.$store.dispatch('announcementModule/getAnnouncementUserInfo', {id: this.$route.params.id, userId: this.$route.params.user}).then(res => {
        const { data, error } = res;
        this.userChapters = data.userChapters;
        this.timeByDay = data.timeByDay;
        this.user = data.user;
        this.notified = data.notified;
        this.userCourse = data.userCourse;
        this.messages = data.messages;
        this.resultsTypes = data.resultsTypes;

        this.$store.dispatch('contentTitleModule/setActions', { route: this.$route.name, actions: [
            {
              name: this.$t('REPORT'),
              event: 'onUserReport',
              class: 'btn btn-primary'
            }
          ]});
      }).finally(() => {
        this.loading = false;
      })
    },
    userChapterInfo(chapter) {
      const index = this.userChapters.findIndex(item => item.chapterId === chapter.id);
      if (index >= 0) return this.userChapters[index];
      return null;
    },
    isFinished(userChapter) {
      return userChapter.finishedAt != null && userChapter.finishedAt.length > 0
    },
    getTypeI18n(type) {
      return type.replaceAll(' ', '').toUpperCase();
    },

    async downloadUserReport() {
      const link = document.createElement('a');
      this.$store.dispatch('announcementModule/downloadAnnouncementUserReport', this.user.announcementUserId).then(res => {
        const href = URL.createObjectURL(res.data);
        link.href = href;
        link.download = `${this.user.firstName} ${this.user.lastName}-report.pdf`;
        link.click();
        URL.revokeObjectURL(href);
      });
    }
  }
}
</script>

 <style scoped lang="scss"> 
.AnnouncementUserDetails {
  .chapter {
    img {
      width: 40px;
      height: 40px;
    }
  }

  .fa-check-circle {
    color: red;
    &.finished {
      color: green;
    }
  }
}
</style>
