<template>
  <div class="ViewSurvey">
    <div class="ViewSurvey--content">
      <ul class="nav nav-tabs">
        <li class="nav-item" role="presentation">
          <button
              class="nav-link"
              :class="activePane === 'questions' ? 'active' : ''"
              id="questions-tab"
              @click="activePane = 'questions'"
          >
            <i class="fa fa-comments"></i> {{ $t("QUESTIONS") }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
              class="nav-link"
              :class="activePane === 'audience' ? 'active' : ''"
              id="audience-tab"
              @click="activePane = 'audience'"
          >
            <i class="fa fa-filter"></i> {{ $t("SURVEY.AUDIENCE") }}
          </button>
        </li>
      </ul>
      <div class="tab-content">
        <div class="tab-pane fade"
             :class="activePane === 'questions' ? 'active show' : ''"
             id="questions"
             role="tabpanel"
             aria-labelledby="questions-tab">
          <div class="d-flex align-items-center justify-content-center" v-if="loading">
            <spinner />
          </div>
          <questions v-model="questions" :realtime="true" :survey-id="$route.params.id" v-else/>
        </div>

        <div class="tab-pane fade"
             :class="activePane === 'audience' ? 'active show' : ''"
             id="audience"
             role="tabpanel"
             aria-labelledby="audience-tab">
          <survey-audience :id="$route.params.id"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Questions from "../components/Questions.vue";
import {get, sync} from "vuex-pathify";
import Spinner from "../../admin/components/base/Spinner.vue";
import SurveyAudience from "./SurveyAudience.vue";

export default {
  name: "ViewSurvey",
  components: {SurveyAudience, Spinner, Questions},
  data() {
    return {
      activePane: 'questions'
    };
  },
  computed: {
    survey: get('surveyModule/survey'),
    loading: get('surveyModule/loading'),
    questions: sync('surveyModule/surveyQuestions'),
    useGlobalEventBus: get('contentTitleModule/getUseGlobalEventBus'),
  },
  created() {
    this.$store.dispatch('contentTitleModule/setActions', { route: this.$route.name, actions: [
        {
          name: this.$t('DELETE'),
          event: 'deleteSurvey',
          class: 'btn btn-danger'
        },
        {
          name: this.$t('EDIT'),
          event: 'editSurvey',
          class: 'btn btn-primary'
        },
      ]});

    this.$store.dispatch('surveyModule/getSurvey', this.$route.params.id);
  },
  mounted() {
    if (this.useGlobalEventBus) {
      this.$eventBus.$on('deleteSurvey',  e => {
        this.$alertify.confirmWithTitle(
            this.$t('SURVEY.DELETE.CONFIRM.TITLE'),
            this.$t('SURVEY.DELETE.CONFIRM.DESCRIPTION'),
            () => {
              this.$store.dispatch('surveyModule/deleteSurvey', this.$route.params.id).then(res => {
                const { error } = res;
                if (error) this.$toast.error(this.$t('SURVEY.DELETE.FAILED') + '')
                else {
                  this.$toast.success(this.$t('SURVEY.DELETE.SUCCESS') + '');
                  this.$router.replace({ name: 'Home'});
                }
              })
            },
            () => {},
        );
      });
      this.$eventBus.$on('editSurvey', e => {
        this.$router.replace({name: 'UpdateSurvey', params: {id: this.$route.params.id}});
      });
    }
  },
  beforeDestroy() {
    this.$eventBus.$off('deleteSurvey');
    this.$eventBus.$off('editSurvey');
  }
}
</script>

 <style scoped lang="scss"> 
.ViewSurvey {
  &--content {
    @include nav-bar-style;
  }
}
</style>
