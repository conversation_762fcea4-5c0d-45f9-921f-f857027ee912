import {make} from "vuex-pathify";
import axios from "axios";

  
const DEFAULT_QUESTION_PROPERTIES = {
    id: -1,
    statement: '',
    translations: [],
    type: null,
    active: true,
    main: false,
    randomOrder: false,
    position:0,
    name:'',
    description:'',
    questions: [],
    warnings:[]
};

const state = {
    loading: true,
    surveys: [],
    pagination: {
        page: 1,
        totalItems: 0
    },

    survey: null,
    surveyQuestions: [],
    surveyCourses: [],
    surveyAnnouncements: [],
    translations:[],
    warnings:[],
    isSuperAdmin:false,
    userLocale: undefined,

    // Store available courses/announcements for survey assigment
    manualData: {
        courses: [],
        announcements: []
    },

    form: {
        id: -1,
        applyTo: 1,
        name: '',
        description: '',
        position:0,
        questions: [],
        courses: [],
        announcements: [],
        translations:[],
        warnings:[],
        survey: {
            id: -1,
            applyTo: 1,
            name: '',
            description: '',
            questions: [],
            courses: [],
            announcements: [],
            translations:[],
            warnings:[],
        }
    },

    indexQuestionForm: -1,
    questionForm: DEFAULT_QUESTION_PROPERTIES,
    defaultQuestionProperties: DEFAULT_QUESTION_PROPERTIES,
    questionsMain: [],
};

const getters = {
    ...make.getters(state),
    getTranslations: (state) => () => state.translations,
    getWarnings: (state) => () => state.warnings,
    getQuestionsMain: (state) => () => state.questionsMain,
    getIsSuperAdmin: (state) => () => state.isSuperAdmin,
    getUserLocale: (state) => () => state.userLocale, 
};

const mutations = {
    ...make.mutations(state),
    updateDefaultQuestions(state, values) {
        state.questionForm = values;
        state.defaultQuestionProperties = values;
    },

    addCurrentQuestion(state) {
        state.form.questions.push(state.questionForm);
        state.questionForm = structuredClone(state.defaultQuestionProperties);
    },

    updateCurrentQuestion(state) {
        state.form.questions[state.indexQuestionForm] = structuredClone(state.questionForm);
        state.questionForm = structuredClone(state.defaultQuestionProperties);
        state.indexQuestionForm = -1;
    },

    editQuestion(state, {index, question }) {
        state.questionForm = question;
        state.indexQuestionForm = index;
    },

    RESET_QUESTION_FORM(state) {
        state.questionForm = structuredClone(state.defaultQuestionProperties);
    },

    SET_QUESTIONS_MAIN(state, questions) {
        state.questionsMain = questions;
    }

};

const actions = {
    ...make.actions(state),

    async initForm({ commit, rootGetters }, id = -1) {
        commit('SET_LOADING', true);//loading
    
        let form = {
            id: -1,
            applyTo: 1,
            name: '',
            description: '',
            questions: [],
            courses: [],
            announcements: [],
            warnings: [],
            survey: {
                id: -1,
                applyTo: 1,
                name: '',
                description: '',
                questions: [],
                courses: [],
                announcements: [],
                warnings: [],
                translations: []
            }
        };

        if (id > 0) { 
            const result = await axios.get(`/admin/survey/${id}`);        
            commit('SET_FORM', {...result.data.data});
            commit('SET_TRANSLATIONS', result.data.data.survey.translations);
            commit('SET_WARNINGS', result.data.data.survey.warningLocales);           
            commit('SET_IS_SUPER_ADMIN', result.data.data.isSuperAdmin);
        } else {
            const result = await axios.get(`/admin/survey/initForm`); 
            commit('SET_FORM', form);
            commit('SET_TRANSLATIONS', result.data.data.translations);
            commit('SET_WARNINGS', result.data.data.warningLocales);
            commit('SET_QUESTIONS_MAIN', result.data.data.questionsMain);           
            commit('SET_IS_SUPER_ADMIN', result.data.data.isSuperAdmin);
        }

        commit('updateDefaultQuestions', {
            id: -1,
            statement: '',
            type: null,
            active: true,
            main: false,
            randomOrder: false,
            position:0,
            questions: [],
            warnings:[]
        });

        commit('SET_LOADING', false);
    },

    async saveNewSurvey({}, formData) {
        const headers = {
            'Content-Type': 'multipart/form-data'
        };

        const result = await axios.post('/admin/survey', formData, {
            headers
        });
        return result.data;
    },

    async updateSurvey({}, { id, formData}) {
        // const headers = {
        //     'Content-Type': 'multipart/form-data'
        // };

        // const result = await axios.post(`/admin/survey/${id}/update`, formData, {
        //     headers
        // });
        // return result.data;
    },

    async getSurveys({ commit, getters }) {
        commit('SET_LOADING', true);
        try {
            const { page } = getters['pagination'];
            const result = await axios.get(`/admin/surveys/${page}`)
            const { data, error } = result.data;
 
            commit('SET_SURVEYS', data.items); 
            commit('SET_IS_SUPER_ADMIN', data.isSuperAdmin);
            
            commit('SET_PAGINATION', {
                page: page,
                totalItems: data['total-items'],
            });
        } finally {
            commit('SET_LOADING', false);
        }
    },

    async activateSurvey({}, { id, active }) {
        const result = await axios.patch(`/admin/survey/${id}/activate`, { active });
        return result.data;
    },

    async deleteSurvey({dispatch,  commit, getters }, id) {  
        try {
            const result = await axios.delete(`/admin/survey/${id}`)
            
            const { error } = result.data;
            if (!error){
                commit('SET_PAGINATION', {  page: 1 });
                dispatch('getSurveys');        
            }
            return result.data;
        } catch (e) {
            if(e.response.data.status == 409) return Promise.reject(e.response.data.status);
            else return Promise.reject("Failed to make request");
        }
    },

    async cloneSurvey({dispatch}, id) {
        const result = await axios.post(`/admin/survey/${id}/clone`);
        const { error } = result.data;
        if (!error) dispatch('getSurveys');
        return result.data;
    },

    async getSurvey({commit}, id) {
        commit('SET_LOADING', true);
        try {
            const result = await axios.get(`/admin/survey/${id}`);
            const { error, data } = result.data;
            
            commit('SET_SURVEY', data.survey);
            commit('SET_SURVEY_QUESTIONS', data.questions);
            commit('SET_USER_LOCALE', data.userLocale); 
            return result.data;
        } finally {
            commit('SET_LOADING', false);
        }
    },

    async activateQuestion({ commit }, { id, active }) {
        const result = await axios.post(`/admin/survey/question/${id}/activate`, { active });
        return result.data;
    },

    async saveSurveyQuestion({ commit }, { surveyId, question }) {
        const headers = {
            'Content-Type': 'multipart/form-data'
        };
        const result = await axios.post(`/admin/survey/${surveyId}/save-question`, question, {
            headers
        });
        
        return result.data;
    },

    async getManualData({ commit }) {
        const result = await axios.get('/admin/survey/manual-data');
        const { data } = result.data;
        commit('SET_MANUAL_DATA', data);
    },

    async getSelectedCoursesAndAnnouncements({ commit }, id) {
        const result = await axios.get(`/admin/survey/${id}/courses-announcements`);
        const { data } = result.data;
        commit('SET_SURVEY_COURSES', data.courses);
        commit('SET_SURVEY_ANNOUNCEMENTS', data.announcements);
    },

    async setSurveyApplyTo({ commit }, { id, applyTo }) {
        const result = await axios.post(`/admin/survey/${id}/apply-to`, { applyTo })
        return result.data;
    },

    async deleteSurveyQuestion({ commit }, id) {
        const result = await axios.delete(`/admin/survey/question/${id}`);
        return result.data;
    },

    // New methods
    editLocalQuestion({ commit, getters, rootGetters }, index) {
        const locales = rootGetters['localeModule/locales'];
        const { form } = getters;
        const question = structuredClone(form.questions[index]);
        const keys = Object.keys(locales);
        keys.forEach(k => {
            if (!question.translations.find(t => t.locale === k)) {
                question.translations.push({locale: k, statement: ''});
            }
        })
        commit('editQuestion', {index, question });
    },
    saveLocalQuestion({ commit, getters }) {
        const { indexQuestionForm } = getters;

     
        
        if (indexQuestionForm < 0) {
            commit('addCurrentQuestion')
        } else {
            commit('updateCurrentQuestion');
        }
    },

    resetQuestionForm({ commit }) {
        commit('RESET_QUESTION_FORM')
    },

    // saveSurvey({ getters }) {
    //     const { form } = getters; 

    //     const url = form?.survey?.id > 0 ? `/admin/survey/${form.survey.id}/update` : '/admin/api/v1/survey';

    //     return axios.post(url, form).then(r => (r.data))
    //         .catch(e => ({ error: true, data: 'Failed to make request', trace: e}));
    // },

    async saveSurvey({ commit, getters }, { id, formData }) {
        try {
            console.log(id)
          //  const { form } = getters;
          commit("SET_LOADING", true);
    
          const { data, error } = await axios.post(`/admin/survey/${id}/update`, formData);
    
          if (error) {
            throw new Error(data?.message);
          }
        } catch (error) {
          console.log(error);
        } finally {
          commit("SET_LOADING", false);
        }
    },

    async save({ commit }, {  formData }) {
        try {
          commit("SET_LOADING", true);
    
          const { data, error } = await axios.post('/admin/api/v1/survey', formData);
    
          if (error) {
            throw new Error(data?.message);
          }
        } catch (error) {
          console.log(error);
        } finally {
          commit("SET_LOADING", false);
        }
      },


    async changePosition({ commit }, { endpoint, requestData }) {
      try {
       const { data, error } = await axios.put(endpoint, requestData);

        if (error) {
          throw new Error(data?.message);
        }
      } catch (error) {
        console.log(error);
      }
    },

    async changeIsRequiered({ commit }, { endpoint, requestData }) {
        const result = await axios.put(endpoint, requestData);

        return result.data;
    },

    async changeIsConfidential({ commit }, { endpoint, requestData }) {
        const result = await axios.put(endpoint, requestData);

        return result.data;
    },

    async changeIsMain({ commit }, {surveyId }) {
        const result = await axios.put(`admin/survey/${surveyId}/isMain`);

        return result.data;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
}
