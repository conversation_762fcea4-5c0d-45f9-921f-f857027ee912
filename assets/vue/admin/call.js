import Vue from 'vue';
import Call from './views/Call';
import store from './store';
import Multiselect from 'vue-multiselect';
import 'vue-multiselect/dist/vue-multiselect.min.css';
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';

Vue.use(VueToast);

Vue.component('multiselect', Multiselect)

new Vue({
    components: { Call, Multiselect },
    // template: '<Call v-bind:id="this.announcement"></Call>',
    store,
}).$mount('#call')
