import Vue from 'vue';

Vue.filter('nicetime', seconds => {
    if (seconds === 0) {
        return '-';
    }

    let hours = Math.floor(seconds / 3600);
    let mins = Math.floor((seconds - hours * 3600) / 60);

    return (hours > 0 ? hours + ' horas ' : '') + mins + ' minutos';
});

Vue.filter('formatDate', timestamp => {
    if (!timestamp) return '';

    let date = new Date(timestamp * 1000);
    let months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    let minutes =  date.getMinutes() < 10 ? '0' +  date.getMinutes() : date.getMinutes();
    return date.getDate() + ' ' + months[date.getMonth()] + ' ' + date.getFullYear() + ' ' + date.getHours() + ':' + minutes;
})
