<template>
  <div class="statementType">
    <p class="my-0">
      <b class="text-uppercase">{{indexValue}}{{ $t('SURVEY.STATEMENT') }}:</b> {{ question.text }}
    </p>
    <p class="my-0">
      <b class="text-uppercase">{{$t('SURVEY.SOLUTION')}}:</b>
      <span v-for="(_, answerIndex) in question.answers"
            :key="'statement_solution_question_' + index + '_answer_' + answerIndex">
        {{ getQuestionSolutionText(answerIndex) }}
      </span>
    </p>
    <p class="mt-0 mb-3" v-if="showResponse">
      <b class="text-uppercase">{{ responseLabel }}:</b>
      <span v-for="(answer, answerIndex) in question.answers"
            :class="{'text-success': details && answer.correct, 'text-danger': details && answer.incorrect}"
            :key="'statement_question_' + index + '_answer_' + answerIndex">
        {{ getQuestionAnswersText(answerIndex) }}
      </span>
    </p>
  </div>
</template>

<script>
import { attemptQuestions } from '../../models/UserStatsModel'

export default {
  name: "statementType",
  props: {
    index: { type: Number, default: 0 },
    details: { type: Boolean, default: false },
    enumerable: { type: Boolean, default: false },
    showResponse: { type: Boolean, default: false },
    question: { type: attemptQuestions, default: () => (new attemptQuestions()) },
  },
  computed: {
    indexValue() {
      return this.index ? `${this.index}. ` : ''
    },
    responseLabel() {
      return `${this.$t('SURVEY.QUESTION.RESPONSE', [''])}`.trim()
    }
  },
  methods: {
    getQuestionAnswersText(answerIndex) {
      return (answerIndex ? ', ' : '') + (this.enumerable ? answerIndex + 1 + '. ' : '') + this.question.answers[answerIndex].text
    },
    getQuestionSolutionText(answerIndex) {
      return (answerIndex ? ', ' : '') + (this.enumerable ? answerIndex + 1 + '. ' : '') + this.question.answers[answerIndex].answer
    }
  }
}
</script>

<style scoped lang="scss">
.statementType {
}
</style>