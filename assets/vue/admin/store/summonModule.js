import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
    called: undefined,
    notified: undefined,
    results: undefined,
    filters: undefined,
    loading: false, // todo add funcionality
    searching: false, // todo add funcionality
    calling: false, // todo add funcionality
    notifying: false, // todo add funcionality
});

const state = () => getDefaultState();

export const getters = {
    getCalled: (state) => () => state.called,
    isLoading: (state) => () => state.loading,
    isSearching: (state) => () => state.searching,
    isCalling: (state) => () => state.calling,
};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    async fetchFilters({ commit }, challenge) {
        const url = `/admin/challenge/${challenge}/filters`;
        let filters;

        try {
            commit('SET_SEARCHING', true);
            const {data} = await axios.get(url);
            filters = data.data.filters;
            commit('SET_FILTERS', filters);
        } finally {
            commit('SET_SEARCHING', false);
        }

        return filters;
    },

    async fetchCalled({ commit }, challenge) {
        const url = `/admin/challengeuser/${challenge}/called`;
        let called;

        try {
            commit('SET_LOADING', true);
            const {data} = await axios.get(url);
            called = data.data.called;
            commit('SET_CALLED', called);
        } finally {
            commit('SET_LOADING', false);
        }

        return called;
    },

    async fetchSearch({ commit }, search) {
        const url = `/admin/challengeusers/${search.challenge}/search`;
        let results;

        try {
            commit('SET_SEARCHING', true);
            const {data} = await axios.post(
                url,
                {
                    q: search.searchQuery,
                }
            );
            results = data.data.results;
            commit('SET_RESULTS', results);
        } finally {
            commit('SET_SEARCHING', false);
        }

        return results;
    },

    async fetchCall({ commit }, call) {
        const url = `/admin/challengeusers/${call.challenge}/call/${call.user}`;
        let result;

        try {
            commit('SET_CALLING', true);
            const {data} = await axios.get(url);
            result = data;
            commit('SET_CALLED', result);
        } finally {
            commit('SET_CALLING', false);
        }

        return result;
    },

    async fetchUnCall({ commit }, call) {
        const url = `/admin/challengeusers/${call.challenge}/uncall/${call.user}`;
        let result;

        try {
            commit('SET_CALLING', true);
            const {data} = await axios.get(url);
            result = data;
            commit('SET_CALLED', result);
        } finally {
            commit('SET_CALLING', false);
        }

        return result;
    },

    async fetchNotify({ commit }, call) {
        const url = `/admin/challengeusers/${call.challenge}/notify/${call.user}`;
        let result;

        try {
            commit('SET_NOTIFYING', true);
            const {data} = await axios.get(url);
            result = data;
            commit('SET_NOTIFIED', result);
        } finally {
            commit('SET_NOTIFYING', false);
        }

        return result;
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
