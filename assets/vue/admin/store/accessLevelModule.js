import axios from 'axios';
import {make} from 'vuex-pathify';

const getDefaultState = () => ({});

const state = () => getDefaultState();

export const getters = {};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {

    async fetchManagers({commit}, {}) {
        const url = `/admin/managers/list`;
        let managers;

        try {
            const {data} = await axios.get(url);
            managers = data.data.managers;
        } finally {
        }

        return managers;
    },

    async fetchCourseManagers({commit}, {courseId}) {
        const url = `/admin/managers/course/${courseId}` ;
        let managers;

        try {
            const {data} = await axios.get(url);
            managers = data.data.managers;
        } finally {
        }

        return managers;
    },

    async fetchProfessionalCategories({commit}, {}) {
        const url = `/admin/professional-categories/list`;
        let professionalCategories;

        try {
            const {data} = await axios.get(url);
            professionalCategories = data.data.professionalCategories;
        } finally {
        }

        return professionalCategories;
    },

    async fetchCourseProfessionalCategories({commit}, {courseId}) {
        const url = `/admin/professional-categories/course/${courseId}` ;
        let professionalCategories;

        try {
            const {data} = await axios.get(url);
            professionalCategories = data.data.professionalCategories;
        } finally {
        }

        return professionalCategories;
    },

    async fetchFilterCategories({commit}, {}) {
        const url = `/admin/filter-categories` ;
        let filterCategories;

        try {
            const {data} = await axios.get(url);
            filterCategories = data.data.filterCategories;          
        } finally {
        }

        return filterCategories;
    },

    async fetchCourseFilters({commit}, {courseId}) {
        const url = `/admin/filters/course/${courseId}` ;
        let courseFilters;

        try {
            const {data} = await axios.get(url);
            courseFilters = data.data.courseFilters;
        } finally {
        }

        return courseFilters;
    },

};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
