import axios from 'axios';
import { make } from 'vuex-pathify';

const getDefaultState = () => ({
    userchapter: undefined,
    sending: false, // todo add funcionality
});

const state = () => getDefaultState();

export const getters = {
    isSending: (state) => () => state.sending,
    userChapter: (state) => () => state.userchapter,
};

export const mutations = {
    ...make.mutations(state),
};

export const actions = {
    async getState({ commit }, chapter) {
        const url = `/api/chapter/${chapter}/start`;
        try {
            commit('SET_SENDING', true);
            const {data} = await axios.get(
                url,
                {headers: { Authorization: 'Bearer ' + localStorage.getItem('token')},}
            );
            console.log('Data Axios', data)
            console.log('Data Axios User Chapter', data.data.chapter.id)
            chapter = data.data.chapter;
            commit('SET_USERCHAPTER', data.data.chapter.id);
        } finally {
            commit('SET_SENDING', false);
        }
        console.log('Chapter', chapter);
        return chapter.data;
    },
    async updateState({ commit, state }, chapterStatus) {
        const url = `/api/chapter/${state.userchapter}/update`;
        try {
            commit('SET_SENDING', true);
            const {data} = await axios.post(
                url,
                {
                    data: chapterStatus
                },
                {headers: { Authorization: 'Bearer ' + localStorage.getItem('token')},}
            );
        } finally {
            commit('SET_SENDING', false);
        }
    },

    async downloadFile({commit}, namePdf) {
        const url = `/uploads/pdf/packages/` + namePdf;
        const data = await axios({
            url: url,
            method: 'GET',
            headers: {'ContentType': 'application/x-www-form-urlencoded', Authorization: 'Bearer ' + localStorage.getItem('token')},
            responseType: 'blob'
        }).then(res => {
            let link = document.createElement('a');
            link.href = window.URL.createObjectURL(new Blob([res.data], {type: 'application/pdf'}));
            link.download = namePdf;
            link.click();
        });
    }
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
