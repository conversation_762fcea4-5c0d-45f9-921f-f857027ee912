import Vue from 'vue';
import '../../vue/registerBaseComponents';
import UploadPuzzle from './views/UploadPuzzle';
import store from './store';
import { Cropper } from 'vue-advanced-cropper';
import 'vue-advanced-cropper/dist/style.css';
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';


window.Vue = new Vue({
  components: { UploadPuzzle },
  store,
  Cropper,
}).$mount('#upload-puzzle');

Vue.use(VueToast, {
  duration: 5000,
  position: 'top-right',
});
