import Vue        from 'vue';
import TaskCourse from './views/TaskCourse';
import listFilesTask from './components/task-course/listFilesTask';
import historyTask   from './components/task-course/historyTask';
import vueVimeoPlayer from 'vue-vimeo-player';

import store from './store';
import VueToast from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-sugar.css';

Vue.use(vueVimeoPlayer);

window.Vue = new Vue({
    components: { TaskCourse, listFilesTask, historyTask },
    store
}).$mount('#taskCourse')

Vue.use(VueToast, {
    duration: 5000,
    position: 'top-right',
});