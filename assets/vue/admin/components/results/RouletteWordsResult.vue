<template>
  <div class="results">
    <h4 class="mb-1">Results ({{ correctAnswers }}/{{ questions.length }})</h4>
    <div class="questions">
      <div class="question" v-for="(item, index) in questions" :key="index">

        <div class="letter state--default">{{ item.question.letter }}</div>

        <div class="roulette-options">
          <h5 v-if="item.question.type">
            Comienza por:
          </h5>

          <h5 v-else>
            Contiene:
          </h5>
        </div>

        <h5> Pista: {{item.question.question}} </h5>

        <div class="answers">
          <div class="answer"
               :class="'selected'"
          >
            <div class="answer-option correct">
              {{item.question.word}}
            </div>

            -->

            <div class="answer-option"
                 :class="item.correct ? 'correct' : 'wrong'">
              {{item.value}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'RouletteResults',
  props: ['questions'],

  data() {
    return {
    }
  },

  computed: {
    correctAnswers: function () {
      let correct = 0;
      for (let i = 0; i < this.questions.length; i++) {
        if (this.questions[i].correct) {
          correct++;
        }
      }

      return correct;
    },
  }
};
</script>

 <style scoped lang="scss"> 
</style>
