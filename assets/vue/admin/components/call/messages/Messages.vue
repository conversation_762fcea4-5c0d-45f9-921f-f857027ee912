<template>
  <div class="messages">
    <button
      class="btn btn-primary btn-sm position-relative"
      type="button"
      data-bs-toggle="offcanvas"
      :data-bs-target="`#messagesCanvasBottom${user.id}`"
      aria-controls="offcanvasRight"
      @click="fetchMessagesUser()"
    >
      <i class="fas fa-sms"></i>
      <span
        class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
        v-if="messagesStudent.length > 0"
      >
        {{ messagesStudent.length }}
        <span class="visually-hidden">unread messages</span>
      </span>
    </button>

    <div
      class="offcanvas offcanvas-end"
      tabindex="-1"
      :id="`messagesCanvasBottom${user.id}`"
      aria-labelledby="offcanvasRightLabel"
    >
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="messagesCanvasBottomLabel">
          {{ user.firstName }}
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white text-reset"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
        ></button>
      </div>
      <div class="offcanvas-body small" id="body-messages">
        <div class="comments" id="comments">
          <Comment
            v-for="message in messages"
            :key="message.id"
            :comment="message"
          />
          <div v-if="messages && messages.length === 0">
            {{ translationsVue.announcements_configureFields_no_messages }}
          </div>
        </div>

        <div class="send-messages">
          <div class="content">
            <textarea placeholder="Añadir comentario" v-model="comment" />
            <button class="btn btn-primary" @click="sendComment()">
              <i class="fas fa-paper-plane"> </i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { get } from "vuex-pathify";
import Comment from "./Comment";

export default {
  components: {
    Comment,
  },

  props: {
    user: {
      type: Object,
      required: true,
    },

    messagesStudent: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      idAnnouncement,
      comment: "",
      translationsVue,
    };
  },

  computed: {
    ...get("callModule", ["isLoading", "getMessages"]),

    messages() {
      return this.getMessages();
    },
  },

  methods: {
    async fetchMessagesUser() {
      console.log("fetchMessagesUser");
      const data = {
        idUser: this.user.id,
        announcement: this.idAnnouncement,
      };

      await this.$store.dispatch("callModule/fetchMessagesByUser", data);
      this.scrollToEnd();
    },

    async sendComment() {
      const formData = new FormData();
      formData.append("form-body", this.comment);
      formData.append("form-user-id", this.user.id);
      formData.append("form-id-announcement", this.idAnnouncement);

      await this.$store.dispatch("callModule/sendMessageToStudent", formData);
      this.scrollToEnd();
      this.comment = "";

      if (this.user.messages > 0) {
        await this.$store.dispatch(
          "callModule/fetchClassroomAnnouncement",
          this.idAnnouncement
        );
      }
    },

    scrollToEnd() {
      const container = this.$el.querySelector("#body-messages");
      container.scrollTop = container.scrollHeight;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.messages {
  .comments {
    margin-bottom: 8rem;
  }

  .send-messages {
    margin-top: 1rem;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    flex: 1;
    gap: 1rem;
    bottom: 0;
    left: 0;
    right: 0;
    position: absolute;
    width: 100%;
    background: #fff;

    .content {
      margin-top: 1rem;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      width: 100%;
      padding: 1rem;
      gap: 1rem;

      textarea {
        flex: 1;
        border-radius: 8px;
        font-size: 16px;
        width: 80%;
        height: 2.5rem;
        margin: auto;
        border: solid 1px var(--color-neutral-mid-light);

        &:focus {
          outline: 1px solid var(--color-primary);
        }
      }
    }
  }
}

.offcanvas-bottom {
  right: 0;
  left: 0;
  height: 50vh !important;
  max-height: 100%;
}

.offcanvas-header {
  background: $color-app;
  color: #fff !important;
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: 1.5;
  color: #fff;
}

.offcanvas-end {
  top: 50vh;
}

.btn-info {
  color: #fff;
}

.offcanvas-body {
  text-align: left;
}
</style>
