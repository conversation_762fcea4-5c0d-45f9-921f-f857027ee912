<template>
  <div class="routelleLetter">
    <div @click="toggleDisplay">
      {{ letter }}
    </div>

    <div v-show="display">
      <div>
        <label for="type">Selecciona: </label>

        <select id="type" v-model="selected">
          <option v-for="option in options" v-bind:value="option.value">
            {{ option.text }}
          </option>
        </select>
      </div>

      <div>
        <label for="text">Explicacion</label>
        <textarea id="text" v-model="text" placeholder="Explicación"></textarea>
      </div>

      <div>
        <label for="response">Respuesta</label>
        <input id="response" v-model="word" placeholder="Palabra respuesta" />
      </div>

      <div>Botón guardar</div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["letter"],

  data() {
    return {
      display: false,
      text: "",
      word: "",
      selected: 0,
      options: [
        { text: "Comienza por", value: 0 },
        { text: "Contiene", value: 1 },
      ],
    };
  },

  computed: {},

  async created() {},

  methods: {
    toggleDisplay() {
      this.display = !this.display;
    },
  },
};
</script>

 <style scoped lang="scss"> 
.routelleLetter {
}
</style>
