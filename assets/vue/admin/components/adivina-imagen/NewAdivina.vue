<template>
  <CreateAdivina :adivina="adivina"/>
</template>

<script>
import C<PERSON><PERSON><PERSON><PERSON> from "./Create<PERSON><PERSON><PERSON>";

export default {
  components: {
    Create<PERSON><PERSON><PERSON>,
  },

  props: {
    urlChapter: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      adivina: {
        image: null,
        words: "",
        clue: "",
        time: "30",
        title: "",
      },
    };
  },
};
</script>

 <style scoped lang="scss"> 
.new-question {
  .preview-image {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 1px solid #ccc;
  }

  input[type="file"] {
    display: none;
  }

  .trash-question {
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;
    .trash {
      cursor: pointer;
    }
  }

  .options {
    .form {
      height: 100%;
      flex: 2;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .form-head {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 1rem;
        .form-inputs {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }
      }

      .form-footer {
        margin-top: 1rem;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 1rem;
        .words {
          flex-grow: 1;
        }
        .help {
          flex-basis: 50%;
        }
      }
    }
  }
}
</style>
