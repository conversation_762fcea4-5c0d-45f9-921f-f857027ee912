<template>
  <div class="SimpleCard" :class="{loading: isLoading}">
    <div class="icon-container" :style="{backgroundColor: isLoading ? '#CFD8DC' : options.color}">
      <i :class="isLoading ? 'fa fa-circle-o-notch fa-spin fa-fw': options.icon"></i> <span>{{ isLoading ? '' : options.title }}</span></div>
    <p class="value" :style="{color: options.color}">{{ isLoading ? '' : options.value }}</p>
  </div>
</template>

<script>

export default {
  name: "SimpleCard",
  components: {},
  props: {
    isLoading: {
      type: Boolean,
      default: true
    },
    tag: {
      type: String,
      default: "Card",
    },
    options: {
      type: Object,
      default: () => ({
        title: '',
        value: '',
        color: '#8BC34A',
        icon: 'fa fa-user'
      })
    },
  },
};
</script>

 <style scoped lang="scss"> 
.SimpleCard {
  width: 100%;
  height: 100%;
  background-color: white;
  border: 1px solid #E7EBF0;
  box-shadow: 0 0 2px 2px #ECEFF1;
  overflow: hidden;
  border-radius: 7px;
  display: grid;
  grid-template-rows: 1fr 1fr;
  gap: 0;

  p {
    margin: 0 auto;
  }

  .icon-container {
    font-size: 3rem;
    color: white;
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-gap: 1rem;
    align-items: center;
    text-align: center;

    span {
      text-align: left;
      font-size: 1.2rem;
    }
  }

  .value {
    font-weight: bold;
    font-size: 3rem;
    padding: 1rem;
    align-self: center;
  }

  &.loading {
    .icon-container {
      width: 100%;
      display: block;
      text-align: center;
      margin: 0 auto;
      padding: 1rem;
    }

    .value {
      border-radius: 7px;
      width: clamp(50px, 90%, 300px);
      background-color: #37474F;
      height: 0.5rem;
      margin: 0 auto;
      animation: opacityAnimation 1.1s linear infinite alternate;
    }
  }
}
</style>
