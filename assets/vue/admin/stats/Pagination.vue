<template>
  <div class="paginationContainer">
    <div
        class="pagination-control prev"
        :class="{disabled: currentPage < 1}"
        @click="prevPage">
      <span><i class="fas fa-angle-left"></i> Previous</span>
    </div>
    <div
        v-for="page in pages"
        :key="'page' + page"
        class="pagination-control page"
        :class="{active: page === currentPage}"
        @click="setPage(page)">
      <span>{{ page + 1 }}</span>
    </div>
    <div
        class="pagination-control next"
        :class="{disabled: currentPage === maxValue}"
        @click="nextPage">
      <span>Next <i class="fas fa-angle-right"></i></span>
    </div>
  </div>
</template>

<script>
export default {
  name: "ChartPagination",
  props: {
    totalItems: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 10
    },
  },
  data() {
    return {
      currentPage: 0,
      pages: []
    };
  },
  computed: {
    maxValue() {
      const maxVal = Math.max(Math.ceil(this.totalItems / (this.pageSize || 1)) - 1, 0);
      if (maxVal < this.currentPage) this.currentPage = 0;
      return maxVal;
    },
  },
  mounted() {
    this.initPagination();
  },
  methods: {
    initPagination() {
      this.currentPage = 0;
      this.generatePages();
    },
    emitData() { this.$emit('current-page', this.currentPage); },
    generatePages() {
      this.pages = [];
      const Max = Math.min((!this.currentPage ? 2 : this.currentPage + 1), this.maxValue);
      const Min = Math.max(this.currentPage - (this.currentPage === this.maxValue ? 2 : 1), 0)
      for (let i = Min; i <= Max; i += 1) {
        this.pages = [...this.pages, i];
      }
    },
    setPage(page) {
      this.currentPage = page;
      this.generatePages();
      this.emitData();
    },

    prevPage() {
      if (this.currentPage > 0) this.setPage(this.currentPage - 1);
    },

    nextPage() {
      if (this.currentPage < this.maxValue) this.setPage(this.currentPage + 1);
    },
  }
}
</script>

 <style scoped lang="scss"> 
.paginationContainer {
  display: flex;
  flex-wrap: wrap;
  margin: 0.5rem 1rem 1rem;
  gap: 0.25rem;
  justify-content: flex-end;
  user-select: none;
  font-size: 0.8rem;

  .pagination-control {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 2rem;
    height: 2rem;
    cursor: pointer;
    color: #455A64;

    &.page.active {
      color: white;
      background-color: #68BBB0;
      border-radius: 3px;
    }

    &.prev, &.next {
      white-space: nowrap;
      vertical-align: middle;
      width: fit-content;
      padding-inline: 0.5rem;
    }

    &.disabled {
      color: #B0BEC5;
    }
  }
}
</style>
