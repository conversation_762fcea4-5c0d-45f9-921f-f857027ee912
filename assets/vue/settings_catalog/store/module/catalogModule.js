import { make } from "vuex-pathify";
import axios from "axios";

const getDefaultState = () => ({
  loading: true,
  catalogs: [],
  catalogsFromBd: [],
  modalities: [],
});

const state = () => getDefaultState();

export const getters = {
  //...make.getters(state),
  getCatalogsFromBd: (state) => () => state.catalogsFromBd,
  getModalities: (state) => () => state.modalities,
};

export const mutations = {
  ...make.mutations(state),
};

export const actions = {
  ...make.actions(state),
  async load({ commit }, endpoint) {
    commit("SET_LOADING", true);
    return await axios
      .get(endpoint)
      .then((r) => {
        const { data } = r.data;

        commit("SET_CATALOGS", data);
        commit("SET_MODALITIES", data); 

        return r.data;
      })
      .catch((e) => ({
        error: true,
        data: e,
      }))
      .finally(() => {
        commit("SET_LOADING", false);
      });
  },

  async changeActiveState({}, { endpoint, data }) {
    return await axios
      .post(endpoint, data)
      .then((r) => r.data)
      .catch((e) => ({
        error: true,
        data: e,
      }));
  },

  async save({}, { endpoint, data }) {
    return await axios
      .post(endpoint, data)
      .then((r) => r.data)
      .catch((e) => ({
        error: true,
        data: e,
      }));
  },

  async uploadTypeDiplomaFiles({}, { endpoint, formData }) {
    const result = await axios.post(endpoint, formData);

    return result;
  },

  async generatePreviewTypeDiploma({}, endpoint) {
    const result = await axios.post(endpoint);

    return result;
  },

  async loadCatalogsFromBd({ commit }, endpoint) {
    try {
      const { data } = await axios.get(endpoint);
      commit("SET_CATALOGS_FROM_BD", data?.data);
    } catch (error) {
      console.log("error", error);
    } finally {
      commit("SET_LOADING", false);
    }
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
