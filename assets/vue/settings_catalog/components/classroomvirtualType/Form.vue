<script>
import BaseForm from "../BaseForm.vue";
import {get} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";

export default {
  name: "TypeCourse",
  components: {BaseSwitch, BaseForm},
  data() {
    return {
      locale: 'es',
      classroomvirtualType: {
        id: -1,
        name: '',
        state: false,
      },
    };
  },
  computed: {
    catalogs: get('catalogModule/catalogs'),
    locales: get('localeModule/locales'),
  },
  created() {
    if (this.catalogs.length === 0) {
      this.returnToList();
      return;
    }

    let 
      classroomvirtualType= {
        id: -1,
        name: '',
        state: false,
      }

    if (this.$route.name === 'ClassroomvirtualTypeUpdate') {
      classroomvirtualType = this.catalogs.find(c => c.id === this.$route.params.id);
      if (classroomvirtualType === undefined) {
        this.returnToList();
        return;
      }
    }

    this.classroomvirtualType = classroomvirtualType;
  },
  methods: {
    returnToList() {
      this.$router.push({name: 'ClassroomvirtualType', params: this.$route.params});
    },

    submit() {
      const update = this.$route.name === 'ClassroomvirtualTypeUpdate';
      const endpoint = update ? '/admin/classroomvirtual-Type/update' : '/admin/classroomvirtual-Type/create';
      const save = () => {
        return this.$store.dispatch('catalogModule/save', { endpoint: endpoint, data: this.classroomvirtualType });
      }

      save().then(r => {
        const { error, data } = r;
        if (error) this.$toast.error(data);
        else {
          this.$toast.success(this.$t('CATALOG.SAVED') + '');
          this.returnToList();
        }
      })
    }
  }
}

</script>

<template>
  <base-form v-model="locale" @cancel="returnToList()" @submit="submit()">
    <template v-slot:form>
      <div class="form-group col-12">
        <label>{{ $t('NAME') }}</label>
        <input type="text" class="form-control" v-model="classroomvirtualType.name">
      </div>

      <div class="form-group col-12 d-flex align-items-center justify-content-start">
        <BaseSwitch :tag="`switcher-classroomvirtual-Type-form-state-${classroomvirtualType.id}`"
                     v-model="classroomvirtualType.state"/>
        <label class="ml-1">{{ $t('ACTIVE') }}</label>
      </div>
    </template>
  </base-form>
</template>

<style scoped lang="scss">

</style>
