<script>
import CategoryFilter, {
  generateCompatibleData,
  getFiltersForRequest
} from "../../common/components/filter/CategoryFilter.vue";
import AddRemove from "../../common/components/select/AddRemove.vue";
import Spinner from "../../base/BaseSpinner.vue";
import {get} from "vuex-pathify";
import Loader from "../../admin/components/Loader.vue";

export default {
  name: "AccessLevel",
  components: {Loader, Spinner, AddRemove, CategoryFilter},
  data() {
    return {
      loading: false,
      loadingSelectedFilters: true,

      savingFilters: false,
      savingManagers: false,

      loadingManagers: true,
      filters: [], //filters,\
      availableManagers: [],
      selectedManagers: [],

      // Professional categories configuration
      loadingProfessionalCategories: false,
      savingProfessionalCategories: false,
      professionalCategories: [],
      selectedProfessionalCategories: []
    };
  },
  computed: {
    courseUrl: get('configModule/config@courseUrl'),
    useFilters: get('configModule/config@useFilters')
  },
  created() {
    document.title = this.$t('COURSE.ACCESS_LEVEL') + '';
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: 'CourseDetail',
      params: {
        linkName: this.$t('USER.COURSES.COURSE'),
        params: {},
        type: 'link',
        linkValue: this.courseUrl
      }
    });

    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.$t('COURSE.ACCESS_LEVEL'),
        params: {}
      }
    });

    if (this.useFilters) this.loadSelectedFilters();
    this.loadManagers();
    if (!this.useFilters) this.loadProfessionalCategories();
  },
  methods: {
    async loadManagers() {
      this.loadingManagers = true;
      try {
        const result1 = await this.$store.dispatch("courseModule/getManagers", this.$route.params.id);
        this.selectedManagers = result1.data;

        this.availableManagers = await this.$store.dispatch("userModule/loadAllManagers");
      } finally {
        this.loadingManagers = false;
      }
    },

    async loadProfessionalCategories() {
      try {
        this.loadingProfessionalCategories = true;

        const result1 = await this.$store.dispatch('courseModule/getCourseProfessionalCategories', this.$route.params.id);
        this.selectedProfessionalCategories = result1.error ? [] : result1.data;


        const result2 = await this.$store.dispatch('courseModule/getProfessionalCategories');
        this.professionalCategories = result2.error ? [] : result2.data;

      } finally {
        this.loadingProfessionalCategories = false;
      }
    },

    loadSelectedFilters() {
      const courseId = this.$route.params.id;
      this.loadingSelectedFilters = true;
      this.$store
          .dispatch("courseModule/getCourseFilters", courseId)
          .then((r) => {
            const {data} = r;
            this.filters = generateCompatibleData(data);
          }).finally(() => {
        this.loadingSelectedFilters = false;
      });
    },

    saveFilters() {
      const data = getFiltersForRequest(this.filters);

      this.savingFilters = true;
      this.$store.dispatch('courseModule/saveCourseFilters', {id: this.$route.params.id, filtersIds: data}).then(r => {
        console.log(r);
        const {error, data} = r;
        if (!error) this.$toast.success(this.$t('CATALOG.SAVED') + '');
      }).finally(() => {
        this.savingFilters = false;
      })
    },

    saveManagers() {
      this.savingManagers = true;
      this.$store.dispatch('courseModule/saveCourseManagers', {
        id: this.$route.params.id,
        managers: this.selectedManagers
      }).then(r => {
        const {error, data} = r;
        if (!error) this.$toast.success(this.$t('CATALOG.SAVED') + '');
      }).finally(() => {
        this.savingManagers = false;
      })
    },

    saveProfessionalCategory() {
      this.savingProfessionalCategories = true;
      this.$store.dispatch('courseModule/saveCourseProfessionalCategories', {
        id: this.$route.params.id,
        categories: this.selectedProfessionalCategories
      })
          .then(r => {
            const { error } = r;
            if (!error) this.$toast.success(this.$t('CATALOG.SAVED') + '');
          })
          .finally(() => {
            this.savingProfessionalCategories = false;
          });
    }
  }
}
</script>

<template>
  <div class="col-12 AccessLevel">
    <!-- Filters -->
    <div class="Filters col-12" v-if="useFilters">
      <div class="w-100 d-flex align-content-center justify-content-center flex-row flex-nowrap"
           v-if="loadingSelectedFilters">
        <spinner/>
      </div>

      <div class="w-100" v-else>
        <div class="col-12 text-center">
          <h1>{{ $t("COURSE.AUDIENCE.TITLE") }}</h1>
          <p v-html="$t('COURSE.AUDIENCE.DESCRIPTION')"/>
        </div>

        <category-filter
            v-model="filters"
            :show-category-warning-status="true"
            :category-warning-status-text="
                $t('COURSE.AUDIENCE_WARNING_TEXT') + ''
              "
            :show-titles="true"
            :allow-all="true"
        />

        <div class="w-100 mt-2 d-flex align-content-center justify-content-end SaveContainer">
          <loader :is-loaded="savingFilters" v-if="savingFilters" height="24"/>
          <button :disabled="savingFilters" @click="saveFilters()" type="button" class="btn btn-primary">
            <i class="fa fa-save"></i> {{ $t('SAVE_FILTERS') }}
          </button>
        </div>
      </div>
    </div>


    <!-- Professional Categories -->
    <div class="col-12 ProfessionalCategories" v-else>
      <add-remove
          :source-items="professionalCategories"
          :realtime="false"
          :title="$t('PROFESSIONAL_CATEGORY') + ''"
          :enable-all="false"
          v-model="selectedProfessionalCategories"
          :loading-source="loadingProfessionalCategories"
          :loading-selected="loadingProfessionalCategories"
      />
      <div class="w-100 mt-2 d-flex align-content-center justify-content-end">
        <loader :is-loaded="savingProfessionalCategories" v-if="savingProfessionalCategories" height="24"/>
        <button :disabled="savingProfessionalCategories" @click="saveProfessionalCategory()" type="button"
                class="btn btn-primary"><i class="fa fa-save"></i> {{ $t('SAVE_PROFESSIONAL_CATEGORY') }}
        </button>
      </div>
    </div>

    <!-- Managers -->
    <div class="col-12 Managers">
      <add-remove
          :source-items="availableManagers"
          :realtime="false"
          :title="$t('COURSE.MANAGERS') + ''"
          :enable-all="false"
          v-model="selectedManagers"
          :loading-source="loadingManagers"
          :loading-selected="loadingManagers"
          :fields="{ id: 'id', name: 'fullName' }"
      />
      <div class="w-100 mt-2 d-flex align-content-center justify-content-end">
        <loader :is-loaded="savingManagers" v-if="savingManagers" height="24"/>
        <button :disabled="savingManagers" @click="saveManagers()" type="button" class="btn btn-primary"><i class="fa fa-save"></i> {{ $t('SAVE_MANAGERS')
          }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.SaveContainer {
}
</style>
