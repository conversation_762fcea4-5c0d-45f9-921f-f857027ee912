<script>
import { sync } from 'vuex-pathify';
import MappingForm from "./MappingForm.vue";
import MappingGroup from "./MappingGroup.vue";
import Spinner from "../../../base/BaseSpinner.vue";

export default {
  name: "MappingGroups",
  components: {Spinner, MappingGroup, MappingForm },
  data() {
    return {
      // groups: [],
      viewGroupIndex: -1
    };
  },
  computed: {
    loading: sync('integrationMappingModule/loading'),
    groups: sync('integrationMappingModule/groups'),
    // currentGroup: sync('integrationMappingModule/currentGroup'),
    currentIndex: sync('integrationMappingModule/current@index'),
    currentGroup: sync('integrationMappingModule/current@group'),
    mappings: sync('integrationMappingModule/current@mappings'),
    saving: sync('integrationMappingModule/saving'),
  },
  methods: {
    addGroup() {
      this.groups.push({
        id: Date.now(),
        name: 'Default',
        active: false,
        endpoints: [],
        mappings: []
      });
    },
    setGroup(index) {
      this.currentIndex = index;
      this.currentGroup = this.groups[index];
      this.mappings = this.currentGroup.mappings ?? [];
    }
  }
}
</script>

<template>
  <div class="MappingGroups">
    <div class="col-12">
      <button type="button" class="btn btn-primary" @click="addGroup()"><i class="fa fa-plus"></i></button>
    </div>
    <div class="d-flex w-100 align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>
    <div class="col-12 AllGroups mt-3" v-else>
      <div class="Options">
        <span v-if="groups.length === 0">No data</span>
        <span v-for="(group, i) in groups"
              @click="setGroup(i)"
              :key="`mapping-group-${i}`"
              :class="currentGroup?.id === group.id ? 'active' : ''"
        >
          <strong>{{ group.name }}</strong>
        </span>
      </div>
      <div class="Content" v-if="groups.length > 0">
        <MappingGroup v-if="currentGroup && !saving"></MappingGroup>
        <div class="d-flex align-items-center justify-content-center" v-if="saving">
          <spinner />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.MappingGroups {
  width: 100%;
  .AllGroups {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 1rem;

    .Options {
      display: flex;
      flex-flow: column;
      row-gap: 5px;
      align-items: flex-start;
      span {
        width: 100%;
        cursor: pointer;
        text-align: center;
        border-radius: 3px;
        padding: 3px 10px;
        &:hover {
          background-color: #dbdbfa;
        }

        &.active {
          background-color: #ffffff !important;
          border: 1px solid var(--color-primary) !important;
          color: var(--color-primary) !important;
        }
        &.inactive {
          background-color: var(--color-neutral-light) !important;
          border: 1px solid var(--color-neutral-mid) !important;
          color: var(--color-neutral-mid-dark) !important;
        }

      }
    }

    .Content {
      border: 1px solid var(--color-neutral-mid);
      background-color: var(--color-neutral-lighter);
    }
  }
}

.modal-dialog.Endpoint {
  box-shadow: 1px 3px 6px #212121;
}
</style>
