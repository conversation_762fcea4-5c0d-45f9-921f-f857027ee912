import {make} from "vuex-pathify";
import axios from "axios";

const state = {
    testing: false,
    loading: false,
    mapping: {},
    configuration: {
        enabled: false,
        // Base configuration
        connectionString: 'ldap://example.com',//ldap://<url>:<port>,
        baseDn: 'dc=example,dc=com',
        // Cron configuration
        usernameCn: 'cn',
        username: 'read-only-admin',
        password: '',
        searchDn: '', // ou=users
        searchQuery: '(objectclass=*)',
        mappingGroupId: null,

        userBaseDn: 'dc=example,dc=com',
        userDn: 'uid',
    },
    userTest: {
        username: '',
        password: ''
    },
    tests: {
        testResults: "No test has been run"
    }
};

export const mutations = {
    ...make.mutations(state)
};

export const getters = {
    ...make.getters(state)
};

export const actions = {
    testUserLogin({ commit, getters }) {
        const { userTest } = getters;
        commit('SET_TESTING', true);
        axios.post('/admin/api/v1/integrations/ldap/test-user-login', userTest).then(r => {
            commit('SET_TESTS', {
                testResults: r
            });
        }).catch(e => {
            if (e.response !== undefined) {
                commit('SET_TESTS', {
                    testResults: e.response
                });
            }
        }).finally(() => {
            commit('SET_TESTING', false);
        })
    },

    testCronConnection({ getters, commit }) {
        const { configuration } = getters;
            commit('SET_TESTING', true);
        axios.post('/admin/api/v1/integrations/ldap/test-cron-login', configuration).then(r => {
            const { data } = r.data;
            console.log(data);
            if (data.length > 0) {
                console.log(data[0]);
                const attrs = Object.keys(data[0]);
                commit('configModule/setConfigKey', { key: 'remoteAttributes', value: attrs }, { root: true});
            }
            commit('SET_TESTS', {
                testResults: r
            });
        }).catch(e => {
            commit('SET_TESTS', {
                testResults: e
            });
        }).finally(() => {
            commit('SET_TESTING', false);
        })
    },

    getConfiguration({ commit }) {
        commit('SET_LOADING', true);
        axios.get('/admin/api/v1/integrations/ldap').then(r => {
            const { data } = r.data;
            commit('SET_CONFIGURATION', data);
        }).finally(() => {
            commit('SET_LOADING', false);
        })
    },

    save({ getters }) {
        const { configuration } = getters;
        return axios.post('/admin/api/v1/integrations/ldap', configuration);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    getters,
    actions
}
