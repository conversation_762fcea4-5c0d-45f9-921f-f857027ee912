export class CourseInfo {
  constructor({
    id,
    name,
    typeCourse,
    typeCourseId,
    category,
    createdBy,
    updatedBy,
    createdAt,
    updatedAt,
    description,
    generalInformation,
    image,
    totalChapters,
    locale,
    languages,
    active,
    open,
    open_visible,
    isNew,
    averageRating,
    usersStartCourse,
    usersFinishCourse,
    totalTimeCourse,
    translation,
    icon,
  }) {
    this.id = id ?? null;
    this.name = name ?? null;
    this.typeCourse = typeCourse ?? null;
    this.typeCourseId = typeCourseId ?? null;
    this.category = category ?? null;
    this.createdBy = createdBy ?? null;
    this.updatedBy = updatedBy ?? null;
    this.createdAt = createdAt ?? null;
    this.updatedAt = updatedAt ?? null;
    this.description = description ?? "";
    this.generalInformation = generalInformation ?? "";

    this.image = image ?? null;
    this.totalChapters = totalChapters ?? null;
    this.translation = translation ?? null;
    this.locale = locale ?? null;
    this.icon = icon ?? null;
    this.languages = languages ?? [];

    this.active = active ?? false;
    this.open = open ?? false;
    this.openVisible = open_visible ?? false;
    this.isNew = isNew ?? false;
    this.averageRating = averageRating || 0;

    this.usersStartCourse =
      usersStartCourse !== undefined && usersStartCourse !== null
        ? Number(usersStartCourse)
        : 0;
    this.usersFinishCourse =
      usersFinishCourse !== undefined && usersFinishCourse !== null
        ? Number(usersFinishCourse)
        : 0;
    this.totalTimeCourse =
      totalTimeCourse !== undefined && totalTimeCourse !== null
        ? Number(totalTimeCourse)
        : 0;
  }
}
