<template>
  <div id="player-wrapper" v-show="isPlayerVisible" @click.self="closePlayer">
    <div id="player">
      <div id="player-actions">
        <h2 id="player-title">{{ playerTitle }}</h2>
        <i class="fa fa-close" @click="closePlayer"></i>
      </div>
      <iframe :src="playerUrl" ref="playerIframe"></iframe>
    </div>
  </div>
</template>

<script>
export default {
  name: "PlayerGames",
  data() {
    return {
      isPlayerVisible: false,
      playerUrl: "",
      playerTitle: "",
    };
  },
  methods: {
    openPlayer({ url, title }) {
      this.playerUrl = url || "";
      this.playerTitle = title || "";
      this.isPlayerVisible = true;
    },
    closePlayer() {
      const iframe = this.$refs.playerIframe;
      const iframeSrc = iframe?.src || "";

      this.isPlayerVisible = false;
      this.playerUrl = "";

      const reloadPattern = /roleplay/gi;
      if (iframeSrc.match(reloadPattern)) {
        window.location.reload();
      }
    },
    handleMessage(event) {
      if (event.data === "close-modal") {
        this.closePlayer();
      }
    },
  },
  mounted() {
    window.addEventListener("message", this.handleMessage);
  },
  beforeUnmount() {
    window.removeEventListener("message", this.handleMessage);
  },
};
</script>

<style scoped>
#player-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 45, 94, 0.4);
  display: flex;
  z-index: 2000;
  padding: 0.5rem;
}

#player {
  width: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: 100%;
  background: var(--color-neutral-light);
  overflow: hidden;
  border-radius: 5px;
}

#player-actions {
  width: 100%;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--color-secondary);
  padding: 0.5rem 1.25rem;
}

#player-actions h2 {
  font-size: 1.25rem;
  color: white;
  margin: 0;
}

#player-actions i {
  cursor: pointer;
}

iframe {
  width: 100%;
  height: 100%;
}
</style>
