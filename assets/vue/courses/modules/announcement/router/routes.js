import AnnouncementTask        from "../../../../announcement/components/AnnouncementTask.vue";
import HomeView                from "../../../../announcement/views/HomeView.vue";
import NewObservation          from "../../../../announcement/../common/components/observationAnnouncement/NewObservation.vue";
import Observation             from "../../../../announcement/../common/components/observationAnnouncement/Observation.vue";
import TaskCourse              from "../../../../announcement/../common/components/taskCourse/TaskCourse.vue";
import ViewAnnouncement        from "../../../../announcement/views/ViewAnnouncement.vue";
import AnnouncementUserDetails from "../../../../announcement/views/AnnouncementUserDetails.vue";
import AnnouncementFormIntern from "../../../../announcement/views/AnnouncementFormIntern.vue";
import AnnouncementFormExtern from "../../../../announcement/views/AnnouncementFormExtern.vue";
import AnnouncementTaskDetails from "../../../../announcement/views/Details/AnnouncementTaskDetails.vue";
import ViewAnnouncementOld from "../../../../announcement/views/ViewAnnouncementOld.vue";
import DetailView from "../../../../announcement/views/DetailView.vue";
import AnnouncementInfoExtern from "../../../../announcement/views/Details/AnnouncementInfoExtern.vue";

import router from "../../../router/index";
import ROUTE_NAMES from "./routeNames";

export default [
  {
    path: "/admin/apps/announcement/create",
    component: AnnouncementFormIntern,
    name: ROUTE_NAMES.CREATE_ANNOUNCEMENT,
    beforeEnter: (to, from, next) => {
      if (router.app.$isGranted("ROLE_MANAGER")) next();
      return false;
    },
  },
  {
    path: "/admin/apps/announcement-extern/create",
    component: AnnouncementFormExtern,
    name: ROUTE_NAMES.CREATE_ANNOUNCEMENT_EXTERN,
    beforeEnter: (to, from, next) => {
      if (router.app.$isGranted("ROLE_MANAGER")) next();
      return false;
    },
  },
  {
    path: "/admin/apps/announcement/update/:id",
    component: AnnouncementFormIntern,
    name: ROUTE_NAMES.UPDATE_ANNOUNCEMENT,
    beforeEnter: (to, from, next) => {
      if (router.app.$isGranted("ROLE_MANAGER")) next();
      return false;
    },
  },
  {
    path: "/admin/apps/announcement-extern/update/:id",
    component: AnnouncementFormExtern,
    name: ROUTE_NAMES.UPDATE_ANNOUNCEMENT_EXTERN,
    beforeEnter: (to, from, next) => {
      if (router.app.$isGranted("ROLE_MANAGER")) next();
      return false;
    },
  },
  {
    path: "/admin/apps/announcement/:id",
    component: ViewAnnouncement,
    name: ROUTE_NAMES.VIEW_ANNOUNCEMENT,
  },
  {
    path: "/admin/apps/announcementOld/:id",
    component: ViewAnnouncementOld,
    name: ROUTE_NAMES.VIEW_ANNOUNCEMENT_OLD,
  },
  {
    path: "/admin/apps/announcement/task/:id/:studentId",
    component: AnnouncementTaskDetails,
    name: ROUTE_NAMES.VIEW_ANNOUNCEMENT_TASK_DETAILS,
  },
  {
    path: "/admin/apps/announcement/:id/task/create",
    component: AnnouncementTask,
    name: ROUTE_NAMES.CREATE_ANNOUNCEMENT_TASK,
  },
  {
    path: "/admin/apps/announcement/:announcementId/task/:id/update",
    component: AnnouncementTask,
    name: ROUTE_NAMES.UPDATE_TASK_COURSE,
  },
  {
    path: "/admin/apps/announcement/:parentName/:parentId/task/:id/view",
    component: TaskCourse,
    name: ROUTE_NAMES.VIEW_TASK_COURSE,
  },
  {
    path: "/admin/apps/announcement/:id/observation/create",
    component: NewObservation,
    name: ROUTE_NAMES.NEW_OBSERVATION,
  },
  {
    path: "/admin/apps/announcement/:id/observation/update",
    component: NewObservation,
    name: ROUTE_NAMES.UPDATE_OBSERVATION,
  },
  {
    path: "/admin/apps/announcement/observation/:id/view",
    component: Observation,
    name: ROUTE_NAMES.VIEW_OBSERVATION,
  },
  {
    path: "/admin/apps/announcement/:id/user-detail/:user",
    component: AnnouncementUserDetails,
    name: ROUTE_NAMES.VIEW_ANNOUNCEMENT_USER_DETAIL,
  },
  {
    path: "/admin/apps/announcement/:id/view",
    component: DetailView,
    name: ROUTE_NAMES.ANNOUNCEMENT_DETAIL,
    children: [
      {
        path: "info-extern",
        name: ROUTE_NAMES.ANNOUNCEMENT_EXTERN_INFO,
        component: AnnouncementInfoExtern,
      },
    ],
  },
];