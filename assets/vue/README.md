# MiniApp idea for modules

### Declare a global event handler in main.js file
```javascript
Vue.prototype.$eventBus = new Vue();// Global event bus
```

# Shared behavior
### Download locales
```shell
  yarn download-locales
```

### Import into main.js the following files
Locales handler
1. [i18n file](common/i18n.js)
2. [initLocale file](common/utils/initLocale.js)

If the app is going to use froala editor
1. [initFroalaEditor file](common/utils/initFroalaEditor.js)

Handle User info: Roles, names, etc
1. [InitUserInfo file](common/utils/initUserInfo.js)

### !Important: Full vue app with vue-router
If the app is going to work like a full vue app with vue-router, include the following files
1. [Main file route handler](common/utils/checkStarterRoute.js)
2. Include the store file module [routerModule](common/store/modules/routerModule.js) into the app store modules

### Locales
1. Execute
2. Include in main.js file [initLocale](common/utils/initLocale.js)
3. Include [i18n File](common/i18n.js)
4. Init the locales in the event `beforeMount`
    ```js
   beforeMount()
   {
        initLocale(this.$store, this.$el, this.$i18n)
   }
   ```
5. 
## Main directory
[Common Data](common)

1. [Components](common/components) Is recommended to write componentes that can be used in other modules in a default state.
2. [Store Modules](common/store/modules) Recommended to write shared store behavior
    1. To update top title and top actions in page is recommended to include different components and store modules in the app store
        1. [ContentTitle Component](common/components/ContentTitle.vue)
       2. [PageActions Component](common/components/PageActions.vue)
       3. [ContentTitle Store module](common/store/modules/contentTitleModule.js)
