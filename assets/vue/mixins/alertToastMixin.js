export const alertToastMixin = {
  data() {
    return {
      translationsVue, // Variable global que contiene las traducciones de Vue
    };
  },

  methods: {
    alertSuccesSave() {
      this.$toast.open({
        message: translationsVue.save_success,
        type: 'success',
        duration: 5000,
        position: 'top-right',
      });
    },

    alertErrorSave() {
      this.$toast.open({
        message: this.translationsVue.error_success,
        type: 'error',
        duration: 5000,
        position: 'top-right',
      });
    },


    alertWarning(message) {
      this.$toast.open({
        message: message,
        type: 'warning',
        duration: 5000,
        position: 'top-right',
      });
    },
  },
};
