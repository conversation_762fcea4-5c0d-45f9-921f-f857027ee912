<template>
  <home
    title="ITINERARY.HOME.TITLE"
    description="ITINERARY.HOME.DESCRIPTION"
    :allow-filters="true"
    @apply-filters="applyFilters()"
    @clear-filters="clearFilters()"
  >
    <template v-slot:content-actions>
      <button type="button" class="btn btn-primary" @click="newItinerary()">
        <i class="fa fa-plus mr-2"></i>{{ $t("ITINERARY.NEW_ITINERARY") }}
      </button>
      <button type="button" class="btn btn-primary" @click="downloadExcel()">
        <i class="fa fa-download mr-2"></i>{{ $t("DOWNLOAD_REPORT") }}
      </button>
    </template>
    <template v-slot:content-filters>
      <div class="row w-100 pb-2">
        <div class="col-xs-12 col-4">
          <label for="course-selector">{{ $t("FILTER.SELECT_COURSE") }}</label>
          <select
            class="multiselect"
            id="course-selector"
            aria-label="Select a course"
            v-model="courseId"
          >
            <option
              v-for="course in courses"
              :key="course.id"
              :value="course.id"
            >
              {{ course.code }} - {{ course.name }}
            </option>
          </select>
        </div>
        <div class="col-xs-12 col-4">
          <label for="course-selector">Tags</label>
          <Multiselect
            v-model="tags"
            :options="tagList"
            :multiple="true"
            track-by="id"
            label="name"
            placeholder=""
            selectLabel=""
            selectedLabel=""
            deselectLabel=""
            :close-on-select="false"
            :searchable="false"
          >
          </Multiselect>
        </div>
        <div class="col-xs-12 col-4">
          <label for="active-selector">{{
            $t("ITINERARY.LABEL.PLURAL")
          }}</label>
          <select
            class="multiselect"
            id="active-selector"
            aria-label="Select itinerary active"
            v-model="active"
          >
            <option value="">{{ $t("ITINERARY.ALL") }}</option>
            <option value="active">{{ $t("ITINERARY.ACTIVE") }}</option>
            <option value="inactive">{{ $t("ITINERARY.INACTIVE") }}</option>
          </select>
        </div>
      </div>
    </template>
    <template v-slot:content-main>
      <div
        v-if="loading"
        class="d-flex align-items-center justify-content-center"
      >
        <spinner />
      </div>
      <div v-else>
        <table class="table">
          <thead>
            <tr>
              <th>{{ $t("ITINERARY.LABEL.SORT") }}</th>
              <th>{{ $t("NAME") }}</th>
              <th class="text-center">{{ $t("ITINERARY.TOTAL_COURSES") }}</th>
              <th>{{ $t("CREATED_BY") }}</th>
              <th class="text-center">{{ $t("ACTIVE") }}</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(itinerary, index) in itineraries"
              draggable
              @dragstart="startDrag($event, index)"
              @drop="onDrop(index)"
              @dragover.prevent
              @dragenter.prevent
            >
              <td><i class="fa fa-bars order"></i></td>
              <td>
                <a :href="itinerary.viewHref" class="link">{{
                  itinerary.name
                }}</a>
              </td>
              <td class="text-center">{{ itinerary.totalCourses }}</td>
              <td>{{ itinerary.firstName }} {{ itinerary.lastName }}</td>
              <td class="text-center">
                <div class="custom-control custom-switch">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    :id="'switch_itinerary_' + itinerary.id"
                    v-model="itinerary.active"
                    @change="activateItinerary(itinerary)"
                  />
                  <label
                    class="custom-control-label"
                    :for="'switch_itinerary_' + itinerary.id"
                  ></label>
                </div>
              </td>
              <td>
                <div class="dropdown">
                  <button
                    class="btn btn-default"
                    type="button"
                    :id="`dropdown-menu-${itinerary.id}`"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    <i class="fa fa-ellipsis-h"></i>
                  </button>
                  <ul
                    class="dropdown-menu"
                    :aria-labelledby="`dropdown-menu-${itinerary.id}`"
                  >
                    <li>
                      <a
                        class="dropdown-item"
                        @click="cloneItinerary(itinerary.id)"
                        >{{ $t("CLONE") }}</a
                      >
                    </li>
                    <li>
                      <a class="dropdown-item" :href="itinerary.editHref">{{
                        $t("EDIT")
                      }}</a>
                    </li>
                    <li>
                      <a class="dropdown-item" :href="itinerary.viewHref">{{
                        $t("VIEW")
                      }}</a>
                    </li>
                    <li>
                      <a
                        class="dropdown-item delete"
                        @click="deleteItinerary(itinerary)"
                        >{{ $t("DELETE") }}</a
                      >
                    </li>
                  </ul>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="4" v-if="itineraries.length === 0">
                <base-not-result />
              </td>
            </tr>
          </tbody>
        </table>
        <pagination
          :prop-current-page="page"
          :total-items="totalItems"
          @current-page="onCurrentPage"
          :pageSize="pageSize"
        />
      </div>
    </template>
  </home>
</template>

<script>
import { get, sync } from "vuex-pathify";
import Home from "../../base/Home.vue";
import Multiselect from "vue-multiselect";
import Spinner from "../../admin/components/base/Spinner.vue";
import Pagination from "../../admin/components/Pagination.vue";
import BaseNotResult from "../../base/BaseNotResult.vue";
import searchMixin from '../../mixins/searchMixin';
import TaskQueueService from "../../common/services/TaskQueueService";

export default {
  name: "HomeView",
  components: { BaseNotResult, Pagination, Home, Multiselect, Spinner },
  mixins: [searchMixin],

  data() {
    return {
      dragItem: null,
      dropItem: null,
      pageSize: 25,
    };
  },

  computed: {
    loading: get("itineraryModule/loading"),
    itineraries: get("itineraryModule/itineraries"),
    page: sync("itineraryModule/pagination@page"),
    totalItems: get("itineraryModule/pagination@totalItems"),
    courses: get("itineraryModule/courses"),
    courseId: sync("itineraryModule/filters@courseId"),
    tagList: sync("itineraryModule/tags"),
    tags: sync("itineraryModule/filters@tags"),
    active: sync("itineraryModule/filters@active"),
    urlNewItinerary: get("configModule/config@urlNewItinerary"),
  },
  created() {
    this.$store.dispatch("contentTitleModule/addRoute", {
      routeName: this.$route.name,
      params: {
        linkName: this.$t("ITINERARY.LABEL.PLURAL"),
        params: {},
      },
    });

    this.getItineraries();
    this.getFilters();
  },
  methods: {
    async cloneItinerary(itineraryId) {
      await this.$store.dispatch("itineraryModule/cloneItinerary", {
        endpoint: `/admin/itinerary/cloneItinerary`,
        requestData: { itineraryId: itineraryId },
      });
      this.getItineraries();
    },
    startDrag(evt, index) {
      this.dragItem = index;
      evt.dataTransfer.dropEffect = "move";
      evt.dataTransfer.effectAllowed = "move";
    },

    async onDrop(index) {
      let data = this.orderNewSort(this.dragItem, index);

      this.dropItem = this.itineraries.splice(this.dragItem, 1)[0];
      this.itineraries.splice(index, 0, this.dropItem);
      this.dropItem = null;

      await this.$store.dispatch("itineraryModule/changeSort", {
        endpoint: `/admin/itinerary/changeSort`,
        requestData: { itineraries: data },
      });

      this.setNewSort(data);
    },
    setNewSort(data) {
      data.forEach((item) => {
        const index = this.itineraries.findIndex(
          (itinerary) => itinerary.id === item.id
        );
        this.itineraries[index].sort = item.newSort;
      });
    },
    //antIndexSort, newIndexSort--> posición en el vector que se muestra va de 0,9
    //sortPos, newSortPos--> sort actual en la base de datos
    orderNewSort(antIndexSort, newIndexSort) {
      let newSortItineraries = [];
      let sortPos = this.itineraries[antIndexSort].sort;
      let newSortPos = this.itineraries[newIndexSort].sort;

      if (sortPos > newSortPos) {
        newSortItineraries = this.sortMayorMenor(
          sortPos,
          newSortPos,
          antIndexSort,
          newIndexSort
        );
      }

      if (sortPos < newSortPos) {
        newSortItineraries = this.sortMenorMayor(
          sortPos,
          newSortPos,
          antIndexSort,
          newIndexSort
        );
      }

      return newSortItineraries;
    },
    sortMenorMayor(sortPos, newSortPos, antIndexSort, newIndexSort) {
      let newSortItineraries = [];

      let elmentTras = {
        id: this.itineraries[antIndexSort].id,
        newSort: newSortPos,
      };
      newSortItineraries.push(elmentTras);

      for (let index = newIndexSort; index > antIndexSort; index--) {
        let element = {
          id: null,
          newSort: null,
        };
        element.id = this.itineraries[index].id;
        element.newSort = this.itineraries[index].sort - 1;

        newSortItineraries.push(element);
      }

      return newSortItineraries;
    },
    sortMayorMenor(sortPos, newSortPos, antIndexSort, newIndexSort) {
      let newSortItineraries = [];

      let elmentTras = {
        id: this.itineraries[antIndexSort].id,
        newSort: newSortPos,
      };
      newSortItineraries.push(elmentTras);

      for (let index = newIndexSort; index < antIndexSort; index++) {
        let element = {
          id: null,
          newSort: null,
        };
        element.id = this.itineraries[index].id;
        element.newSort = this.itineraries[index].sort + 1;

        newSortItineraries.push(element);
      }

      return newSortItineraries;
    },

    onCurrentPage(page) {
      this.page = page;
      this.getItineraries();
    },
    newItinerary() {
      window.location.href = this.urlNewItinerary;
    },
    applyFilters() {
      this.page = 1;
      this.getItineraries();
    },
    clearFilters() {
      this.courseId = null;
      this.tags = [];
      this.active = "";
      this.getItineraries();
    },
    getItineraries() {
      this.$store.dispatch("itineraryModule/getItineraries");
    },
    getFilters() {
      if (this.courses.length === 0)
        this.$store.dispatch("itineraryModule/getAvailableCourses");
      if (this.tagList.length === 0)
        this.$store.dispatch("itineraryModule/getAvailableTags");
    },
    deleteItinerary(itinerary) {

      if(itinerary.active){
        this.$alertify.alert(this.$t("ITINERARY.DELETE.CONFIRM.TITLE"),
            this.$t("ITINERARY.DELETE.CONFIRM.VALIDATION"));
        return;
      }

      this.$alertify.confirmWithTitle(
        this.$t("ITINERARY.DELETE.CONFIRM.TITLE"),
        this.$t("ITINERARY.DELETE.CONFIRM.DESCRIPTION"),
        () => {
          this.$store
            .dispatch("itineraryModule/deleteItinerary", itinerary.id)
            .then((res) => {
              const { error } = res;
              if (error)
                this.$toast.error(this.$t("ITINERARY.DELETE.FAILED") + "");
              else {
                this.$toast.error(this.$t("ITINERARY.DELETE.SUCCESS") + "");
              }
            });
        },
        () => {}
      );
    },

    downloadExcel() {
      TaskQueueService.enqueueTask({
        url: '/admin/itineraries_exports',
        method: 'GET',
        data: {
          courseId: this.courseId || 0,
          tags: this.tags.map((tag) => tag.id),
        },
        messages: {
          success: this.$t('ITINERARY.EXPORT.SUCCESS'),
          error: this.$t('ITINERARY.EXPORT.FAILED')
        },
        toast: this.$toast
      });
    },

    activateItinerary(itinerary) {
      this.$store
        .dispatch("itineraryModule/activateItinerary", {
          id: itinerary.id,
          active: itinerary.active,
        })
        .then((res) => {
          const { error } = res;
          if (error) this.$toast.error(this.$t("SURVEY.ACTIVATE.FAILED") + "");
        });
    },
    onSearch(searchValue) {
      this.$store.dispatch("itineraryModule/handleSearch", searchValue);
    },
  },
};
</script>

 <style scoped lang="scss">
@import "vue-multiselect/dist/vue-multiselect.min.css";
.link {
  color: var(--color-primary);
  cursor: pointer;
}

#course-selector,
#active-selector {
  border: 1px solid #e8e8e8;
  border-radius: 5px;
  padding-right: 0.5rem;
}
.order {
  color: var(--color-primary);
}
</style>
