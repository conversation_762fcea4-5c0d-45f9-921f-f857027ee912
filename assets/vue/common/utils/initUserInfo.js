import $ from 'jquery';

export default (store, el, i18n = null) => {
    const rolesDomElement = $('#roles');
    if (rolesDomElement.length > 0) {
        const userRoles = JSON.parse(rolesDomElement.attr('roles'));
        rolesDomElement.remove();
        store.dispatch('userModule/setUserRoles', userRoles);
    }

    const userDomElement = $('#user');
    if (userDomElement.length > 0) {
        const user = JSON.parse(userDomElement.attr('user'));
        userDomElement.remove();
        store.dispatch('userModule/setUser', user);
    }
}
