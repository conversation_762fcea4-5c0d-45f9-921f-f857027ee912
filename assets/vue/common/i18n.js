import VueI18n from 'vue-i18n';
import Vue from 'vue';
import loadLocales from '../loadLocales';
import axios from 'axios';

Vue.use(VueI18n);

function defaultLocale(store) {
  return store.getters['localeModule/getDefaultLocale'] || 'en';
}

function userLocale(store) {
  return store.getters['localeModule/getUserLocale'];
}

/**
 *
 * @param store Is required for the store to include the common localeModule
 * @returns {VueI18n}
 */
export default (store) => {
  return new VueI18n({
    locale: userLocale(store) ?? defaultLocale(store),
    fallbackLocale: defaultLocale(store),
    messages: loadLocales,
  });
};

export function getI18n(locale = 'en', fallbackLocale = 'en') {
  return new VueI18n({
    locale,
    fallbackLocale,
    messages: loadLocales,
  });
}

const fetchLocales = async () => {
  try {
    const { data } = await axios.get('/admin/languages');
    return data.data;
  } catch (e) {
    return {};
  }
};

export async function getI18nApi() {
  const { locales, defaultLanguage, locale } = await fetchLocales();
  const i18n = new VueI18n({
    locale,
    fallbackLocale: defaultLanguage,
    messages: loadLocales,
  });

  return { i18n, locales, locale };
}
