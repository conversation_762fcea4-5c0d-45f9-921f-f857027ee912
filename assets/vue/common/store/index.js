import Vue from 'vue';
import Vuex from 'vuex';
import pathify from "vuex-pathify";
Vue.use(Vuex);

import configModule from "./modules/configModule";
import contentTitleModule from "./modules/contentTitleModule";
import errorModule from "./modules/errorModule";
import froalaEditorModule from "./modules/froalaEditorModule";
import loaderModule from "./modules/loaderModule";
import localeModule from "./modules/localeModule";
import routerModule from "./modules/routerModule";
import userModule from "./modules/userModule";

function initStore(
    {
        state = {},
        getters = {},
        mutations = {},
        actions = {},
        modules = {}
    }) {
    return new Vuex.Store({
        plugins: [pathify.plugin],
        state: {
            ...state,
        },
        getters: {
            ...getters,
        },
        mutations: {
            ...mutations,
        },
        actions: {
            ...actions
        },
        modules: {
            configModule,
            contentTitleModule,
            errorModule,
            froalaEditorModule,
            loaderModule,
            localeModule,
            routerModule,
            userModule,
            ...modules
        }
    });
}

export default initStore;
