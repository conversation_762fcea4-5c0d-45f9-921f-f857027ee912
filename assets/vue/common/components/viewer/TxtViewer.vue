<template>
  <div class="TxtViewer">
    <VueDocPreview :url="url" type="text"/>
  </div>
</template>

<script>
import VueDocPreview from 'vue-doc-preview';

export default {
  name: "TxtViewer",
  components: { VueDocPreview },
  props: {
    src: {
      type: String,
      required: true
    }
  },
  computed: {
    url() {
      const domain = window.location.origin;
      return `${domain}/${this.src}`;
    }
  },
}
</script>

 <style scoped lang="scss"> 

</style>
