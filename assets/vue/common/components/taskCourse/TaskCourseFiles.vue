<template>
  <div class="TaskCourseFiles">
    <div class="w-100 d-flex align-items-center justify-content-end pt-1 pb-1">
      <button class="btn btn-primary" @click="openModal()">{{ $t('FILE_UPLOAD.UPLOAD_FILES') }}</button>
    </div>
    <div class="d-flex align-items-center justify-content-center" v-if="loadingFiles">
      <loader :is-loaded="loadingFiles"/>
    </div>
    <div class="table-container table-responsive" v-show="!loadingFiles">
      <table class="table datagrid">
        <thead>
        <tr>
          <th>{{ $t('FILE') }}</th>
          <th class="text-center">{{ $t('DOWNLOADABLE') }}</th>
          <th class="text-center">{{ $t('FILE_UPLOAD.FILE_TYPE') }}</th>
          <th class="text-center">{{ $t('CREATED_AT') }}</th>
          <th class="text-center">{{ $t('ACTIONS') }}</th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(file, index) in files" :key="file.id">
          <td>{{ file.originalName }} {{ openIndex }}</td>
          <td class="text-center">
            <div class="custom-control custom-switch">
              <input type="checkbox" class="custom-control-input" :id="'switch_' + file.id"
                     v-model="file.isDownload" @change="setDownloadable(file)">
              <label class="custom-control-label" :for="'switch_' + file.id"></label>
            </div>
          </td>
          <td class="text-center" style="max-width: 20%">{{ file.mimeType }}</td>
          <td class="text-center text-nowrap">
            {{ file.createdAt }}
          </td>
          <td class="d-flex flex-row justify-content-center align-items-center">
            <button type="button" class="btn btn-sm btn-info" @click="openTaskFile(index)"><i class="fa fa-eye"></i> </button>
            <button type="button" class="btn btn-sm btn-danger ml-1" @click="deleteFile(file)"><i class="fa fa-trash"></i></button>
            <viewer :custom="true" v-if="openIndex === index" :file="file" :base-path="basePath" :modal="true" :open="openIndex === index" @close="openIndex = -1" />
          </td>
        </tr>
        </tbody>
      </table>
    </div>

<!--    Modal -->
    <div class="modal fade" id="taskCourseFileModal" tabindex="-1" aria-labelledby="taskCourseFileModal" aria-hidden="true">
      <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{{ $t('FILE_UPLOAD.UPLOAD_FILES') }}</h5>
            <button type="button" class="btn-close btn-close-white" @click="closeModal()" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="col-12">
              <add-task-course-file :id="taskCourseId"
                                    @cancel="closeModal()"
                                    @success="onUploadSuccess()"
              ></add-task-course-file>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Loader from "../../../admin/components/Loader.vue";
import AddTaskCourseFile from "./AddTaskCourseFile.vue";
import $ from "jquery";
import PdfViewer from "../viewer/PdfViewer.vue";
import Viewer from "../viewer/Viewer.vue";

export default {
  name: "TaskCourseFiles",
  components: {Viewer, PdfViewer, AddTaskCourseFile, Loader},
  props: {
    taskCourseId: {
      type: Number|String,
      required: true
    }
  },
  data() {
    return {
      loadingFiles: true,
      files: [],
      basePath: '',
      openIndex: -1
    };
  },
  created() {
    this.loadFiles();
    this.$store.dispatch('taskCourseModule/loadAllowedFileTypes');
  },
  methods: {
    openTaskFile(index) {
      this.openIndex = index;
    },
    loadFiles() {
      this.loadingFiles = true;
      this.$store.dispatch('taskCourseModule/loadTaskFiles', this.taskCourseId).then(res => {
        const {data, error, basePath } = res;
        this.files = data;
        this.files.forEach(item => {
          item.view = false;
        })
        this.basePath = basePath;
      }).finally(() => {
        this.loadingFiles = false;
      });
    },

    setDownloadable(file) {
      this.$store.dispatch('taskCourseModule/setFileTaskDownloadable', { id: file.id, downloadable: file.isDownload }).then(res => {
        const { error } = res;
        if (error) this.$toast.error(this.$t('TASK_COURSE.FILE.SET_DOWNLOADABLE.FAILED') + '')
      })
    },

    deleteFile(file) {
      this.$alertify.confirmWithTitle(
          this.$t('FILE_UPLOAD.CONFIRM_DELETE.TITLE'),
          this.$t('FILE_UPLOAD.CONFIRM_DELETE.DESCRIPTION'),
          () => {
            this.$store.dispatch('taskCourseModule/deleteFileTask', file.id).then(res => {
              const { error } = res;
              if (error) this.$toast.error(this.$t('FILE_UPLOAD.DELETE.FAILED', [file.originalName]) + '');
              else {
                this.$toast.success(this.$t('FILE_UPLOAD.DELETE.SUCCESS', [file.originalName]) + '');
                const index = this.files.findIndex(item => item.id === file.id);
                if (index >= 0) this.files.splice(index, 1);
              }
            })
          },
          () => {},
      );
    },

    openModal() {
      $('#taskCourseFileModal').modal({
        show  : true,
        static  : true,
        backdrop: false,
        keyboard: false
      });
    },

    closeModal() {
      $('#taskCourseFileModal').modal('hide');
    },

    onUploadSuccess() {
      this.closeModal();
      this.loadFiles();
    }
  }
}
</script>

 <style scoped lang="scss"> 

</style>
