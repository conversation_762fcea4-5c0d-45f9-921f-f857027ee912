<template>
  <div class="Filters">
    <div class="col-12 form-group mb-2 mr-sm-2 p-0 d-flex flex-row flex-nowrap">
      <input
          v-model="query"
          type="text"
          class="form-control flex-grow-1 mr-2"
          id="search"
          :placeholder="$t(`${i18nPrefix}.PLACEHOLDER.FILTER_QUERY`)"
          aria-describedby="btn-filter"
          v-on:keyup.enter="search()"
      />
      <button type="button" class="btn btn-primary" id="btn-filter" @click="showFilters = !showFilters"><i class="fa fa-filter"></i> {{ $t('FILTERS') }}</button>
    </div>

    <div
        v-if="showFilters && loading"
        class="col-12 d-flex justify-content-center align-items-center"
    >
      <spinner />
    </div>

    <template v-if="showFilters">
      <div
          class="col-md-6 mb-2"
          v-for="(filter, index) in filters"
          :key="index"
      >
        <Multiselect
            v-model="filterQueries[filter.name]"
            :options="filter.filters"
            :multiple="true"
            :placeholder="$t(`${i18nPrefix}.FIND_BY`) + ': ' + filter.name"
            track-by="name"
            label="name"
        ></Multiselect>
      </div>
      <div
          class="col-md-6 mb-2"
          v-for="(sfilter, index) in statusFilters"
          :key="index+100"
      >
        <Multiselect
            v-model="statusQuery[sfilter.name]"
            :options="sfilter.filters"
            :multiple="false"
            :placeholder="$t(`${i18nPrefix}.FIND_BY`) + ': ' + sfilter.name"
            track-by="name"
            label="name"
        ></Multiselect>
      </div>
      
    </template>

    <div
        v-if="showFilters && !loading"
        class="col-12 d-flex align-items-center justify-content-center"
    >
      <button type="button" class="btn btn-primary mb-2" @click="search()">
        {{ $t("APPLY_FILTERS") }}
      </button>

      <button type="button" class="btn btn-warning ml-1 mb-2" @click="cleanFilters()">
        {{ $t("CATALOG.SETTING.CLEAR_FILTERS") }}
      </button>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Multiselect from "vue-multiselect";
import Spinner from "../../../admin/components/base/Spinner.vue";

export default {
  name: "Filters",
  components: {Spinner, Multiselect},
  props: {
    prefix: {
      type: String,
      default: null
    },
    source: {
      type: String,
      default: '/admin/itinerary/available-filters'
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: true,
      query: '',
      showFilters: false,
      filters: [],
      statusFilters:[],
      filterQueries: {},
      statusQuery: {},
    };
  },
  computed: {
    innerValue: {
      get() {

      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    },
    i18nPrefix() {
      return this.prefix != null && this.prefix.length > 0 ? `${this.prefix}.FILTERS` : 'FILTERS';
    }
  },
  watch: {
    value: {
      handler: function (val) {
        this.filterQueries = this.value.filters;
        this.statusQuery = this.value.statusFilters;
        this.query = this.value.searchQuery;
      },
      deep: true
    }
  },
  created() {
    this.getFilters();
  },
  methods: {
    search() {
      this.innerValue = {
        searchQuery: this.query,
        filters: this.filterQueries,
        statusFilters: this.statusQuery
      };
      this.$emit('search');
    },
    cleanFilters() {
      this.query = '';
      this.filterQueries = {};
      this.statusQuery = {};

      this.innerValue = {
        searchQuery: '',
        filters: {},
        statusFilters: {},
      };
      this.$emit('search');
    },
    getFilters() {
      if (this.source.length < 1) return;
      this.loading = true;
      axios.get(this.source).then((r) => {
        const { data } = r.data;
        this.filters = data.filters;
        this.statusFilters = data.statusFilters;        
      }).finally(() => {
        this.loading = false;
        this.search();
      });
    }
  }
}
</script>

<style src="../../../../../node_modules/vue-multiselect/dist/vue-multiselect.min.css"></style>
 <style scoped lang="scss"> 
.Filters {
  display: flex;
  flex-flow: row wrap;
}
</style>
