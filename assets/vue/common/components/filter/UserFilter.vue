<template>
  <div class="UserFilter">
    <filters v-model="filters"
             :source="urlFilters"
             @search="applyFilters()"
             :prefix="prefix"/>
    <div class="UserFilter--error" v-if="group && usersPerGroup === 0">
      {{ prefix ? $t(prefix + '.COMPONENT.USER_FILTER.USERS_PER_GROUP_0') : $t('COMPONENT.USER_FILTER.USERS_PER_GROUP_0') }}
    </div>

    <div class="UserFilter--selector Groups" v-if="group">
      <div class="UserFilter--source Groups--source">
        <div class="Groups--source--header">
          <div class="d-flex flex-column">
            <span><b>{{ $t("RESULTS_WITH_VALUE", [paginated ? pagination.totalItems : available.length])}}</b></span>
            <button type="button" class="btn text-danger ml-auto" @click="clearSourceElements()">
              {{ $t("CLEAR_RESULTS")}}
            </button>
          </div>
          <div class="form-check">
            <label class="form-check-label" for="flexCheckDefault">
              {{ $t('COMPONENT.USER_FILTER.SELECT_ALL') }}
            </label>
            <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault" v-model="selectAll">
          </div>
        </div>

        <div class="d-flex align-items-center justify-content-center" v-if="loadingSource">
          <spinner />
        </div>

        <div class="content" v-else>
          <div class="card option" v-for="user in available" :key="user.id" :class="getCardColor(user)">
            <div class="card-information">
              <span class="card-firstName">
                <span class="iconContainer">
                  <i :class="getCardIcon(user)"></i>
                </span>
                {{ user.firstName }} {{ user.lastName }}
              </span>
              <span class="card-email">{{ user.email }}</span>
            </div>

            <div class="form-check">
              <i v-if="user.isDuplicated || !user.isActive"
                 class="fa fa-info-circle icon-info"
                 data-toggle="tooltip"
                 data-placement="top"
                 :title="$t(getCardTitle(user))"></i>
              <input v-if="!user.isDuplicated" class="form-check-input" type="checkbox" v-model="user.checked" @change="validatePagination">
              <i v-else class="disabled fa fa-square-o"></i>
            </div>
          </div>
        </div>
        <div class="UserFilter--source--pagination" v-if="paginated">
          <pagination
              :key="disablePagination"
              :prop-current-page="pagination.currentPage"
              :total-items="pagination.totalItems"
              :page-size="pagination.pageSize"
              :disabled="disablePagination"
              @current-page="onSourceCurrentPage"
          />
        </div>
      </div>

      <div class="UserFilter--selected Groups--selected">
        <div class="Groups--group" v-for="(groupInfo, index) in innerValue" :key="groupInfo.id">
          <div class="Groups--group--header">
            <span v-if="groupInfo.code">{{ $t('COMPONENT.USER_FILTER.GROUP_AND_NUMBER', [index + 1]) }} ({{groupInfo.code}})</span>
            <span v-else>{{ $t('COMPONENT.USER_FILTER.GROUP_AND_NUMBER', [index + 1]) }}</span>
            <span class="ml-auto"><i class="fa fa-users"></i>
              {{ prefix ? $t(prefix + '.COMPONENT.USER_FILTER.SELECTED_IN_GROUP') : $t('COMPONENT.USER_FILTER.SELECTED_IN_GROUP') }}
            </span>
            <span class="badge bg-default">
              {{ groupInfo.data.length }}/{{ usersPerGroup }}
            </span>
            <button type="button"
                    class="btn text-danger" @click="removeGroup(index)"><i class="fa fa-minus"></i></button>
          </div>
          <div class="Groups--group--actions">
            <button :disabled="disabled"
                    type="button" class="btn btn-primary"
                    @click="addSelectedToGroup(groupInfo.id)"
            >{{ $t('COMPONENT.USER_FILTER.ADD_SELECTED_TO_GROUP') }} <i class="fa fa-caret-right"></i>
            </button>
            <button :disabled="disabled"
                    type="button" class="btn btn-danger ml-auto"
                    @click="removeAllFromGroup(groupInfo.id)"
            ><i class="fa fa-caret-left"></i> {{ $t('COMPONENT.USER_FILTER.REMOVE_ALL') }}
            </button>
          </div>
          <div class="Groups--group--content content">
            <div class="card" v-for="user in groupInfo.data" :key="user.id">
              <div class="card-information">
                <span class="card-firstName">
                  <i v-if="user.isDuplicated"
                     :title ="$t('ANNOUNCEMENT.STATUS.USER.DUPLICATED')"
                     class="fa fa-exclamation-triangle text-warning"></i>
                  {{ user.firstName }} {{ user.lastName }}</span>
                <span class="card-email">{{ user.email }}</span>
              </div>

              <button
                  :disabled="disabled"
                  type="button"
                  class="btn text-danger"
                  @click="removeFromGroup(groupInfo.id, user)"
              >
                <i class="fa fa-minus"></i>
              </button>
            </div>
          </div>
        </div>
        <button :disabled="disabled" type="button" class="btn btn-primary" @click="addGroup()"> {{ $t('COMPONENT.USER_FILTER.NEW_GROUP') }}</button>
      </div>
    </div>



    <!-- Default user selection -->
    <div class="UserFilter--selector" :class="allowAll ? 'All' : ''" v-else>
      <div class="UserFilter--source">
        <div class="w-100 d-flex flex-row flex-nowrap align-items-center mb-2">
          <span><b>{{ $t("RESULTS_WITH_VALUE", [available.length])}}</b></span>
          <button :disabled="disabled" type="button" class="btn text-danger ml-auto" @click="clearSourceElements()">
            {{ $t("CLEAR_RESULTS")}}
          </button>
        </div>

        <div class="d-flex align-items-center justify-content-center" v-if="loadingSource">
          <spinner />
        </div>

        <div class="content" v-else>
          <div class="card" v-for="user in available" :key="user.id">
            <div class="card-information">
              <span class="card-firstName">{{ user.firstName }} {{ user.lastName }}</span>
              <span class="card-email">{{ user.email }}</span>
            </div>

            <button :disabled="disabled" type="button" @click="add(user)" class="ml-auto btn btn-primary">
              <i class="fa fa-plus"></i>
            </button>
          </div>
        </div>

        <div class="UserFilter--source--pagination" v-if="paginated">
          <pagination
              :prop-current-page="pagination.currentPage"
              :total-items="pagination.totalItems"
              :page-size="pagination.pageSize"
              @current-page="onSourceCurrentPage"
          />
        </div>
      </div>
      <div class="UserFilter--actions w-100 d-flex mt-auto mb-auto flex-column p-2" v-if="allowAll">
        <button :disabled="disabled" class="mt-1 btn btn-primary" @click="addAll()">
          {{ $t('ADD_ALL') }}
          <i class="fas fa-angle-double-right ml-2"></i>
        </button>
        <button :disabled="disabled" class="mt-1 btn btn-default" @click="removeAll()">
          <i class="fas fa-angle-double-left mr-2"></i>
          {{ $t("REMOVE_ALL") }}
        </button>
      </div>

      <div class="UserFilter--selected">
        <input type="text" class="form-control mb-2" v-model="selectedQuery">

        <div class="d-flex align-items-center justify-content-center" v-if="loadingSelected">
          <spinner />
        </div>

        <div class="content" v-else>
          <div class="card" v-for="user in selected" :key="user.id">
            <div class="card-information">
              <span class="card-firstName">{{ user.firstName }} {{ user.lastName }}</span>
              <span class="card-email">{{ user.email }}</span>
            </div>

            <button
                :disabled="disabled"
                class="btn text-danger"
                @click="remove(user)"
            >
              <i class="fa fa-minus"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Filters from "./Filters.vue";
import axios from "axios";
import Spinner from "../../../admin/components/base/Spinner.vue";
import BaseNotResult from "../../../base/BaseNotResult.vue";
import Pagination from "../../../admin/components/Pagination.vue";
import {sync} from "vuex-pathify";
import $ from 'jquery'

export default {
  name: "UserFilter",
  components: {Pagination, BaseNotResult, Spinner, Filters },
  props: {
    value: {
      type: Array|Object,
      default: () => ([])
    },
    realtime: {
      type: Boolean,
      default: true
    },
    rest: {
      type: Boolean,
      default: true
    },

    prefix: {
      type: String,
      default: null
    },

    allowAll: {
      type: Boolean,
      default: true
    },


    urlFilters: {
      type: String,
      default: '/admin/itinerary/available-filters'
    },

    urlSource: {
      type: String,
      default: null
    },

    loadSourceOnStart: {
      type: Boolean,
      default: false
    },

    sourceProperty: {
      type: String,
      default: null
    },

    urlAdd: {
      type: String,
      default: null
    },
    urlAddAll: {
      type: String,
      default: null
    },
    urlRemove: {
      type: String,
      default: null
    },
    urlRemoveAll: {
      type: String,
      default: null
    },

    group: {
      type: Boolean,
      default: false
    },

    /**
     * @param usersPerGroup when is set to -1, no user limit in a group, 0 no user allowed, greater than 0, apply user limits per group
     */
    usersPerGroup: {
      type: Number,
      default: -1
    },

    disabled: {
      type: Boolean,
      default: false
    },

    useGlobalEvents: {
      type: Boolean,
      default: false
    },

    paginated: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filters: {},
      available: [],
      loadingSource: false,
      sourceElements: [],
      selectedQuery: '',
      addingElement: false,
      filterActive: false,
      selectAll: false,

      pagination: {
        currentPage: 1,
        totalItems: 0,
        pageSize: 20
      },
      disablePagination: false,
      clickPagination: false
    };
  },
  computed: {
    currentStep: sync("announcementFormModule/steps@current"),
    stepsInformation: sync("announcementFormModule/stepsInformation"),
    alertPrefix() {
      return this.prefix && this.prefix.length > 0 ? `${this.prefix}.USER_FILTER` : 'USER_FILTER';
    },
    innerValue: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    },
    selected() {
      if (!this.group) {
        let query = this.selectedQuery;
        if (query.length > 0) {
          query = this.normalize(query);
          return this.innerValue.filter(selected => {
            return this.normalize(this.name(selected)).includes(query);
          })
        }
      }
      return this.innerValue;
    },
    loadingSelected() {
      return this.addingElement;
    }
  },
  watch: {
    currentStep(newStep) {
      if (this.stepsInformation && this.stepsInformation['titles'] instanceof Map)
      {
        if (this.stepsInformation['titles'].get(newStep) === 'Alumnado') {
          this.loadSourceElements();
        }
      }
    },
    selected: {
      immediate: true,
      deep: true,
      handler: function () {
        this.setAvailableData();
      }
    },
    loadSourceOnStart: {
      handler: function(val) {
        if (this.loadSourceOnStart) this.loadSourceElements();
      },
      immediate: true
    },

    filterActive: {
      handler: function (val, oldVal) {
        this.setAvailableData();
      }
    },

    selectAll: {
      handler: function (val, oldVal) {
        if (!oldVal && val) {
          this.available = this.available.map(obj => ({...obj, checked: !obj.isDuplicated}));
        } else if (oldVal && !val) {
          this.available = this.available.map(obj => ({...obj, checked: false}));
        }
        this.validatePagination()
      }
    }
  },
  mounted() {
    if (this.useGlobalEvents) {
      this.$eventBus.$on('USER_FILTER.EVENT.SEARCH', e => {
        this.loadSourceElements();
      });
    }
  },
  beforeDestroy() {
    if (this.useGlobalEvents) {
      this.$eventBus.$off('USER_FILTER.EVENT.SEARCH');
    }
  },
  methods: {
    onSourceCurrentPage(page) {
      this.clickPagination = true;
      for (const key in this.available) {
        if(this.available[key].checked){
          this.disablePagination = true
          return this.$toast.error(this.$t(`ANNOUNCEMENT.STUDENT.ADD.ALERT`));
        }
      }
      this.clickPagination = false;
      this.disablePagination = false
      this.pagination.currentPage = page;
      this.loadSourceElements();
    },
    showActiveStatus(user){
      return 'isActive' in user;
    },
    getCardIcon(user) {
      if (user?.isDuplicated) return 'fa fa-exclamation-triangle'
      return user.isActive ? 'fa fa-check' : 'fa fa-pause'
    },
    getCardColor(user) {
      if (user?.isDuplicated) return 'danger'
      return user.isActive ? 'success' : 'warning'
    },
    getCardTitle(user) {
      if (user?.isDuplicated) return 'ANNOUNCEMENT.STATUS.USER.DUPLICATED'
      return user.isActive ? 'ACTIVE' : 'ANNOUNCEMENT.STATUS.USER.INACTIVE'
    },
    name(user) {
      if ('name' in user) return user.name;
      return user.firstName + ' ' + user.lastName;
    },
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadSourceElements();
    },
    loadSourceElements() {
      if (!this.urlSource) return;
      this.loadingSource = true;

      if (this.paginated) {
        const url = new URL(this.urlSource);
        url.searchParams.set('page', this.pagination.currentPage);
        url.searchParams.set('page-size', this.pagination.pageSize);
        
        axios.post(url.toString(), this.filters).then(res => {
          const { totalItems, data } = res.data.data;
          this.pagination.totalItems = totalItems;
          this.sourceElements = data;

          this.setAvailableData();
        }).finally(() => {
          this.loadingSource = false;
        });
      } else {
        axios.post(this.urlSource, this.filters).then(res => {
          const { data } = res.data;
          if (this.sourceProperty != null && this.sourceProperty in data) {
            this.sourceElements = data['' + this.sourceProperty];
          } else this.sourceElements = data;

          this.setAvailableData();
        }).finally(() => {
          this.loadingSource = false;
        });
      }
    },

    setAvailableData() {
      let selected = this.innerValue;
      if (this.group) {
        const groups = this.innerValue;
        let available = structuredClone(this.sourceElements);
        groups.forEach(group => {
          available = available.filter(available => {
            return group.data.findIndex(s => s.id === available.id) < 0;
          });
        });
        this.available = available;
      } else {
        this.available =  this.sourceElements.filter(available => {
          return selected.findIndex(s => s.id === available.id) < 0;
        });
      }

      if (this.filterActive){
        this.available =  this.available.filter(available => {
          return available.isActive == this.filterActive;
        });
      }
      this.selectAll = false;
      setTimeout(() => $('[data-toggle="tooltip"]').tooltip(), 100)
    },

    clearSourceElements() {
      this.sourceElements = [];
    },
    async add(user) {
      let success = !this.realtime;
      if (this.realtime) {
        const self = this;
        function save() {
          if (self.rest) return axios.post(`${self.urlAdd}/${user.id}`);
          else return axios.post(self.urlAdd, user);
        }
        this.addingElement = true;
        const result = await save();
        const { error } = result.data;
        success = !error;
        let message = this.getMessage(result.data);
        if (error) {
          this.$toast.error(message ?? this.$t(`${this.alertPrefix}.ADD.FAILED`) + '');
        }
        else {
          this.$toast.success( message ?? this.$t(`${this.alertPrefix}.ADD.SUCCESS`));
          if ('user' in result.data) {
            success = false;
            this.addElement(result.data.user);
          }
        }
      }
      if (success) this.addElement(user);
      this.addingElement = false;
    },
    async remove(user) {
      let success = !this.realtime;
      if (this.realtime) {
        const self = this;
        function save() {
          if (self.rest) return axios.delete(`${self.urlRemove}/${user.id}`);
          else return axios.post(self.urlRemove, user);
        }
        const result = await save();
        const { error } = result.data;
        success = !error;
        let message = this.getMessage(result.data);
        if (error) this.$toast.error(message ?? this.$t(`${this.alertPrefix}.DELETE.FAILED` ) + '');
        else this.$toast.success( message ?? this.$t(`${this.alertPrefix}.DELETE.SUCCESS`));
      }
      if (success) this.removeElement(user);
    },
    async removeFromGroup(groupId, user) {
      const values = structuredClone(this.innerValue);
      let index = values.findIndex(selected => selected.id === groupId);
      if (index < 0) return;
      values[index].data = values[index].data.filter(u => u.id !== user.id);
      this.innerValue = values;
    },
    async addAll() {
      const selected = structuredClone(this.available);
      let success = !this.realtime;
      if (this.realtime) {
        const ids = [];
        selected.forEach(s => {
          ids.push(s.id);
        });

        const result = await axios.post(this.urlAddAll, {data: ids});
        const { error } = result.data;
        success = !error;

        let message = this.getMessage(result.data);
        if (error) this.$toast.error(message ?? this.$t(`${this.alertPrefix}.ADD.FAILED`) + '');
        else this.$toast.success( message ?? this.$t(`${this.alertPrefix}.ADD.SUCCESS`));
      }

      if (success) {
        selected.forEach(user => {
          this.addElement(user);
        });
        this.$emit('refresh');
      }
    },
    removeAll() {
      this.$alertify.confirmWithTitle(
          this.$t(`${this.alertPrefix}.DELETE_ALL.CONFIRM.TITLE`),
          this.$t(`${this.alertPrefix}.DELETE_ALL.CONFIRM.DESCRIPTION`),
          async () => {
            let success = !this.realtime;
            if (this.realtime) {
              const result = await axios.post(this.urlRemoveAll);
              const { error } = result.data;
              success = !error;

              let message = this.getMessage(result.data);
              if (error) this.$toast.error(message ?? this.$t(`${this.alertPrefix}.DELETE.FAILED`));
              else this.$toast.success( message ?? this.$t(`${this.alertPrefix}.DELETE.SUCCESS`)); 
            }

            if (success) {
              this.innerValue = [];
              this.$emit('refresh');
            }
          },
          () => {},
      )
    },

    addGroup() {
      this.disablePagination = false;
      const groups = structuredClone(this.innerValue);
      groups.push({ id: 'local_' + (groups.length + 1), data: []});
      this.innerValue = groups;
    },

    removeGroup(index) {
      const infoTitle = this.prefix ? `${this.prefix}.COMPONENT.USER_FILTER.REMOVE_GROUP_INFO` : 'COMPONENT.USER_FILTER.REMOVE_GROUP_INFO';
      this.$alertify.confirmWithTitle(
          this.$t('COMPONENT.USER_FILTER.REMOVE_GROUP'),
          this.$t(infoTitle),
          () => {
            const groups = this.innerValue;
            groups.splice(index, 1);
            this.innerValue = groups;
          },
          () => {}
      );
    },

    addSelectedToGroup(groupId) {
      if (this.usersPerGroup === 0) {
        this.$toast.clear();
        const warning = this.prefix ? `${this.prefix}.COMPONENT.USER_FILTER.USERS_PER_GROUP_0` : 'COMPONENT.USER_FILTER.USERS_PER_GROUP_0';
        this.$toast.warning(this.$t(warning) + '');
        return;
      }

      let selected = this.available.filter(a => a.checked);
      const groups = this.innerValue;
      const index = groups.findIndex(g => g.id === groupId);
      if (index >= 0) {
        let size = selected.length + groups[index].data.length;
        if (this.usersPerGroup > 0 && size > this.usersPerGroup) {
          const warning = this.prefix ? `${this.prefix}.COMPONENT.USER_FILTER.MAX_USERS_PER_GROUP_EXCEEDED` : 'COMPONENT.USER_FILTER.MAX_USERS_PER_GROUP_EXCEEDED';
          this.$toast.warning(this.$t(warning) + '');
          const currentGroupSize = groups[index].data.length;
          const maxIndex = this.usersPerGroup - currentGroupSize;
          selected = selected.slice(0, maxIndex);
        }

        selected = selected.map(obj => ({...obj, group_id: groupId}));
        let data = groups[index].data;
        data = [...data, ...selected];
        groups[index].data = data;
      }

      this.innerValue = groups;
      this.selectAll = false;
    },

    removeAllFromGroup(groupId) {
      const groups = this.innerValue;
      const index = groups.findIndex(g => g.id === groupId);
      if (index < 0) return;
      groups[index].data = [];
      this.innerValue = groups;
    },
    addAllToGroup(groupId) {},

    addElement(item) {
      const values = structuredClone(this.innerValue);
      values.push(item);
      this.innerValue = values;
    },

    removeElement(item) {
      const values = structuredClone(this.innerValue);
      const index = values.findIndex(selected => selected.id === item.id);
      if (index >= 0) {
        values.splice(index, 1);
        this.innerValue = values;
      }
    },
    normalize(string) {
      return string
          .toLowerCase()
          .normalize("NFD")
          .replace(/[\u0300-\u036f]/g, "");
    },

    getMessage(data) {
      let message = null;
      if ('message' in data) message = data.message;
      else if ('data' in data) message = data.data;

      if ('i18n' in data && data.i18n && message && message.length > 0)
        message = this.$t(message);
      return message;
    },
    validatePagination(){
      if(this.clickPagination){
        let allUnChecked = true;
        for (const key in this.available) {
          if(this.available[key].checked){
            allUnChecked = false;
          }
        }
        this.disablePagination = !allUnChecked
        if(!this.disablePagination)
          this.clickPagination = false;
      }
    }
  }
}
</script>

 <style scoped lang="scss"> 
.UserFilter {
  width: 100%;
  padding: 0.1rem;

  &--error {
    border: 1px solid $color-secondary;
    background-color: $color-secondary-lightest;
    color: var(--color-neutral-darkest);
    padding: 1rem;
    text-align: center;
  }

  &--selector {
    width: 100%;
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(2, 1fr);

    &.All {
      grid-template-columns: calc((100% - 180px) / 2) 180px calc((100% - 180px) / 2);
    }
  }

  @mixin mixin--UserFilter--card {
    .card {
      padding: 0.75rem;
      align-items: center;
      display: grid;
      grid-template-columns: 1fr auto;
      column-gap: 0.20rem;
      
      &.option {
        border: solid;
        border-width: 0 0 0 5px;
        
        .iconContainer {
          width: 1.5rem;
          height: 1.5rem;
          display: inline-grid;
          place-content: center;
          border-radius: 2rem;
          font-size: 1rem;
        }
        
        &.success {
          border-color: var(--color-dashboard-1);
          
          .iconContainer {
            color: var(--color-dashboard-1);
          }
        }
        &.warning {
          border-color: var(--color-dashboard-5);
          
          .iconContainer {
            color: white;
            font-size: 0.65rem;
            background-color: var(--color-dashboard-5);
          }
        }
        &.danger {
          background-color: var(--color-neutral-lighter);
          border-color: var(--color-dashboard-4);
          
          .iconContainer {
            color: var(--color-dashboard-4);
          }
        }
        
        .disabled {
          color: var(--color-neutral-mid);
        }
        
        .form-check {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding-left: 0;
          
          .icon-info {
            color: var(--color-primary);
          }
          
          .form-check-input {
            position: relative;
          }
        }
      }

      .card-information {
        width: 100%;
        display: grid;
        gap: 0.125rem;
        font-size: 0.9rem;
        overflow-x: hidden;

        .card-firstName {
          font-weight: 500;
        }

        .card-email {
          font-style: italic;
          color: var(--color-neutral-dark);
        }
      }
    }
  }

  .Groups--source {
    &--header {
      display: flex;
      flex-flow: row nowrap;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: .5rem !important;
      padding: 0 1rem 0 .5rem;

      button {
        padding: 0 !important;
      }
      .form-check {
        margin-left: auto;
        color: var(--color-primary);

        label {
          margin-right: 1rem;
        }
        input {
          margin-top: 5px;
        }
      }
    }
  }

  .Groups--selected {
    max-height: 70vh;
    overflow-y: auto;
    padding: unset;
    display: flex;
    flex-flow: column;
    align-items: flex-start;
    justify-content: flex-start;
    border: unset;
    background: unset;


    .Groups--group {
      width: 100%;
      border: 1px solid var(--color-neutral-mid-light);
      background: var(--color-neutral-lighter);
      border-radius: 5px;
      margin-bottom: 1rem;

      &--header {
        background-color: #212121;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        padding: .5rem 1rem;
        width: 100%;
        color: #FFFFFF;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;

        .fa-users {
          margin-right: .5rem;
        }

        .badge {
          background-color: #cbd5e1;
          margin-left: .5rem;
        }
      }

      &--actions {
        padding: 0.5rem;
        display: flex;
        flex-flow: row wrap;
        align-items: center;
      }

      h1 {
        width: 100%;
        text-align: left;
        font-size: 18px;
        text-transform: uppercase;
      }

      .content {
        height: 220px;
        overflow-y: auto;
        width: 100%;
        display: grid;
        gap: 0.25rem;
        align-content: flex-start;
        padding: 1rem;

        @include mixin--UserFilter--card;
      }
    }
  }

  &--source, &--selected {
    border: 1px solid var(--color-neutral-mid-light);
    border-radius: 5px;
    padding: 0.5rem;
    background: var(--color-neutral-lighter);

    .content {
      width: 100%;
      display: grid;
      gap: 0.25rem;
      align-content: flex-start;
      min-height: 30vh;
      max-height: 70vh;
      overflow-y: auto;

      @include mixin--UserFilter--card;
    }
  }
}
</style>
