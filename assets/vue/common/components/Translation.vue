<template>
  <div class="Translation" :class="direction">
    <div class="Locales w-100">
      <button type="button" :class="innerValue === key ? 'active' : 'inactive'" v-for="(locale, key) in locales" :key="locale" @click="innerValue = key">
        {{ capitalize(locale) }}
        <i v-if="warning[key] ?? false" class="fa fa-exclamation-triangle warning"></i>
      </button>
    </div>

    <div class="Content">
      <slot name="content" />
    </div>
  </div>
</template>

<script>
import {get} from "vuex-pathify";

export default {
  name: "Translation",
  props: {
    value: {
      type: String|Number,
      default: ''
    },
    warning: {
      type: Object,
      default: () => ({})
    },// {'es': true|false}
    direction: {
      type: String,
      default: 'horizontal',
      validator: function(value) {
        return ["horizontal", "vertical"].includes(value);
      }
    }
  },
  computed: {
    innerValue: {
      get() {
        return this.value ?? [];
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    },
    locales: get('localeModule/locales')
  },
  methods: {
    capitalize(value) {
      return value.charAt(0).toUpperCase() + value.slice(1);
    }
  }
}
</script>

 <style scoped lang="scss"> 
.Translation {
  width: 100%;

  &.vertical {
    display: grid;
    grid-template-columns: 150px 1fr;

    .Locales {
      display: grid;
      gap: .5rem;
      padding-left: 1rem;
      padding-right: 1rem;

      button {
        width: 100%;
        padding: 3px 10px;
        &:hover {
          border: none;
        }
        &.active {
          background-color: #ffffff !important;
          border: 1px solid var(--color-primary) !important;
          color: var(--color-primary) !important;
        }
        &.inactive {
          background-color: var(--color-neutral-light) !important;
          border: 1px solid var(--color-neutral-mid) !important;
          color: var(--color-neutral-mid-dark) !important;
        }
      }
    }

    .Content {
      padding-left: 1rem;
      border-left: 1px solid var(--color-neutral-mid);
    }
  }

  &.horizontal {
    .Locales {
      //display: grid;
      //grid-template-columns: repeat(auto-fit, 100px);
      display: flex;
      flex-flow: row nowrap;
      gap: .25rem;
      background-color: #ffffff;
      overflow: auto;

      & > button {
        width: 100px;
        height: 35px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        border-top: 1px solid var(--color-neutral-mid);
        border-left: 1px solid var(--color-neutral-mid);
        border-right: 1px solid var(--color-neutral-mid);
        //&:hover {
        //
        //}

        &.inactive {
          background-color: var(--color-neutral-mid-light);
          border-bottom: 2px solid var(--color-neutral-mid-light);
          color: var(--color-neutral-mid-darker);
        }

        &.active {
          border-bottom: 2px solid var(--color-neutral-mid-light);
          background-color: var(--color-neutral-lighter);
        }
      }
    }
    .Content {
      border: 1px solid var(--color-neutral-mid);
      background-color: var(--color-neutral-lighter);
    }
  }

  button {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: center;
  }

  .warning {
    color: var(--color-warning);
    font-size: 12px;
    margin-left: 3px;
  }
}
</style>
