<script>
import dateTimeFormatterMixin from "../mixins/dateTimeFormatterMixin";

export default {
  name: "DateTimeAndLocal",
  mixins: [dateTimeFormatterMixin],
  props: {
    locale: {
      type: String,
      default: 'es-ES'
    },
    options: {
      type: Object|Array,
      default: () => ({
        hour12: false
      })
    },
    timezone: {
      type: String,
      default: 'Europe/Madrid'
    },
    value: {
      type: Number|String,
      default: null
    }
  }
}
</script>

<template>
<div class="DateTimeAndLocal">
  <div class="DateTimeAndLocal--current-timezone">
    <strong>{{ clientTimezone }}</strong>
    <span>{{ getDateTimeFormatted(value, locale, {timezone: clientTimezone, ...options}) }}</span>
  </div>
  <strong>{{ timezone }}</strong>
  <span>{{ getDateTimeFormatted(value, locale, {timezone: timezone, ...options}) }}</span>
</div>
</template>

<style scoped lang="scss">
.DateTimeAndLocal {
  position: relative;
  display: flex;
  flex-flow: column;

  &--current-timezone {
    position: absolute;
    display: none;
    flex-flow: column;
    z-index: 10;
    background-color: white;
    border: 1px solid var(--color-primary);
    border-radius: 3px;
    bottom: 15px;
    left: 10px;
    padding: 5px 2px;
  }

  &:hover {
    .DateTimeAndLocal--current-timezone {
      display: flex;
    }
  }
}
</style>
