<template>
  <div class="LoginView">
    <LayoutPageTitle
      :links="links"
      name="Login"
    />
    <div class="form">
      <img
        :src="logo"
        class="logo"
        alt="Vite logo"
      />
      <p>Iniciar sesión</p>
      <BaseInput
        v-model="loginData.payload.email"
        name="email"
        label="Usuario"
        :error="loginData.errors?.email"
      />
      <BaseInput
        v-model="loginData.payload.password"
        name="pass"
        label="Contraseña"
        type="password"
        :error="loginData.errors?.password"
      />
      <BaseButton @click="login">Iniciar sesión</BaseButton>
    </div>
  </div>
</template>

<script setup>
import { useLoginView } from '@/contexts/login/composables/LoginView.composable.js'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'
import LayoutPageTitle from '@/contexts/shared/components/LayoutPageTitle.vue'

const { logo, login, links, loginData } = useLoginView()
</script>

<style scoped lang="scss">
.LoginView {
  display: grid;
  place-content: center;
  width: 100svw;
  min-height: calc(100svh);
  background-color: var(--color-neutral-lighter);

  .form {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    width: 300px;
    margin: auto;
    color: var(--color-neutral-darkest);
    border: 1px solid var(--color-neutral-light);
    background-color: var(--color-neutral-lightest);
    border-radius: 3px;
    padding: 2rem 1rem;

    .logo {
      width: auto;
      max-width: 200px;
      height: calc(var(--header-height) - 1rem);
      object-fit: contain;
      object-position: center;
    }

    .BaseButton {
      margin-top: 1rem;
    }

    .BaseInput {
      :deep(input) {
        color: var(--color-neutral-darkest);
        background-color: var(--color-neutral-lightest);
      }
    }

    p {
      margin: auto;
    }
  }
}
</style>
