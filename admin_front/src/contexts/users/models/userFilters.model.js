import { DynamicInputModel } from '@/contexts/shared/models/dynamicInput.model.js'
import { INPUT_TYPES } from '@/contexts/shared/constants/input.constants.js'

export class UserFilters extends DynamicInputModel {
  constructor({ id, name, key, filters, type, value } = {}) {
    super({ id, name, options: filters, type: type || INPUT_TYPES.SELECT, value })
    this.key = key || this.key
  }

  getInputValue() {
    return { key: this.key, value: this.getParsedDataValue(this.value) }
  }
}
