import StorageService from '@/core/services/storage.service.js'
import { User } from '@/contexts/shared/models/user.model.js'
import { stringToJson } from '@/core/utils/misc.utils.js'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '@/contexts/shared/stores/auth.store.js'

export function useAuth() {
  const { isAuth } = storeToRefs(useAuthStore())

  function getJwtPayloadData() {
    const [_, url] = (StorageService.getToken() || '').split('.')
    if (!url) return {}

    const b64 = url.replace(/-/g, '+').replace(/_/g, '/')
    return decodeURIComponent(
      window
        .atob(b64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
        })
        .join('')
    )
  }

  function initUserData() {
    return new User(isAuth.value ? stringToJson(getJwtPayloadData()) : {})
  }

  return {
    isAuth,
    initUserData,
  }
}
