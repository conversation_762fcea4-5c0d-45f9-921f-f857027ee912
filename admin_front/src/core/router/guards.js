import { useAuth } from '@/contexts/shared/composables/auth.composable.js'

export async function initValidations(to, from, next) {
  if (!$settings.AUTH_ACTIVE || to.meta.isPublic) return next()
  const { isAuth } = useAuth()
  if (to.meta.requiresAuth) return isAuth.value ? next() : next({ name: $settings.PAGES.ENTRY_PAGE })

  return isAuth.value ? next({ name: $settings.PAGES.AUTH_DEFAULT }) : next()
}
