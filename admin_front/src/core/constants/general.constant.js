export const BASE_LOCATION = (() => {
  try {
    const baseURL = import.meta.env.VITE_BASE_URL ?? window?.location?.origin ?? ''
    return baseURL[baseURL.length - 1] === '/' ? baseURL : `${baseURL}/`
  } catch (e) {
    return ''
  }
})()

export const API_URL = (() => {
  try {
    return import.meta.env?.VITE_API_URL ?? `${BASE_LOCATION}api`
  } catch (e) {
    return ''
  }
})()

export const REFRESH_TOKEN_URL = '/token/refresh'
export const PUBLIC_ENDPOINTS = [REFRESH_TOKEN_URL, ...($settings.LOCALE_LIST_URL ? [$settings.LOCALE_LIST_URL] : [])]

export const ONE_DAY_IN_MS = 86400
