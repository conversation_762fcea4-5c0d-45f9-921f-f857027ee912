<?php

declare(strict_types=1);

namespace App\Tests\V2\Mother\User;

use App\V2\Application\DTO\User\UserListItemDTO;

class UserListItemDtoMother
{
    /**
     * Create a UserListItemDTO with default or custom values.
     *
     * @param int           $id           User ID (default: 1)
     * @param string|null   $avatar       User avatar URL (default: null)
     * @param string        $email        User email (default: '<EMAIL>')
     * @param string        $firstName    User first name (default: 'John')
     * @param string        $lastName     User last name (default: 'Doe')
     * @param array<string> $roles        User roles (default: ['ROLE_USER'])
     * @param bool          $isActive     Whether the user is active (default: true)
     * @param int|null      $points       User points (default: 0)
     * @param bool          $editable     Whether the user is editable (default: false)
     * @param bool          $deletable    Whether the user is deletable (default: false)
     * @param bool          $allowLoginAs Whether login as the user is allowed (default: false)
     */
    public static function create(
        int $id = 1,
        ?string $avatar = null,
        string $email = '<EMAIL>',
        string $firstName = 'John',
        string $lastName = 'Doe',
        array $roles = ['ROLE_USER'],
        bool $isActive = true,
        ?int $points = 0,
        bool $editable = false,
        bool $deletable = false,
        bool $allowLoginAs = false,
    ): UserListItemDTO {
        return new UserListItemDTO(
            id: $id,
            avatar: $avatar,
            email: $email,
            firstName: $firstName,
            lastName: $lastName,
            roles: $roles,
            isActive: $isActive,
            points: $points,
            editable: $editable,
            deletable: $deletable,
            allowLoginAs: $allowLoginAs,
        );
    }
}
