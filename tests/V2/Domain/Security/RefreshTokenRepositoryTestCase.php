<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Security;

use App\Tests\Mother\Entity\RefreshTokenMother;
use App\V2\Domain\Security\Exceptions\RefreshTokenNotFoundException;
use App\V2\Domain\Security\RefreshTokenCriteria;
use App\V2\Domain\Security\RefreshTokenRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use PHPUnit\Framework\TestCase;

abstract class RefreshTokenRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): RefreshTokenRepository;

    /**
     * @throws InfrastructureException
     * @throws RefreshTokenNotFoundException
     */
    public function testPut(): void
    {
        $token1 = RefreshTokenMother::create(
            refreshToken: 'token1'
        );
        $token2 = RefreshTokenMother::create(
            refreshToken: 'token2'
        );
        $token3 = RefreshTokenMother::create(
            refreshToken: 'token3'
        );

        $repository = $this->getRepository();

        $repository->put($token1);
        $repository->put($token2);
        $repository->put($token3);

        $this->assertEquals(
            $token1,
            $repository->findOneBy(
                RefreshTokenCriteria::createEmpty()
            )
        );

        $this->assertEquals(
            $token3,
            $repository->findOneBy(
                RefreshTokenCriteria::createEmpty()
                    ->filterByToken('token3')
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws RefreshTokenNotFoundException
     */
    public function testDelete(): void
    {
        $token1 = RefreshTokenMother::create(
            refreshToken: 'token1'
        );
        $token2 = RefreshTokenMother::create(
            refreshToken: 'token2'
        );
        $token3 = RefreshTokenMother::create(
            refreshToken: 'token3'
        );

        $repository = $this->getRepository();

        $repository->put($token1);
        $repository->put($token2);
        $repository->put($token3);

        $this->assertEquals(
            $token1,
            $repository->findOneBy(
                RefreshTokenCriteria::createEmpty()
            )
        );

        $repository->delete($token1);

        $this->assertEquals(
            $token2,
            $repository->findOneBy(
                RefreshTokenCriteria::createEmpty()
            )
        );

        $this->expectException(RefreshTokenNotFoundException::class);
        $repository->findOneBy(
            RefreshTokenCriteria::createEmpty()
                ->filterByToken('token1')
        );
    }
}
