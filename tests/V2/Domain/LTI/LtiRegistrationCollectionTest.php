<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\LTI;

use App\Tests\V2\Domain\Shared\Collection\CollectionTestCase;
use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;

class LtiRegistrationCollectionTest extends CollectionTestCase
{
    /**
     * @throws CollectionException
     */
    protected function createCollection(array $items): Collection
    {
        return new LtiRegistrationCollection($items);
    }

    protected function getExpectedType(): string
    {
        return LtiRegistration::class;
    }

    protected function getItem(): object
    {
        return LtiRegistrationMother::create();
    }
}
