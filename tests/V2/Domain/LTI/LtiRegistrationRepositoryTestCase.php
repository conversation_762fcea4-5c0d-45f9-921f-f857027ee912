<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\LTI;

use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\V2\Domain\LTI\Exceptions\LtiException;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationCriteria;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

abstract class LtiRegistrationRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): LtiRegistrationRepository;

    /**
     * @throws InfrastructureException
     * @throws LtiRegistrationNotFoundException
     */
    public function testPut(): void
    {
        $repository = $this->getRepository();

        $registration1 = LtiRegistrationMother::create(
            name: 'registration-1',
        );

        $registration2 = LtiRegistrationMother::create(
            name: 'registration-2',
            clientId: 'registration-2'
        );

        $repository->put($registration1);
        $repository->put($registration1);
        $repository->put($registration2);

        $result = $repository->findBy(
            LtiRegistrationCriteria::createEmpty()
        );

        $this->assertCount(2, $result);

        $found = $repository->findOneBy(
            LtiRegistrationCriteria::createEmpty()
                ->filterById($registration1->getId())
        );

        $this->assertEquals($registration1, $found);

        try {
            $registration3 = LtiRegistrationMother::create();

            $repository->put($registration3);
            $this->fail('Expected exception');
        } catch (LtiException $e) {
            $this->assertEquals(LtiException::clientIdMustBeUnique(), $e);
        }

        $registration1Updated = LtiRegistrationMother::create(
            id: $registration1->getId(),
            name: 'Updated name',
            clientId: $registration1->getClientId(),
        );

        $repository->put($registration1Updated);
        $found = $repository->findOneBy(
            LtiRegistrationCriteria::createEmpty()
            ->filterById($registration1->getId())
        );

        $this->assertNotEquals($registration1, $found);
        $this->assertEquals($registration1->getId(), $found->getId());
    }

    /**
     * @throws InfrastructureException
     */
    #[DataProvider('provideFindBy')]
    public function testFindBy(
        LtiRegistrationCollection $collection,
        LtiRegistrationCriteria $criteria,
        int $expectedCount,
        array $expectedResult,
    ) {
        $repository = $this->getRepository();
        foreach ($collection->all() as $registration) {
            $repository->put($registration);
        }

        $result = $repository->findBy(
            LtiRegistrationCriteria::createEmpty()
        );

        $this->assertCount(0, array_diff($collection->all(), $result->all()));

        $result = $repository->findBy($criteria);
        $this->assertCount($expectedCount, $result);
        $this->assertCount(0, array_diff($expectedResult, $result->all()));
    }

    /**
     * @throws CollectionException
     * @throws InvalidUuidException
     */
    public static function provideFindBy(): \Generator
    {
        $registration1 = LtiRegistrationMother::create(
            name: 'registration-1',
            clientId: 'registration-1'
        );

        $registration2 = LtiRegistrationMother::create(
            name: 'registration-2',
            clientId: 'registration-2'
        );

        yield 'find all' => [
            'collection' => new LtiRegistrationCollection([
                $registration1, $registration2,
            ]),
            'criteria' => LtiRegistrationCriteria::createEmpty(),
            'expectedCount' => 2,
            'expectedResult' => [$registration1, $registration2],
        ];

        yield 'find by id' => [
            'collection' => new LtiRegistrationCollection([
                $registration1, $registration2,
            ]),
            'criteria' => LtiRegistrationCriteria::createEmpty()
                ->filterById($registration1->getId()),
            'expectedCount' => 1,
            'expectedResult' => [$registration1],
        ];

        yield 'find by client id' => [
            'collection' => new LtiRegistrationCollection([
                $registration1, $registration2,
            ]),
            'criteria' => LtiRegistrationCriteria::createEmpty()
                ->filterByClientId('registration-1'),
            'expectedCount' => 1,
            'expectedResult' => [$registration1],
        ];

        yield 'find by search string' => [
            'collection' => new LtiRegistrationCollection([
                $registration1, $registration2,
            ]),
            'criteria' => LtiRegistrationCriteria::createEmpty()
                ->filterBySearchString('registration'),
            'expectedCount' => 2,
            'expectedResult' => [$registration1, $registration2],
        ];
    }

    /**
     * @throws InfrastructureException
     */
    public function testDelete(): void
    {
        $registration1 = LtiRegistrationMother::create(
            name: 'registration-1',
            clientId: 'registration-1'
        );

        $registration2 = LtiRegistrationMother::create(
            name: 'registration-2',
            clientId: 'registration-2'
        );

        $registration3 = LtiRegistrationMother::create(
            name: 'registration-3',
            clientId: 'registration-3'
        );

        $repository = $this->getRepository();

        $repository->put($registration1);
        $repository->put($registration2);
        $repository->put($registration3);

        $all = $repository->findBy(LtiRegistrationCriteria::createEmpty());

        $this->assertCount(3, $all);
        $this->assertCount(
            0,
            array_diff([$registration1, $registration2, $registration3], $all->all())
        );

        $repository->delete($registration2);

        $all = $repository->findBy(LtiRegistrationCriteria::createEmpty());

        $this->assertCount(2, $all);
        $this->assertCount(
            0,
            array_diff([$registration1, $registration3], $all->all())
        );
    }
}
