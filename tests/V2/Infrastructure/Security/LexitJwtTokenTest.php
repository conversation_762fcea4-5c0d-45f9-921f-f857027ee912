<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Security;

use App\Entity\RefreshToken;
use App\Tests\Mother\Entity\UserMother;
use App\V2\Domain\Security\Exceptions\InvalidTokenException;
use App\V2\Domain\Security\Exceptions\RefreshTokenNotFoundException;
use App\V2\Domain\Security\RefreshTokenRepository;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\User\UserRepository;
use App\V2\Infrastructure\Security\LexitJwtToken;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class LexitJwtTokenTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getLexitJwtToken(
        ?JWTManager $jwtManager = null,
        ?RefreshTokenRepository $refreshTokenRepository = null,
        ?UserRepository $userRepository = null,
        int $jwtTokenTtl = 3600,
    ): LexitJwtToken {
        return new LexitJwtToken(
            jwtManager: $jwtManager ?? $this->createMock(JWTManager::class),
            refreshTokenRepository: $refreshTokenRepository ?? $this->createMock(RefreshTokenRepository::class),
            userRepository: $userRepository ?? $this->createMock(UserRepository::class),
            jwtTokenTtl: $jwtTokenTtl,
        );
    }

    /**
     * @throws InfrastructureException
     * @throws InvalidTokenException
     * @throws Exception
     * @throws RefreshTokenNotFoundException
     */
    public function testRefreshTokenFlow(): void
    {
        $user = UserMother::create();
        $storedToken = null;
        $refreshTokenRepository = $this->createMock(RefreshTokenRepository::class);

        $refreshTokenRepository->expects($this->once())
            ->method('put')
            ->willReturnCallback(function (RefreshToken $refreshToken) use ($user, &$storedToken) {
                $this->assertEquals($user->getUserIdentifier(), $refreshToken->getUsername());
                $storedToken = $refreshToken;
            });

        $findOneByCallMatcher = $this->exactly(2);
        $refreshTokenRepository->expects($findOneByCallMatcher)
            ->method('findOneBy')
            ->willReturnCallback(function () use ($findOneByCallMatcher, &$storedToken) {
                if (1 === $findOneByCallMatcher->numberOfInvocations()) {
                    throw new RefreshTokenNotFoundException();
                }

                return $storedToken;
            });

        $refreshTokenRepository->expects($this->once())
            ->method('delete')
            ->willReturnCallback(function (RefreshToken $refreshToken) use (&$storedToken) {
                $this->assertEquals($storedToken->getRefreshToken(), $refreshToken->getRefreshToken());
            });

        $lexit = $this->getLexitJwtToken(
            refreshTokenRepository: $refreshTokenRepository,
        );

        $token = $lexit->getRefreshToken($user);

        // Execution n time in the app who will use the token

        $lexit->validateRefreshToken($token);
    }
}
