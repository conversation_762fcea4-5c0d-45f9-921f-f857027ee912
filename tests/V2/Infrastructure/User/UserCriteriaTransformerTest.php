<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\User;

use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Pagination;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Domain\User\UserCriteria;
use App\V2\Infrastructure\Shared\DateTimeTransformer;
use App\V2\Infrastructure\User\UserCriteriaTransformer;
use DateTimeImmutable;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class UserCriteriaTransformerTest extends TestCase
{
    /**
     * Test that fromArray correctly transforms an array to a UserCriteria object.
     */
    #[DataProvider('criteriaProvider')]
    public function testFromArray(array $data, UserCriteria $expectedCriteria): void
    {
        // Act
        $result = UserCriteriaTransformer::fromArray($data);

        // Assert
        $this->assertInstanceOf(UserCriteria::class, $result);

        // Compare isActive
        $this->assertEquals($expectedCriteria->getIsActive(), $result->getIsActive());

        // Compare dates
        $this->assertDateEquals($expectedCriteria->getStartDate(), $result->getStartDate());
        $this->assertDateEquals($expectedCriteria->getEndDate(), $result->getEndDate());

        // Compare search and role
        $this->assertEquals($expectedCriteria->getSearch(), $result->getSearch());
        $this->assertEquals($expectedCriteria->getRole(), $result->getRole());

        // Compare sort
        $this->assertSortEquals($expectedCriteria->getSortBy(), $result->getSortBy());

        // Compare pagination
        $this->assertPaginationEquals($expectedCriteria->getPagination(), $result->getPagination());

        // Compare hideSuperAdmin
        $this->assertEquals($expectedCriteria->needsToHideSuperAdmin(), $result->needsToHideSuperAdmin());
    }

    /**
     * Helper method to compare two DateTimeImmutable objects.
     */
    private function assertDateEquals(?\DateTimeImmutable $expected, ?\DateTimeImmutable $actual): void
    {
        if (null === $expected && null === $actual) {
            return;
        }

        $this->assertNotNull($expected);
        $this->assertNotNull($actual);
        $this->assertEquals(
            $expected->format(DateTimeTransformer::DATE_TIME_FORMAT),
            $actual->format(DateTimeTransformer::DATE_TIME_FORMAT)
        );
    }

    /**
     * Helper method to compare two Sort objects.
     */
    private function assertSortEquals(?SortCollection $expected, ?SortCollection $actual): void
    {
        if (null === $expected && null === $actual) {
            return;
        }

        $this->assertNotNull($expected);
        $this->assertNotNull($actual);

        if ($expected->count() !== $actual->count()) {
            $this->fail(\sprintf('Expected %d sort items, got %d.', $expected->count(), $actual->count()));
        }

        foreach ($expected->all() as $index => $expectedSort) {
            $actualSort = $actual->all()[$index];

            $this->assertEquals($expectedSort->getField()->value(), $actualSort->getField()->value());
            $this->assertSame($expectedSort->getDirection(), $actualSort->getDirection());
        }
    }

    /**
     * Helper method to compare two Pagination objects.
     */
    private function assertPaginationEquals(?Pagination $expected, ?Pagination $actual): void
    {
        if (null === $expected && null === $actual) {
            return;
        }

        $this->assertNotNull($expected);
        $this->assertNotNull($actual);
        $this->assertEquals($expected->page(), $actual->page());
        $this->assertEquals($expected->limit(), $actual->limit());
    }

    /**
     * Data provider for testFromArray.
     *
     * @throws CollectionException
     */
    public static function criteriaProvider(): \Generator
    {
        yield 'empty_data' => [
            'data' => [],
            'expectedCriteria' => UserCriteria::createEmpty(),
        ];

        yield 'with_is_active_true' => [
            'data' => ['is_active' => 'true'],
            'expectedCriteria' => UserCriteria::createEmpty()->filterByIsActive(true),
        ];

        yield 'with_is_active_false' => [
            'data' => ['is_active' => 'false'],
            'expectedCriteria' => UserCriteria::createEmpty()->filterByIsActive(false),
        ];

        yield 'with_start_date' => [
            'data' => ['start_date' => '2023-01-15 10:30:00'],
            'expectedCriteria' => UserCriteria::createEmpty()
                ->filterByStartDate(new \DateTimeImmutable('2023-01-15 10:30:00')),
        ];

        yield 'with_end_date' => [
            'data' => ['end_date' => '2023-01-20 15:45:00'],
            'expectedCriteria' => UserCriteria::createEmpty()
                ->filterByEndDate(new \DateTimeImmutable('2023-01-20 15:45:00')),
        ];

        yield 'with_search' => [
            'data' => ['search' => 'john'],
            'expectedCriteria' => UserCriteria::createEmpty()->filterBySearch('john'),
        ];

        yield 'with_role' => [
            'data' => ['role' => 'ROLE_ADMIN'],
            'expectedCriteria' => UserCriteria::createEmpty()->filterByRole('ROLE_ADMIN'),
        ];

        yield 'with_sort_by' => [
            'data' => ['sort_by' => 'email', 'sort_direction' => 'desc'],
            'expectedCriteria' => UserCriteria::createEmpty()
                ->sortBy(new SortCollection([
                    new Sort(new SortableField('email'), SortDirection::DESC),
                ])),
        ];

        yield 'with_sort_by_default_direction' => [
            'data' => ['sort_by' => 'first_name'],
            'expectedCriteria' => UserCriteria::createEmpty()
                ->sortBy(new SortCollection([
                    new Sort(new SortableField('firstName'), SortDirection::ASC),
                ])),
        ];

        yield 'with_pagination' => [
            'data' => ['page' => 2, 'page_size' => 15],
            'expectedCriteria' => UserCriteria::createEmpty()->withPagination(new Pagination(2, 15)),
        ];

        yield 'with_multiple_filters' => [
            'data' => [
                'is_active' => 'true',
                'search' => 'john',
                'role' => 'ROLE_ADMIN',
                'sort_by' => 'email',
                'sort_direction' => 'desc',
                'page' => 2,
                'page_size' => 15,
            ],
            'expectedCriteria' => UserCriteria::createEmpty()
                ->filterByIsActive(true)
                ->filterBySearch('john')
                ->filterByRole('ROLE_ADMIN')
                ->sortBy(new SortCollection([
                    new Sort(new SortableField('email'), SortDirection::DESC),
                ]))
                ->withPagination(new Pagination(2, 15)),
        ];
    }
}
