<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\CommandHandler;

use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\Tests\V2\Mother\Shared\Uuid\UuidMother;
use App\V2\Application\Command\PostLtiToolCommand;
use App\V2\Application\CommandHandler\PostLtiToolCommandHandler;
use App\V2\Domain\LTI\Exceptions\LtiRegistrationNotFoundException;
use App\V2\Domain\LTI\Exceptions\LtiToolNotFoundException;
use App\V2\Domain\LTI\LtiRegistrationRepository;
use App\V2\Domain\LTI\LtiTool;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\UuidGenerator;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PostLtiToolCommandHandlerTest extends TestCase
{
    /**
     * @throws Exception
     */
    private function getHandler(
        ?LtiRegistrationRepository $ltiRegistrationRepository = null,
        ?LtiToolRepository $ltiToolRepository = null,
        ?UuidGenerator $uuidGenerator = null,
    ): PostLtiToolCommandHandler {
        return new PostLtiToolCommandHandler(
            ltiRegistrationRepository: $ltiRegistrationRepository
                ?? $this->createMock(LtiRegistrationRepository::class),
            ltiToolRepository: $ltiToolRepository ?? $this->createMock(LtiToolRepository::class),
            uuidGenerator: $uuidGenerator ?? $this->createMock(UuidGenerator::class),
        );
    }

    /**
     * @throws InfrastructureException
     * @throws LtiRegistrationNotFoundException
     * @throws Exception
     * @throws CriteriaException
     * @throws InvalidUuidException
     */
    public function testHandle(): void
    {
        $toolId = UuidMother::create();
        $registration = LtiRegistrationMother::create();
        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->once())
            ->method('findOneBy')
            ->willReturn($registration);

        $ltiToolRepository = $this->createMock(LtiToolRepository::class);
        $ltiToolRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException(new LtiToolNotFoundException());

        $ltiToolRepository->expects($this->once())
            ->method('put')
            ->willReturnCallback(function (LtiTool $tool) use ($toolId) {
                $this->assertEquals($toolId, $tool->getId());
                $this->assertEquals('Tool 1', $tool->getName());
                $this->assertEquals('Tool 1 audience', $tool->getAudience());
                $this->assertEquals(new Url('https://example.com/authentication'), $tool->getOidcInitiationUrl());
                $this->assertEquals(new Url('https://example.com/launch'), $tool->getLaunchUrl());
                $this->assertEquals(new Url('https://example.com/deeplinking'), $tool->getDeepLinkingUrl());
                $this->assertEquals(new Url('https://example.com/jwks'), $tool->getJwksUrl());
            });

        $uuidGenerator = $this->createMock(UuidGenerator::class);
        $uuidGenerator->expects($this->once())
            ->method('generate')
            ->willReturn($toolId);

        $handler = $this->getHandler(
            ltiRegistrationRepository: $ltiRegistrationRepository,
            ltiToolRepository: $ltiToolRepository,
            uuidGenerator: $uuidGenerator,
        );

        $handler->handle(
            new PostLtiToolCommand(
                registrationId: $registration->getId(),
                name: 'Tool 1',
                audience: 'Tool 1 audience',
                oidcInitiationUrl: new Url('https://example.com/authentication'),
                launchUrl: new Url('https://example.com/launch'),
                deepLinkingUrl: new Url('https://example.com/deeplinking'),
                jwksUrl: new Url('https://example.com/jwks'),
            )
        );
    }

    /**
     * @throws InfrastructureException
     * @throws LtiRegistrationNotFoundException
     * @throws Exception
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    public function testRegistrationNotFoundException(): void
    {
        $exception = new LtiRegistrationNotFoundException();
        $ltiRegistrationRepository = $this->createMock(LtiRegistrationRepository::class);
        $ltiRegistrationRepository->expects($this->once())
            ->method('findOneBy')
            ->willThrowException($exception);

        $handler = $this->getHandler(
            ltiRegistrationRepository: $ltiRegistrationRepository,
        );

        $this->expectExceptionObject($exception);
        $handler->handle(
            new PostLtiToolCommand(
                registrationId: UuidMother::create(),
                name: 'Tool 1',
                audience: 'Tool 1 audience',
                oidcInitiationUrl: new Url('https://example.com/authentication'),
                launchUrl: new Url('https://example.com/launch'),
                deepLinkingUrl: new Url('https://example.com/deeplinking'),
                jwksUrl: new Url('https://example.com/jwks'),
            )
        );
    }
}
