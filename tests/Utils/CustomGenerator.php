<?php

declare(strict_types=1);

namespace App\Tests\Utils;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Id\AbstractIdGenerator;
use Doctrine\ORM\Id\AssignedGenerator;

class CustomGenerator extends AssignedGenerator
{
    public function __construct(
        private readonly AbstractIdGenerator $generator,
    ) {
    }

    /**
     * {@inheritDoc}
     */
    public function generateId(EntityManagerInterface $em, $entity)
    {
        $class = $em->getClassMetadata(\get_class($entity));

        if (self::isIdAssigned($em, $entity)) {
            return parent::generateId($em, $entity);
        } else {
            $idValue = [$class->getSingleIdentifierFieldName() => $this->generator->generateId($em, $entity)];
            $class->setIdentifierValues($entity, $idValue);

            return $idValue;
        }
    }

    public static function isIdAssigned(EntityManagerInterface $em, $entity): bool
    {
        $class = $em->getClassMetadata(\get_class($entity));
        $idFields = $class->getIdentifierFieldNames();
        foreach ($idFields as $idField) {
            $value = $class->getFieldValue($entity, $idField);

            if (!isset($value)) {
                return false;
            }
        }

        return true;
    }
}
