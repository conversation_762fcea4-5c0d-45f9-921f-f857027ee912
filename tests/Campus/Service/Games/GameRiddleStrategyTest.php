<?php

declare(strict_types=1);

namespace App\Tests\Campus\Service\Games;

use App\Campus\Games\GameStrategyInterface;
use App\Campus\Games\Riddle;
use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;

class GameRiddleStrategyTest extends TestGameStrategyInterface
{
    public function getStrategy(): GameStrategyInterface
    {
        return new Riddle(
            $this->createMock(EntityManagerInterface::class),
            $this->createMock(SettingsService::class)
        );
    }

    public static function getValidData(): \Generator
    {
        $chapter = new Chapter();
        $chapterType = new ChapterType();
        $chapterType->setCode('999');
        $chapterType->setPercentageCompleted('0.75');
        $chapter->setType($chapterType);

        $answers = [
            [
                'questionId' => 1,
                'value' => 'gata',
                'time' => 10,
                'correct' => false,
            ],
            [
                'questionId' => 1,
                'value' => 'gato',
                'time' => 4,
                'correct' => false,
            ],
        ];

        $attempts = [
            [
                'questionId' => 1,
                'value' => 'gata',
                'time' => 10,
                'correct' => false,
            ],
            [
                'questionId' => 1,
                'value' => 'gato',
                'time' => 10,
                'correct' => false,
            ],
        ];

        yield 'result ko, no chapter passed' => [
            'data' => ['answers' => $answers,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => null,
            'expectedPoints' => null,
        ];

        yield 'result ko, no total time passed' => [
            'data' => ['answers' => $answers,
                'timeTotal' => null,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no attempts passed' => [
            'data' => ['answers' => $answers,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => null,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko, no answers passed' => [
            'data' => ['answers' => null,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        yield 'result ko No correct answers.' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 2,
                'timeTotal' => 60,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0,
        ];

        $answers = [
            [
                'questionId' => 1,
                'value' => 'gata',
                'time' => 10,
                'correct' => false,
            ],
            [
                'questionId' => 1,
                'value' => 'gato',
                'time' => 10,
                'correct' => true,
            ],
        ];

        yield 'result ok with 1 fail. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 30,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.75,
        ];

        yield 'result ok with 1 fail and less time. With 2 attempts' => [
            'data' => ['answers' => $answers,
                'totalQuestions' => 1,
                'timeTotal' => 15,
                'time' => 9,
                'attempts' => $attempts,
            ],
            'args' => $chapter,
            'expectedPoints' => 0.72,
        ];
    }
}
