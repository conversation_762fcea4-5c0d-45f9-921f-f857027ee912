<?php

declare(strict_types=1);

namespace App\Tests\Functional\Frontend\Help;

use App\Entity\HelpCategory;
use App\Entity\HelpCategoryTranslation;
use App\Entity\HelpText;
use App\Entity\HelpTextTranslation;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

class HelpTest extends FunctionalTestCase
{
    private const HELP_CATEGORY_USER_TRANSLATIONS_DATA = [
        ['locale' => 'en', 'name' => 'User Area'],
        ['locale' => 'pt', 'name' => 'Área de usuário'],
        ['locale' => 'es', 'name' => 'Área de usuario'],
    ];

    private const HELP_CATEGORY_HELP_TRANSLATIONS_DATA = [
        ['locale' => 'en', 'name' => 'Help'],
        ['locale' => 'pt', 'name' => 'Ajuda'],
        ['locale' => 'es', 'name' => 'Ayuda'],
    ];

    private const HELP_TEXT_USER_AREA_TRANSLATIONS_DATA = [
        [
            'locale' => 'en',
            'title' => 'How to create an account?',
            'text' => 'To create an account, click on the "Register" button and complete the form.',
        ],
        [
            'locale' => 'pt',
            'title' => 'Como criar uma conta?',
            'text' => 'Para criar uma conta, clique no botão "Registrar" e complete o formulário.',
        ],
        [
            'locale' => 'es',
            'title' => '¿Cómo crear una cuenta?',
            'text' => 'Para crear una cuenta, haga clic en el botón "Registrarse" y complete el formulario.',
        ],
    ];

    private const HELP_TEXT_HELP_TRANSLATIONS_DATA = [
        [
            'locale' => 'en',
            'title' => 'How to reset my password?',
            'text' => 'If you forgot your password, go to the login page and click on "Forgot my password".',
        ],
        [
            'locale' => 'pt',
            'title' => 'Como redefinir minha senha?',
            'text' => 'Se você esqueceu sua senha, vá para a página de login e clique em "Esqueci minha senha".',
        ],
        [
            'locale' => 'es',
            'title' => '¿Cómo restablecer mi contraseña?',
            'text' => 'Si olvidaste tu contraseña, ve a la página de inicio de sesión y haz clic en "Olvidé mi contraseña".',
        ],
    ];

    protected function setUp(): void
    {
        parent::setUp();
        $this->loadHelpCategoryData();
        $this->loadHelpTextData();
    }

    /**
     * @dataProvider localeProvider
     */
    public function testGetHelp(string $locale): void
    {
        $this->setLocaleForUser($locale);

        $token = $this->loginAndGetToken();

        $response = $this->makeFrontendApiRequest(
            'GET',
            FrontendUserEndpoints::helpEndpoint(),
            [],
            [],
            [],
            $token
        );

        $this->assertSame(
            Response::HTTP_OK,
            $response->getStatusCode(),
            "[Locale: $locale] Código de respuesta HTTP inesperado."
        );

        $responseData = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('status', $responseData, "[Locale: $locale] Falta la clave 'status' en la respuesta");
        $this->assertArrayHasKey('error', $responseData, "[Locale: $locale] Falta la clave 'error' en la respuesta");
        $this->assertArrayHasKey('data', $responseData, "[Locale: $locale] Falta la clave 'data' en la respuesta");

        $this->assertFalse(
            $responseData['error'],
            "[Locale: $locale] Se esperaba 'error' en false, pero se obtuvo true."
        );

        $this->assertIsArray(
            $responseData['data'],
            "[Locale: $locale] El campo 'data' no es un array."
        );

        $this->assertNotEmpty(
            $responseData['data'],
            "[Locale: $locale] No se devolvió ninguna categoría de ayuda."
        );

        /** @var HelpCategoryRepository $helpCategoryRepository */
        $helpCategoryRepository = $this->getEntityManager()->getRepository(HelpCategory::class);

        $helpCategories = $helpCategoryRepository->findAll();

        foreach ($helpCategories as $expectedCategory) {
            $expectedCategoryName = $expectedCategory->getTranslations()->get($locale)
                ? $expectedCategory->getTranslations()->get($locale)->getName()
                : $expectedCategory->getName();

            $actualCategory = array_filter($responseData['data'], function ($category) use ($expectedCategory) {
                return $category['id'] === $expectedCategory->getId();
            });

            $this->assertNotEmpty(
                $actualCategory,
                "[Locale: $locale] No se encontró la categoría con ID {$expectedCategory->getId()} en la respuesta."
            );

            $actualCategory = array_values($actualCategory)[0];

            $this->assertSame(
                $expectedCategoryName,
                $actualCategory['name'],
                "[Locale: $locale] Nombre de la categoría incorrecto para ID {$expectedCategory->getId()}."
            );

            $this->assertIsArray(
                $actualCategory['helpTexts'],
                "[Locale: $locale] 'helpTexts' no es un array en la categoría ID {$expectedCategory->getId()}."
            );
            $this->assertNotEmpty(
                $actualCategory['helpTexts'],
                "[Locale: $locale] 'helpTexts' está vacío en la categoría ID {$expectedCategory->getId()}."
            );

            $expectedHelpTexts = $expectedCategory->getHelpTexts();

            foreach ($expectedHelpTexts as $expectedHelpText) {
                $expectedHelpTextTitle = $expectedHelpText->getTranslations()->get($locale)
                    ? $expectedHelpText->getTranslations()->get($locale)->getTitle()
                    : $expectedHelpText->getTitle();

                $expectedHelpTextText = $expectedHelpText->getTranslations()->get($locale)
                    ? $expectedHelpText->getTranslations()->get($locale)->getText()
                    : $expectedHelpText->getText();

                $actualHelpText = array_filter($actualCategory['helpTexts'], function ($helpText) use ($expectedHelpText) {
                    return $helpText['id'] === $expectedHelpText->getId();
                });

                $this->assertNotEmpty(
                    $actualHelpText,
                    "[Locale: $locale] No se encontró el HelpText con ID {$expectedHelpText->getId()} en la categoría ID {$expectedCategory->getId()}."
                );

                $actualHelpText = array_values($actualHelpText)[0];

                $this->assertSame(
                    $expectedHelpTextTitle,
                    $actualHelpText['title'],
                    "[Locale: $locale] Título incorrecto para HelpText ID {$expectedHelpText->getId()}."
                );

                $this->assertSame(
                    $expectedHelpTextText,
                    $actualHelpText['text'],
                    "[Locale: $locale] Texto incorrecto para HelpText ID {$expectedHelpText->getId()}."
                );
            }
        }
    }

    private function loadHelpCategoryData(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->getEntityManager();

        $helpCategoryCount = $entityManager->getRepository(HelpCategory::class)->count([]);
        $helpCategoryTranslationCount = $entityManager->getRepository(HelpCategoryTranslation::class)->count([]);

        if (0 === $helpCategoryCount || 0 === $helpCategoryTranslationCount) {
            $this->clearHelpCategoryData();

            $helpCategoryUser = new HelpCategory();
            $helpCategoryUser->setName('Área de usuario');
            $helpCategoryUser->setSort(0);

            $this->addTranslationsToCategory($helpCategoryUser, self::HELP_CATEGORY_USER_TRANSLATIONS_DATA, $entityManager);

            $entityManager->persist($helpCategoryUser);

            $helpCategoryHelp = new HelpCategory();
            $helpCategoryHelp->setName('Ayuda');
            $helpCategoryHelp->setSort(1);

            $this->addTranslationsToCategory($helpCategoryHelp, self::HELP_CATEGORY_HELP_TRANSLATIONS_DATA, $entityManager);

            $entityManager->persist($helpCategoryHelp);

            $entityManager->flush();
        }
    }

    private function addTranslationsToCategory(HelpCategory $category, array $translationsData, EntityManagerInterface $entityManager): void
    {
        foreach ($translationsData as $translationData) {
            $translation = new HelpCategoryTranslation();
            $translation->setLocale($translationData['locale']);
            $translation->setName($translationData['name']);
            $translation->setTranslatable($category);
            $category->addTranslation($translation);
            $entityManager->persist($translation);
        }
    }

    private function clearHelpCategoryData(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->getEntityManager();

        $translations = $entityManager->getRepository(HelpCategoryTranslation::class)->findAll();
        foreach ($translations as $translation) {
            $entityManager->remove($translation);
        }

        $categories = $entityManager->getRepository(HelpCategory::class)->findAll();
        foreach ($categories as $category) {
            $entityManager->remove($category);
        }

        $entityManager->flush();
    }

    private function loadHelpTextData(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->getEntityManager();

        $helpTextCount = $entityManager->getRepository(HelpText::class)->count([]);
        $helpTextTranslationCount = $entityManager->getRepository(HelpTextTranslation::class)->count([]);

        if (0 === $helpTextCount || 0 === $helpTextTranslationCount) {
            $this->clearHelpTextData();

            $categoryUserArea = $entityManager->getRepository(HelpCategory::class)->findOneBy(['name' => 'Área de usuario']);
            $categoryHelp = $entityManager->getRepository(HelpCategory::class)->findOneBy(['name' => 'Ayuda']);

            if (!$categoryUserArea) {
                throw new \Exception('Categoría de ayuda "Área de usuario" no encontrada.');
            }

            if (!$categoryHelp) {
                throw new \Exception('Categoría de ayuda "Ayuda" no encontrada.');
            }

            $helpTextUserArea = new HelpText();
            $helpTextUserArea->setCategory($categoryUserArea);
            $helpTextUserArea->setTitle('¿Cómo crear una cuenta?');
            $helpTextUserArea->setText('Para crear una cuenta, haga clic en el botón "Registrarse" y complete el formulario.');

            $this->addTranslationsToHelpText(
                $helpTextUserArea,
                self::HELP_TEXT_USER_AREA_TRANSLATIONS_DATA,
                $entityManager
            );

            $entityManager->persist($helpTextUserArea);

            $helpTextHelp = new HelpText();
            $helpTextHelp->setCategory($categoryHelp);
            $helpTextHelp->setTitle('¿Cómo restablecer mi contraseña?');
            $helpTextHelp->setText('Si olvidaste tu contraseña, ve a la página de inicio de sesión y haz clic en "Olvidé mi contraseña".');

            $this->addTranslationsToHelpText(
                $helpTextHelp,
                self::HELP_TEXT_HELP_TRANSLATIONS_DATA,
                $entityManager
            );

            $entityManager->persist($helpTextHelp);

            $entityManager->flush();
        }
    }

    private function addTranslationsToHelpText(HelpText $helpText, array $translationsData, EntityManagerInterface $entityManager): void
    {
        foreach ($translationsData as $translationData) {
            $translation = new HelpTextTranslation();
            $translation->setLocale($translationData['locale']);
            $translation->setTitle($translationData['title']);
            $translation->setText($translationData['text']);
            $translation->setTranslatable($helpText);
            $helpText->addTranslation($translation);
            $entityManager->persist($translation);
        }
    }

    private function clearHelpTextData(): void
    {
        /** @var EntityManagerInterface $entityManager */
        $entityManager = $this->getEntityManager();

        $translations = $entityManager->getRepository(HelpTextTranslation::class)->findAll();
        foreach ($translations as $translation) {
            $entityManager->remove($translation);
        }

        $helpTexts = $entityManager->getRepository(HelpText::class)->findAll();
        foreach ($helpTexts as $helpText) {
            $entityManager->remove($helpText);
        }

        $entityManager->flush();
    }

    protected function tearDown(): void
    {
        $this->clearHelpTextData();
        $this->clearHelpCategoryData();
        parent::tearDown();
    }
}
