<?php

declare(strict_types=1);

namespace App\Tests\Functional\Service;

use App\Entity\Task;
use App\Entity\ZipFileTask;
use App\Exception\NoAvailableSlotException;
use App\Service\SlotManagerService;
use App\Tests\Functional\FunctionalTestCase;

class ZipFileTaskTimeoutAndSlotTest extends FunctionalTestCase
{
    private const TEST_DEBUG_MODE = false; // Desactivate debug mode
    private const DEFAULT_TIMEOUT_SECONDS = 3600; // 1 hour in seconds
    private const CUSTOM_TIMEOUT_SECONDS = 1800; // 30 minutes in seconds
    private const SHORT_TIMEOUT_SECONDS = 60; // 1 minute in seconds
    private const DEFAULT_SLOT_QUANTITY = 3;

    protected function setUp(): void
    {
        self::$debugMode = self::TEST_DEBUG_MODE;
        parent::setUp();
        $this->truncateEntities([Task::class, ZipFileTask::class]);

        // Configure default settings
        $settings = $this->getService('App\Service\SettingsService');
        $settings->setSetting('app.export.task.timeout_seconds', (string) self::DEFAULT_TIMEOUT_SECONDS);
        $settings->setSetting('app.export.zip_task.slot_quantity', (string) self::DEFAULT_SLOT_QUANTITY);

        // Verify the settings were saved correctly
        $this->log('Default timeout seconds: ' . $settings->get('app.export.task.timeout_seconds'));
        $this->log('Default slot quantity: ' . $settings->get('app.export.zip_task.slot_quantity'));
    }

    protected function tearDown(): void
    {
        $this->truncateEntities([Task::class, ZipFileTask::class]);
        parent::tearDown();
    }

    /**
     * Test that the SlotManagerService correctly handles ZipFileTasks in TIMEOUT status
     * based on the configured timeout period.
     */
    public function testZipFileTaskTimeoutInSlotManager(): void
    {
        $em = $this->getEntityManager();
        $slotManagerService = $this->getService(SlotManagerService::class);
        $user = $this->getDefaultUser();

        // Create a ZipFileTask with TIMEOUT status that started less than the default timeout ago
        $recentTimeoutTask = new ZipFileTask();
        $recentTimeoutTask->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT_SECONDS - 600) . ' seconds')) // 10 minutes before timeout
            ->setOriginalName('test-file.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($recentTimeoutTask);
        $em->flush();

        // Try to get an available slot - should consider the recent task as running
        try {
            $slot = $slotManagerService->getAvailableZipTaskExecutionSlot();
            $this->assertNotNull($slot, 'Should get a slot since only one task is running');
            $this->log('Got slot with one TIMEOUT task running');
        } catch (\Exception $e) {
            $this->fail('Should not throw an exception: ' . $e->getMessage());
        }

        // Create more tasks to reach the slot limit
        for ($i = 0; $i < self::DEFAULT_SLOT_QUANTITY - 1; ++$i) {
            $task = new ZipFileTask();
            $task->setTask('zip-task')
                ->setType('announcement')
                ->setEntityId('123')
                ->setCreatedBy($user)
                ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
                ->setStartedAt(new \DateTimeImmutable())
                ->setOriginalName('test-file-' . $i . '.zip')
                ->setParams(['param1' => 'value1']);
            $em->persist($task);
        }
        $em->flush();

        // Now we should have DEFAULT_SLOT_QUANTITY running tasks (1 in TIMEOUT status within timeout period, rest in IN_PROGRESS)
        // Try to get another slot - should throw an exception
        try {
            $slotManagerService->getAvailableZipTaskExecutionSlot();
            $this->fail('Should throw an exception because all slots are used');
        } catch (NoAvailableSlotException $e) {
            $this->assertStringContainsString('ZIPTASK-SLOTS-EXCEEDED', $e->getMessage());
            $this->log('Correctly got exception: ' . $e->getMessage());
        }

        // Change the timeout to a shorter value so the TIMEOUT task is no longer considered running
        $settings = $this->getService('App\Service\SettingsService');
        $settings->setSetting('app.export.task.timeout_seconds', self::SHORT_TIMEOUT_SECONDS);

        // Now we should be able to get a slot
        try {
            $slot = $slotManagerService->getAvailableZipTaskExecutionSlot();
            $this->assertNotNull($slot, 'Should get a slot since the TIMEOUT task is now expired');
            $this->log('Got slot after reducing timeout period');
        } catch (\Exception $e) {
            $this->fail('Should not throw an exception: ' . $e->getMessage());
        }
    }

    /**
     * Test that the slot quantity setting is respected for ZipFileTasks.
     */
    public function testZipFileTaskSlotQuantityLimit(): void
    {
        $em = $this->getEntityManager();
        $slotManagerService = $this->getService(SlotManagerService::class);
        $user = $this->getDefaultUser();
        $settings = $this->getService('App\Service\SettingsService');

        // Set a custom slot quantity
        $customSlotQuantity = 2;
        $settings->setSetting('app.export.zip_task.slot_quantity', (string) $customSlotQuantity);
        $this->log('Set custom slot quantity: ' . $customSlotQuantity);

        // Create tasks up to the slot limit
        for ($i = 0; $i < $customSlotQuantity; ++$i) {
            $task = new ZipFileTask();
            $task->setTask('zip-task')
                ->setType('announcement')
                ->setEntityId('123')
                ->setCreatedBy($user)
                ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
                ->setStartedAt(new \DateTimeImmutable())
                ->setOriginalName('test-file-' . $i . '.zip')
                ->setParams(['param1' => 'value1']);
            $em->persist($task);
        }
        $em->flush();

        // Try to get another slot - should throw an exception
        try {
            $slotManagerService->getAvailableZipTaskExecutionSlot();
            $this->fail('Should throw an exception because all slots are used');
        } catch (NoAvailableSlotException $e) {
            $this->assertStringContainsString('ZIPTASK-SLOTS-EXCEEDED', $e->getMessage());
            $this->log('Correctly got exception: ' . $e->getMessage());
        }

        // Increase the slot quantity
        $settings->setSetting('app.export.zip_task.slot_quantity', (string) ($customSlotQuantity + 1));
        $this->log('Increased slot quantity to: ' . ($customSlotQuantity + 1));

        // Now we should be able to get a slot
        try {
            $slot = $slotManagerService->getAvailableZipTaskExecutionSlot();
            $this->assertNotNull($slot, 'Should get a slot after increasing the slot quantity');
            $this->log('Got slot after increasing slot quantity');
        } catch (\Exception $e) {
            $this->fail('Should not throw an exception: ' . $e->getMessage());
        }
    }

    /**
     * Test the interaction between timeout and slot quantity settings for ZipFileTasks.
     */
    public function testZipFileTaskTimeoutAndSlotInteraction(): void
    {
        $em = $this->getEntityManager();
        $slotManagerService = $this->getService(SlotManagerService::class);
        $user = $this->getDefaultUser();
        $settings = $this->getService('App\Service\SettingsService');

        // Set a custom slot quantity and timeout
        $customSlotQuantity = 2;
        $settings->setSetting('app.export.zip_task.slot_quantity', (string) $customSlotQuantity);
        $settings->setSetting('app.export.task.timeout_seconds', (string) self::CUSTOM_TIMEOUT_SECONDS);
        $this->log('Set custom slot quantity: ' . $customSlotQuantity);
        $this->log('Set custom timeout: ' . self::CUSTOM_TIMEOUT_SECONDS);

        // Create a task with TIMEOUT status that started less than the custom timeout ago
        $recentTimeoutTask = new ZipFileTask();
        $recentTimeoutTask->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::CUSTOM_TIMEOUT_SECONDS - 300) . ' seconds')) // 5 minutes before timeout
            ->setOriginalName('test-file-timeout.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($recentTimeoutTask);

        // Create another task in IN_PROGRESS status to reach the slot limit
        $inProgressTask = new ZipFileTask();
        $inProgressTask->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
            ->setStartedAt(new \DateTimeImmutable())
            ->setOriginalName('test-file-inprogress.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($inProgressTask);
        $em->flush();

        // Try to get another slot - should throw an exception
        try {
            $slotManagerService->getAvailableZipTaskExecutionSlot();
            $this->fail('Should throw an exception because all slots are used');
        } catch (NoAvailableSlotException $e) {
            $this->assertStringContainsString('ZIPTASK-SLOTS-EXCEEDED', $e->getMessage());
            $this->log('Correctly got exception: ' . $e->getMessage());
        }

        // Change the timeout to a shorter value so the TIMEOUT task is no longer considered running
        $settings->setSetting('app.export.task.timeout_seconds', (string) self::SHORT_TIMEOUT_SECONDS);
        $this->log('Reduced timeout to: ' . self::SHORT_TIMEOUT_SECONDS);

        // Now we should be able to get a slot
        try {
            $slot = $slotManagerService->getAvailableZipTaskExecutionSlot();
            $this->assertNotNull($slot, 'Should get a slot since the TIMEOUT task is now expired');
            $this->log('Got slot after reducing timeout period');
        } catch (\Exception $e) {
            $this->fail('Should not throw an exception: ' . $e->getMessage());
        }

        // Create another task in IN_PROGRESS status to reach the slot limit again
        $inProgressTask2 = new ZipFileTask();
        $inProgressTask2->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
            ->setStartedAt(new \DateTimeImmutable())
            ->setOriginalName('test-file-inprogress2.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($inProgressTask2);
        $em->flush();

        // Try to get another slot - should throw an exception again
        try {
            $slotManagerService->getAvailableZipTaskExecutionSlot();
            $this->fail('Should throw an exception because all slots are used');
        } catch (NoAvailableSlotException $e) {
            $this->assertStringContainsString('ZIPTASK-SLOTS-EXCEEDED', $e->getMessage());
            $this->log('Correctly got exception: ' . $e->getMessage());
        }
    }

    /**
     * Test that the countPendingZipFileTasksByUser method correctly respects the timeout setting.
     */
    public function testCountPendingZipFileTasksByUserRespectsTimeout(): void
    {
        $em = $this->getEntityManager();
        $user = $this->getDefaultUser();
        $settings = $this->getService('App\Service\SettingsService');

        // Create tasks with different statuses and start times
        $pendingTask = new ZipFileTask();
        $pendingTask->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_PENDING)
            ->setOriginalName('test-file-pending.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($pendingTask);

        $inProgressTask = new ZipFileTask();
        $inProgressTask->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_IN_PROGRESS)
            ->setStartedAt(new \DateTimeImmutable())
            ->setOriginalName('test-file-inprogress.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($inProgressTask);

        $recentTimeoutTask = new ZipFileTask();
        $recentTimeoutTask->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT_SECONDS - 600) . ' seconds')) // 10 minutes before timeout
            ->setOriginalName('test-file-recent-timeout.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($recentTimeoutTask);

        $oldTimeoutTask = new ZipFileTask();
        $oldTimeoutTask->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::DEFAULT_TIMEOUT_SECONDS + 600) . ' seconds')) // 10 minutes after timeout
            ->setOriginalName('test-file-old-timeout.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($oldTimeoutTask);

        $completedTask = new ZipFileTask();
        $completedTask->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_COMPLETED)
            ->setStartedAt(new \DateTimeImmutable('-1 hour'))
            ->setFinishedAt(new \DateTimeImmutable())
            ->setOriginalName('test-file-completed.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($completedTask);

        $em->flush();

        // Count pending tasks with default timeout
        $zipFileTaskRepository = $em->getRepository(ZipFileTask::class);
        $pendingCount = $zipFileTaskRepository->countPendingZipFileTasksByUser($user);
        $this->assertEquals(3, $pendingCount, 'Should count PENDING, IN_PROGRESS, and recent TIMEOUT tasks as pending');
        $this->log('Pending count with default timeout: ' . $pendingCount);

        // Change the timeout to a shorter value
        $settings->setSetting('app.export.task.timeout_seconds', (string) self::SHORT_TIMEOUT_SECONDS);
        $this->log('Reduced timeout to: ' . self::SHORT_TIMEOUT_SECONDS);

        // Count pending tasks with shorter timeout
        $pendingCount = $zipFileTaskRepository->countPendingZipFileTasksByUser($user);
        $this->assertEquals(2, $pendingCount, 'Should only count PENDING and IN_PROGRESS tasks as pending with shorter timeout');
        $this->log('Pending count with shorter timeout: ' . $pendingCount);

        // Create a new timeout task within the shorter timeout period
        $veryRecentTimeoutTask = new ZipFileTask();
        $veryRecentTimeoutTask->setTask('zip-task')
            ->setType('announcement')
            ->setEntityId('123')
            ->setCreatedBy($user)
            ->setStatus(ZipFileTask::STATUS_TIMEOUT)
            ->setStartedAt(new \DateTimeImmutable('-' . (self::SHORT_TIMEOUT_SECONDS - 10) . ' seconds')) // 10 seconds before timeout
            ->setOriginalName('test-file-very-recent-timeout.zip')
            ->setParams(['param1' => 'value1']);
        $em->persist($veryRecentTimeoutTask);
        $em->flush();

        // Count pending tasks again
        $pendingCount = $zipFileTaskRepository->countPendingZipFileTasksByUser($user);
        $this->assertEquals(3, $pendingCount, 'Should count PENDING, IN_PROGRESS, and very recent TIMEOUT tasks as pending');
        $this->log('Pending count after adding very recent timeout task: ' . $pendingCount);
    }
}
