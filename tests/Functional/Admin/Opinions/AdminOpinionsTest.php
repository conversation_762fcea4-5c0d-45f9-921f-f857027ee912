<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Opinions;

use App\Entity\Course;
use App\Entity\Filter;
use App\Entity\FilterCategory;
use App\Entity\Nps;
use App\Entity\UserCourse;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminOpinionsEndpoint;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class AdminOpinionsTest extends FunctionalTestCase
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testEmptyOpinions(): void
    {
        $course = $this->createandGetCourse();
        $response = $this->getCourseUserStatsDataResponse($course);
        $responseData = $this->extractResponseData($response);

        $this->assertNotEmpty($responseData);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertArrayHasKey('hideEmptyOpinions', $responseData);
        $this->assertArrayHasKey('items', $responseData);
        $this->assertArrayHasKey('total-items', $responseData);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     *
     * @dataProvider opinionsDataProvider
     */
    public function testOpinions(string $opinionValue): void
    {
        $course = $this->createandGetCourse();
        $userCourse = $this->createAndGetUserCourse($this->getDefaultUser(), $course);
        $opinion = $this->createAndGetOpinion(course: $userCourse, value: $opinionValue);
        $response = $this->getCourseUserStatsDataResponse($course);
        $responseData = $this->extractResponseData($response);

        $this->assertNotEmpty($responseData);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertArrayHasKey('hideEmptyOpinions', $responseData);
        $this->assertArrayHasKey('items', $responseData);
        $this->assertArrayHasKey('total-items', $responseData);

        $this->assertNotEmpty($responseData['items']);
        $this->assertArrayHasKey('avatar', $responseData['items'][0]);
        $this->assertArrayHasKey('courseCode', $responseData['items'][0]);
        $this->assertArrayHasKey('courseId', $responseData['items'][0]);
        $this->assertArrayHasKey('courseName', $responseData['items'][0]);
        $this->assertArrayHasKey('createdAt', $responseData['items'][0]);
        $this->assertArrayHasKey('extra', $responseData['items'][0]);
        $this->assertArrayHasKey('firstName', $responseData['items'][0]);
        $this->assertArrayHasKey('highlight', $responseData['items'][0]);
        $this->assertArrayHasKey('id', $responseData['items'][0]);
        $this->assertArrayHasKey('lastName', $responseData['items'][0]);
        $this->assertArrayHasKey('rating', $responseData['items'][0]);
        $this->assertArrayHasKey('rating_id', $responseData['items'][0]);
        $this->assertArrayHasKey('text', $responseData['items'][0]);
        $this->assertArrayHasKey('user_course_id', $responseData['items'][0]);
        $this->assertArrayHasKey('user_id', $responseData['items'][0]);
        $this->assertArrayHasKey('visible', $responseData['items'][0]);
        $this->assertContains($opinion->getId(), array_column($responseData['items'], 'id'));
        $this->assertContains($opinionValue, array_column($responseData['items'], 'text'));
    }

    public static function opinionsDataProvider(): \Generator
    {
        yield 'test opinion' => [
            'opinionValue' => 'test opinion',
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws MappingException
     * @throws Exception
     */
    public function testOpinionsForManager(): void
    {
        $em = $this->getEntityManager();
        $course = $this->createandGetCourse();

        $filter = $this->createAndGetFilter();

        $userManaged = $this->getOrCreateUser('<EMAIL>', 'User', 'Managed');
        $userCourseManaged = $this->createAndGetUserCourse($userManaged, $course);
        $userManaged->setFilters([$filter]);

        $userNotManaged = $this->getOrCreateUser('<EMAIL>', 'User', 'NotManaged');
        $userCourseNotManaged = $this->createAndGetUserCourse($userNotManaged, $course);

        $this->createAndGetOpinion(course: $userCourseManaged, value: 'You can see me', user: $userManaged);
        $this->createAndGetOpinion(course: $userCourseNotManaged, value: 'You can not see me', user: $userNotManaged);

        $this->getDefaultUser()->setRoles(['ROLE_MANAGER']);
        $this->getDefaultUser()->addManagerFilter($filter);

        $em->flush();

        $response = $this->getCourseUserStatsDataResponse($course);
        $responseData = $this->extractResponseData($response);

        $this->assertNotEmpty($responseData);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertArrayHasKey('text', $responseData['items'][0]);
        $this->assertCount(1, $responseData['items']);
        $this->assertEquals('You can see me', $responseData['items'][0]['text']);

        $this->getDefaultUser()->setRoles(['ROLE_USER', 'ROLE_ADMIN']);
        $this->getDefaultUser()->removeManagerFilter($filter);
        $em->flush();
        $em->clear();

        $this->truncateEntities([
            Nps::class,
            Course::class,
            UserCourse::class,
            FilterCategory::class,
            Filter::class,
        ]);

        $em->createQuery('DELETE FROM App\Entity\User u WHERE u.email = :email1 OR u.email = :email2')
            ->setParameter('email1', $userManaged->getEmail())
            ->setParameter('email2', $userNotManaged->getEmail())
            ->execute();
        $em->flush();

        $em->createQuery('DELETE FROM App\Entity\User u WHERE u.email = :email1 OR u.email = :email2')
            ->setParameter('email1', $userManaged->getEmail())
            ->setParameter('email2', $userNotManaged->getEmail())
            ->execute();
        $em->flush();
    }

    private function getCourseUserStatsDataResponse(Course $course): Response
    {
        $userToken = $this->loginAndGetToken();

        return $this->makeAdminApiRequest(
            'GET',
            AdminOpinionsEndpoint::opinionsEndpoint($course->getId()),
            [],
            [],
            [],
            $userToken
        );
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    public function tearDown(): void
    {
        $this->truncateEntities([
            Nps::class,
            Course::class,
            UserCourse::class,
        ]);
        parent::tearDown();
    }
}
