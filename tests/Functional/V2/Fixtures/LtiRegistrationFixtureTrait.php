<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Fixtures;

use App\Tests\V2\Mother\LTI\LtiRegistrationMother;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;

trait LtiRegistrationFixtureTrait
{
    /**
     * @throws InvalidUuidException
     */
    private function setAndGetLtiRegistrationInRepository(
        ?Uuid $id = null,
        ?string $name = null,
        ?string $clientId = null,
    ): LtiRegistration {
        $registration = LtiRegistrationMother::create(
            id: $id,
            name: $name,
            clientId: $clientId,
        );

        $this->client->getContainer()->get('App\V2\Domain\LTI\LtiRegistrationRepository')
            ->put($registration);

        return $registration;
    }
}
