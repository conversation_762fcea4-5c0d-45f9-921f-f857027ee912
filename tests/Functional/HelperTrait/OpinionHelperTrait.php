<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Announcement;
use App\Entity\Nps;
use App\Entity\NpsQuestion;
use App\Entity\User;
use App\Entity\UserCourse;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait OpinionHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetOpinion(
        ?UserCourse $course = null,
        string $type = 'text',
        string $value = 'test opined',
        ?User $user = null,
        ?Announcement $announcement = null,
    ): Nps {
        $em = $this->getEntityManager();
        $npsQuestion = $this->getEntityManager()->getRepository(NpsQuestion::class)->findOneBy(['main' => 1, 'type' => $type]);

        $opinion = new Nps();
        $opinion->setCourse($course);
        $opinion->setQuestion($npsQuestion);
        $opinion->setUser($user ?? $this->getDefaultUser());
        $opinion->setCreatedBy($user ?? $this->getDefaultUser());
        $opinion->setToPost(true);
        $opinion->setValue($value);
        $opinion->setType($type);
        $opinion->setMain(true);
        $opinion->setAnnouncement($announcement);

        $em->persist($opinion);
        $em->flush();

        return $opinion;
    }

    /**
     * @throws ORMException
     */
    protected function removeOpinion(Nps $opinion): void
    {
        $em = $this->getEntityManager();
        if (!$em->contains($opinion)) {
            // Si la entidad está desconectada (detached), busco la entidad desde la base de datos
            $opinion = $em->find(\get_class($opinion), $opinion->getId());

            if (!$opinion) {
                return;
            }
        }

        $em->remove($opinion);
        $em->flush();
    }
}
