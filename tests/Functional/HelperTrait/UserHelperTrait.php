<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\User;
use App\Tests\Functional\HelperTrait\Endpoints\Frontend\FrontendUserEndpoints;
use App\V2\Domain\Shared\Filter\FilterCollection;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

trait UserHelperTrait
{
    protected const array DEFAULT_ROLES = ['ROLE_USER'];

    protected function getDefaultUser(): User
    {
        return $this->getContainer()
            ->get('doctrine')
            ->getRepository(User::class)
            ->find(self::DEFAULT_USER_ID);
    }

    protected function loginAndGetToken(
        string $email = self::DEFAULT_USER_EMAIL,
        string $password = self::DEFAULT_USER_PASSWORD
    ): string {
        $response = $this->makeFrontendApiRequest('POST', FrontendUserEndpoints::loginEndpoint(), [
            'email' => $email,
            'password' => $password,
        ]);

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $responseData = $this->extractResponseData($response);

        $this->assertArrayHasKey('token', $responseData);

        return $responseData['token'];
    }

    protected function loginAndGetTokenForUser(User $user): string
    {
        return $this->loginAndGetToken($user->getEmail(), self::DEFAULT_USER_PASSWORD);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetUser(
        string $firstName = 'John',
        string $lastName = 'Doe',
        array $roles = self::DEFAULT_ROLES,
        string $locale = 'es',
        string $localeCampus = 'es',
        string $email = '<EMAIL>',
        bool $isActive = true,
        bool $open = true,
        string $password = self::DEFAULT_USER_PASSWORD,
        ?FilterCollection $userFilters = null,
        ?FilterCollection $manageFilters = null,
        ?User $createdBy = null,
        bool $validated = true,
    ): User {
        $em = $this->getEntityManager();
        /** @var UserPasswordHasherInterface $passwordHasher */
        $passwordHasher = $this->client->getContainer()
            ->get('Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface');

        $user = new User();
        $user->setFirstName($firstName);
        $user->setLastName($lastName);
        $user->setRoles($roles);
        $user->setLocale($locale);
        $user->setLocaleCampus($localeCampus);
        $user->setEmail($email);
        $user->setIsActive($isActive);
        $user->setPassword($password);
        $user->setOpen($open);
        $user->setValidated($validated);

        if (null !== $userFilters) {
            $user->setFilters($userFilters->all());
        }

        if (null !== $manageFilters) {
            foreach ($manageFilters->all() as $filter) {
                $user->addManagerFilter($filter);
            }
        }

        if (null !== $createdBy) {
            $user->setCreatedBy($createdBy);
        }

        $em->persist($user);
        $em->flush();

        //        $user->setPassword($passwordHasher->hashPassword($user, $password));
        //        $em->flush();

        return $user;
    }

    /**
     * Hard delete users by their IDs, bypassing the soft delete mechanism.
     * This method also deletes related records in tables that have foreign key constraints.
     *
     * @param array $userIds       Array of user IDs to delete
     * @param array $relatedTables Optional array of related tables to clean up before deleting users
     *
     * @throws Exception
     */
    protected function hardDeleteUsersByIds(array $userIds, array $relatedTables = ['user_manage']): void
    {
        if (empty($userIds)) {
            return;
        }

        $em = $this->getEntityManager();
        $connection = $em->getConnection();

        // Create placeholders for the SQL IN clause
        $userIdPlaceholders = implode(',', array_fill(0, \count($userIds), '?'));

        // Delete related records in tables that have foreign key constraints
        foreach ($relatedTables as $table) {
            $connection->executeStatement(
                "DELETE FROM $table WHERE user_id IN ($userIdPlaceholders)",
                $userIds
            );
        }

        // Now we can safely delete the users
        // Remove the foreign key constraints
        $connection->executeStatement(
            'SET FOREIGN_KEY_CHECKS=0'
        );
        $connection->executeStatement(
            "DELETE FROM user WHERE id IN ($userIdPlaceholders)",
            $userIds
        );
        // Re-enable the foreign key constraints
        $connection->executeStatement(
            'SET FOREIGN_KEY_CHECKS=1'
        );
    }

    /**
     * @throws NotSupported
     * @throws ORMException
     */
    protected function getOrCreateUser(
        string $email,
        ?string $firstName = null,
        ?string $lastName = null,
        ?array $roles = null
    ): User {
        $user = $this->getEntityManager()->getRepository(User::class)->findOneBy(['email' => $email]);
        if ($user) {
            return $user;
        }

        return $this->createAndGetUser(
            firstName: $firstName,
            lastName: $lastName,
            roles: $roles ?? self::DEFAULT_ROLES,
            locale: 'es',
            localeCampus: 'es',
            email: $email,
            isActive: true,
            open: true,
            password: 'password',
        );
    }
}
