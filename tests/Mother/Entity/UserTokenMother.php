<?php

declare(strict_types=1);

namespace App\Tests\Mother\Entity;

use App\Entity\User;
use App\Entity\UserToken;

class UserTokenMother
{
    public static function create(
        ?User $user = null,
        ?int $type = null,
        ?string $token = null,
        bool $revoked = false,
        bool $used = false,
        ?\DateTimeImmutable $usedAt = null,
        ?\DateTimeImmutable $validUntil = null,
        array $extra = []
    ): UserToken {
        $userToken = new UserToken();
        $userToken->setUser($user);
        $userToken->setType($type);
        $userToken->setToken($token);
        $userToken->setRevoked($revoked);
        $userToken->setUsed($used);
        $userToken->setUsedAt($usedAt);
        $userToken->setValidUntil($validUntil);
        $userToken->setExtra($extra);

        return $userToken;
    }
}
