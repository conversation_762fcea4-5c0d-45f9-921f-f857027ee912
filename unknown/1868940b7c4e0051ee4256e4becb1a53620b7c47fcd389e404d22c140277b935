<?php

namespace App\Controller\Admin;

use App\Entity\TimeGame;
use App\Admin\Traits\SerializerTrait;
use App\Entity\Chapter;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Symfony\Component\HttpFoundation\Request;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Routing\Annotation\Route;

class TimeGameCrudController extends AbstractCrudController
{
    use SerializerTrait;
    private $em;
    private $requestStack;
    private $logger;
    private $context;
    protected $translator;

    public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger, AdminContextProvider $context, TranslatorInterface $translator)
    {
        $this->em = $em;
        $this->requestStack = $requestStack;
        $this->logger = $logger;
        $this->context = $context;
        $this->translator = $translator;
    }

    public static function getEntityFqcn(): string
    {
        return TimeGame::class;
    }


    /**
     * @Route("/admin/time-game/save", name="save_time_game", methods={"POST"})
     */
    public function saveTimeChapter(Request $request)
    {
        try {
            $idChapter = $request->request->get('idChapter');
            $time = $request->request->get('time');

            $chapter = $this->em->getRepository(Chapter::class)->find($idChapter);
            $timeGame = $this->em->getRepository(TimeGame::class)->findOneBy(['chapter' => $chapter]);

            if (!$timeGame) {
                $timeGame = new TimeGame();
                $timeGame->setTime($time);
                $timeGame->setChapter($chapter);
            } else {
                $timeGame->setTime($time);
            }

            $this->em->persist($timeGame);
            $this->em->flush();

            $response = [
                'status' => Response::HTTP_ACCEPTED,
                'error' => false,
                'data' => [
                    'time' => $timeGame->getTime(),
                    'idChapter' => $chapter->getId()

                ]
            ];
        } catch (\Exception $e) {
            $response  = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => [
                    'message' => $e->getMessage()
                ]
            ];
        }

        return $this->sendResponse($response);
    }

    /**
     * @Route("/admin/time-game/{id}", name="get_time_game", methods={"GET"})
     */
    public function getTimeChapter(Chapter $chapter)
    {
        try {
            $timeGame = $this->em->getRepository(TimeGame::class)->findOneBy(['chapter' => $chapter]);

            if (!$timeGame) {
                $timeGame = new TimeGame();
                $timeGame->setTime(30);
                $timeGame->setChapter($chapter);
                $this->em->persist($timeGame);
                $this->em->flush();
            }

            $response = [
                'status' => Response::HTTP_ACCEPTED,
                'error' => false,
                'data' => [
                    'time' => $timeGame->getTime(),
                    'idChapter' => $chapter->getId()

                ]
            ];
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => [
                    'message' => $e->getMessage()
                ]
            ];
        }
        return $this->sendResponse($response);
    }
}
