<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\IntegrationGroup;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<IntegrationGroup>
 *
 * @method IntegrationGroup|null find($id, $lockMode = null, $lockVersion = null)
 * @method IntegrationGroup|null findOneBy(array $criteria, array $orderBy = null)
 * @method IntegrationGroup[]    findAll()
 * @method IntegrationGroup[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class IntegrationGroupRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, IntegrationGroup::class);
    }

    public function add(IntegrationGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(IntegrationGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
