<?php

namespace App\Entity;

use App\Entity\ForumPost;
use App\Behavior\Timestampable;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\EmailNotificationRepository;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=EmailNotificationRepository::class)
 *
 */
class EmailNotification
{
    use Timestampable;

    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     * @Groups({"list"})
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="emailNotificationUser")
     * @ORM\JoinColumn(nullable=false)
     */
    private $user;

    /**
     * @ORM\Column(type="boolean", nullable=true)
     * @Groups({"list"})
     */
    private $sent;

    /**
     * @ORM\Column(type="string", length=50, nullable=true)
     */
    private $type;

    /**
     * @ORM\ManyToOne(targetEntity=Itinerary::class, inversedBy="emailNotificationUser")
     * @ORM\JoinColumn(nullable=true)
     */
    private $itinerary;

    /**
     * @ORM\ManyToOne(targetEntity=Announcement::class, inversedBy="emailNotificationAnnouncement")
     * @ORM\JoinColumn(nullable=true)
     */
    private $announcement;

    /**
     * @ORM\ManyToOne(targetEntity=ForumPost::class, inversedBy="emailNotificationForum")
     * @ORM\JoinColumn(nullable=true)
     */
    private $forumPost;

    /**
     * @ORM\Column(type="string", length=100, nullable=true)
     */
    private $title;

    /**
     * @ORM\Column(type="string", length=250, nullable=true)
     */
    private $message;

    /**
     * @ORM\Column(type="json", nullable="true")
     */
    private $attributes = [];

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $translationText;

    /**
     * @ORM\Column(type="string", length=255, nullable=true)
     */
    private $translationTitle;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private $extra = [];

    public function getId(): ?int
    {
        return $this->id;
    }

    public function isSent(): ?bool
    {
        return $this->sent;
    }

    public function setSent(bool $sent): self
    {
        $this->sent = $sent;

        return $this;
    }

    /**
     * Get the value of user
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * Set the value of user
     *
     * @return  self
     */
    public function setUser($user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get the value of type
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set the value of type
     *
     * @return  self
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get the value of itinerary
     */
    public function getItinerary()
    {
        return $this->itinerary;
    }

    /**
     * Set the value of itinerary
     *
     * @return  self
     */
    public function setItinerary($itinerary)
    {
        $this->itinerary = $itinerary;

        return $this;
    }

    /**
     * Get the value of announcement
     */
    public function getAnnouncement()
    {
        return $this->announcement;
    }

    /**
     * Set the value of announcement
     *
     * @return  self
     */
    public function setAnnouncement($announcement)
    {
        $this->announcement = $announcement;

        return $this;
    }

    /**
     * Get the value of forumPost
     */
    public function getForumPost()
    {
        return $this->forumPost;
    }

    /**
     * Set the value of forumPost
     *
     * @return  self
     */
    public function setForumPost($forumPost)
    {
        $this->forumPost = $forumPost;

        return $this;
    }

    /**
     * Get the value of title
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * Set the value of title
     *
     * @return  self
     */
    public function setTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    /**
     * Get the value of message
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * Set the value of message
     *
     * @return  self
     */
    public function setMessage($message)
    {
        $this->message = $message;

        return $this;
    }

    /**
     * Get the value of attributes
     */
    public function getAttributes(): ?array
    {
        return $this->attributes;
    }

    /**
     * Set the value of attributes
     *
     * @return  self
     */
    public function setAttributes(?array $attributes): self
    {
        $this->attributes = $attributes;

        return $this;
    }


    public function getTranslationText(): ?string
    {
        return $this->translationText;
    }

    public function setTranslationText(?string $translationText): self
    {
        $this->translationText = $translationText;

        return $this;
    }

    public function getTranslationTitle(): ?string
    {
        return $this->translationTitle;
    }

    public function setTranslationTitle(?string $translationTitle): self
    {
        $this->translationTitle = $translationTitle;

        return $this;
    }

    public function getExtra(): ?array
    {
        return $this->extra;
    }

    public function setExtra(?array $extra): self
    {
        $this->extra = $extra;

        return $this;
    }
}
