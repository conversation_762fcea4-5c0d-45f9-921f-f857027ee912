<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\SurveyRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=SurveyRepository::class)
 *
 * @ORM\HasLifecycleCallbacks()
 *
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=false, hardDelete=false)
 */
class Survey implements TranslatableInterface
{
    use TranslatableTrait;

    use AtAndBy;
    public const APPLY_ALL = 1;
    public const APPLY_COURSES = 2;
    public const APPLY_ANNOUNCEMENTS = 3;
    public const APPLY_MANUAL = 4;

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"list"})
     * @Groups({"update-course"})
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"list"})
     * @Groups({"update-course"})
     */
    private $name;

    /**
     * @ORM\Column(type="boolean")
     *
     * @Groups({"update-course"})
     */
    private $active;

    /**
     * @ORM\Column(type="text", nullable=true)
     *
     * @Groups({"update-course"})
     */
    private $description;

    /**
     * @ORM\OneToMany(targetEntity=NpsQuestion::class, mappedBy="survey", cascade={"persist","remove"})
     */
    private $npsQuestions;

    /**
     * @ORM\OneToMany(targetEntity=SurveyCourse::class, mappedBy="survey", orphanRemoval=true, cascade={"persist","remove"})
     */
    private $surveyCourses;

    /**
     * @ORM\OneToMany(targetEntity=SurveyAnnouncement::class, mappedBy="survey", orphanRemoval=true, cascade={"persist","remove"})
     */
    private $surveyAnnouncements;

    /**
     * @ORM\Column(type="smallint")
     *
     * @Groups({"update-course"})
     */
    private int $applyTo = self::APPLY_ALL;

    /**
     * @ORM\Column(type="array", nullable=true)
     */
    private $meta = [];

    /**
     * @ORM\Column(type="boolean", nullable=true)
     */
    private $isMain;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="announcementAprovedCriterias")
     *
     * @ORM\JoinColumn(name="created_by_id", referencedColumnName="id", nullable=false)
     */
    private $createdBy;

    public function __construct()
    {
        $this->npsQuestions = new ArrayCollection();
        $this->surveyCourses = new ArrayCollection();
        $this->surveyAnnouncements = new ArrayCollection();
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return Collection<int, NpsQuestion>
     */
    public function getNpsQuestions(): Collection
    {
        return $this->npsQuestions;
    }

    public function setNpsQuestions($npsQuestions): self
    {
        $this->npsQuestions = $npsQuestions;

        return $this;
    }

    public function addNpsQuestion(NpsQuestion $npsQuestion): self
    {
        if (!$this->npsQuestions->contains($npsQuestion)) {
            $this->npsQuestions[] = $npsQuestion;
            $npsQuestion->setSurvey($this);
        }

        return $this;
    }

    public function removeNpsQuestion(NpsQuestion $npsQuestion): self
    {
        if ($this->npsQuestions->removeElement($npsQuestion)) {
            // set the owning side to null (unless already changed)
            if ($npsQuestion->getSurvey() === $this) {
                $npsQuestion->setSurvey(null);
            }
        }

        return $this;
    }

    public function __toString()
    {
        return $this->getId() . '-' . $this->getName();
    }

    public function setSurveyCourses(ArrayCollection $surveyCourses): self
    {
        $this->surveyCourses = $surveyCourses;

        return $this;
    }

    /**
     * @return Collection<int, SurveyCourse>
     */
    public function getSurveyCourses(): Collection
    {
        return $this->surveyCourses;
    }

    public function addSurveyCourse(SurveyCourse $surveyCourse): self
    {
        if (!$this->surveyCourses->contains($surveyCourse)) {
            $this->surveyCourses[] = $surveyCourse;
            $surveyCourse->setSurvey($this);
        }

        return $this;
    }

    public function removeSurveyCourse(SurveyCourse $surveyCourse): self
    {
        if ($this->surveyCourses->removeElement($surveyCourse)) {
            // set the owning side to null (unless already changed)
            if ($surveyCourse->getSurvey() === $this) {
                $surveyCourse->setSurvey(null);
            }
        }

        return $this;
    }

    public function setSurveyAnnouncements(ArrayCollection $surveyAnnouncements): self
    {
        $this->surveyAnnouncements = $surveyAnnouncements;

        return $this;
    }

    /**
     * @return Collection<int, SurveyAnnouncement>
     */
    public function getSurveyAnnouncements(): Collection
    {
        return $this->surveyAnnouncements;
    }

    public function addSurveyAnnouncement(SurveyAnnouncement $surveyAnnouncement): self
    {
        if (!$this->surveyAnnouncements->contains($surveyAnnouncement)) {
            $this->surveyAnnouncements[] = $surveyAnnouncement;
            $surveyAnnouncement->setSurvey($this);
        }

        return $this;
    }

    public function removeSurveyAnnouncement(SurveyAnnouncement $surveyAnnouncement): self
    {
        if ($this->surveyAnnouncements->removeElement($surveyAnnouncement)) {
            // set the owning side to null (unless already changed)
            if ($surveyAnnouncement->getSurvey() === $this) {
                $surveyAnnouncement->setSurvey(null);
            }
        }

        return $this;
    }

    public function getApplyTo(): ?int
    {
        return $this->applyTo;
    }

    public function setApplyTo(int $applyTo): self
    {
        $this->applyTo = $applyTo;

        return $this;
    }

    public function getMeta(): ?array
    {
        return $this->meta;
    }

    public function setMeta(?array $meta): self
    {
        $this->meta = $meta;

        return $this;
    }

    public function isIsMain(): ?bool
    {
        return $this->isMain;
    }

    public function setIsMain(?bool $isMain): self
    {
        $this->isMain = $isMain;

        return $this;
    }
}
