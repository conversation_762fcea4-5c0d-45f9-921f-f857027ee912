<?php

declare(strict_types=1);

namespace App\Service\TaskCron;

use App\Entity\ZipFileTask;
use App\Service\Annoucement\CronJob\ZipFileTaskService as ZipFileTaskServiceCronJob;
use App\Service\SlotManagerService;
use App\Service\TemplatedEmail\TemplatedEmailService;
use App\Service\ZipFileTask\ZipFileTaskService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class ZipFileTaskExecutorService
{
    private const TASK_TIMEOUT_MINUTES = 30;

    private EntityManagerInterface $em;
    private ZipFileTaskService $zipFileService;
    private ZipFileTaskServiceCronJob $zipFileServiceCronJob;
    private LoggerInterface $logger;
    private TemplatedEmailService $templatedEmailService;
    private SlotManagerService $slotManagerService;

    public function __construct(
        EntityManagerInterface $em,
        ZipFileTaskService $zipFileService,
        ZipFileTaskServiceCronJob $zipFileServiceCronJob,
        LoggerInterface $logger,
        TemplatedEmailService $templatedEmailService,
        SlotManagerService $slotManagerService
    ) {
        $this->em = $em;
        $this->zipFileService = $zipFileService;
        $this->zipFileServiceCronJob = $zipFileServiceCronJob;
        $this->logger = $logger;
        $this->templatedEmailService = $templatedEmailService;
        $this->slotManagerService = $slotManagerService;
    }

    public function execute(ZipFileTask $task): void
    {
        $task->setStatus(ZipFileTask::STATUS_IN_PROGRESS);
        $this->em->persist($task);
        $this->em->flush();

        try {
            $this->zipFileServiceCronJob->zip($task);
            $task->setStatus(ZipFileTask::STATUS_COMPLETED);
            $task->setFinishedAt(new \DateTimeImmutable());
        } catch (\Throwable $e) {
            $task->setStatus(ZipFileTask::STATUS_FAILED);

            $this->templatedEmailService->sendErrorNotification(
                'ZipFileTask',
                $e,
                $task->getId()
            );

            throw $e;
        } finally {
            $this->em->persist($task);
            $this->em->flush();
        }
    }

    public function getAvailableExecutionSlot(): ExecutionSlot
    {
        // Delegamos la verificación de slots disponibles al SlotManagerService
        return $this->slotManagerService->getAvailableZipTaskExecutionSlot();
    }
}
