<?php

declare(strict_types=1);

namespace App\Modules\Announcement\Builder;

use App\Entity\Announcement;

interface Builder
{
    public function setCourse(): void;

    public function setConfigurationBase(): void;

    public function setBonus(): void;

    public function setStudents(): void;

    public function setTutorGroup(): void;

    public function setCommunication(): void;

    public function setSurvey(): void;

    public function setDiploma(): void;

    public function setAlertTutor(): void;

    public function getAnnouncement(): Announcement;
}
