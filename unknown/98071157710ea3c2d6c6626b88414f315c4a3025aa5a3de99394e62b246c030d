<?php

declare(strict_types=1);

namespace App\Command\V2;

use App\V2\Domain\LTI\LtiKeyProvider;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class LTIGenerateKeysCommand extends Command
{
    public function __construct(
        private readonly LtiKeyProvider $ltiKeyProvider,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('app:lti:generate-keys');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        try {
            $io->info('Generating keys');
            $this->ltiKeyProvider->generateKeys();
            $io->success('Keys have been generated');

            return Command::SUCCESS;
        } catch (InfrastructureException $e) {
            $io->error($e->getMessage());

            return Command::FAILURE;
        }
    }
}
