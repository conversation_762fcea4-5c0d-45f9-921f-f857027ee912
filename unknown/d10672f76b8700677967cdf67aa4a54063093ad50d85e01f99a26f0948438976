<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\CategorizeAnswers;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CategorizeAnswers>
 *
 * @method CategorizeAnswers|null find($id, $lockMode = null, $lockVersion = null)
 * @method CategorizeAnswers|null findOneBy(array $criteria, array $orderBy = null)
 * @method CategorizeAnswers[]    findAll()
 * @method CategorizeAnswers[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CategorizeAnswersRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CategorizeAnswers::class);
    }

    public function add(CategorizeAnswers $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(CategorizeAnswers $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
