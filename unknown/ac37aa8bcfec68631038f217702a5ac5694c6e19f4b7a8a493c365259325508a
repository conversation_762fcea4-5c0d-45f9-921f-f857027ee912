<?php

declare(strict_types=1);

namespace App\Service\Course\Report\General;

use App\Entity\Course;
use App\Service\Course\Common\UserCourseService;
use App\Service\Course\Traits\CourseReportTrait;
use App\Utils\SpreadsheetUtil;
use App\Utils\TimeUtils;
use App\Utils\ToolsUtils;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use Psr\Log\LoggerInterface;
use Symfony\Component\Intl\Locales;

class CourseStatsReport extends BaseReport
{
    use CourseReportTrait;
    private CourseReportService $courseReportService;
    private LoggerInterface $logger;
    private UserCourseService $userCourseService;
    private EntityManagerInterface $em;

    public function __construct(
        CourseReportService $courseReportService,
        LoggerInterface $logger,
        UserCourseService $userCourseService,
        EntityManagerInterface $em
    ) {
        $this->courseReportService = $courseReportService;
        $this->logger = $logger;
        $this->userCourseService = $userCourseService;
        $this->em = $em;
    }

    private function setDefaultData(Course $course, $params)
    {
        $this->courseReportService->setLocale($course->getLocale());
        $this->courseReportService->setParams($params);

        $strategy = new ContextStatsService($this->courseReportService);
        $this->dataChapters = $strategy->getStats($course);
        $this->dataUsersChapters = $strategy->getStatsUserChaperCourse($course);

        $this->params = $params;
    }

    public function generate($course = null, array $params = []): string
    {
        $this->logger->info('Course_generateReport[Started]');
        if (!($course instanceof Course)) {
            $course = $this->courseReportService->getDataInfoCourse($course);
        }
        if (null === $course) {
            throw new \RuntimeException('Course not found');
        }

        $this->setDefaultData($course, $params);

        $courseDetails = $params['courseDetails'] ?? true;
        $userIds = $this->userCourseService->getAllUsersIds($course, $params);

        $this->chapterExcelDetails($course);
        if ($courseDetails) {
            $this->courseDetailsExcel($course, $userIds);
        }

        $this->logger->info('Course_generateReport[Completed]');

        return $this->courseReportService->getCourseSaveFilesDirectoryName($course);
    }

    public function generateFromCourse(Course $course, array $params = []): string
    {
        $this->logger->info('Course_generateReport[Started]');
        if (!($course instanceof Course)) {
            $course = $this->courseReportService->getDataInfoCourse($course);
        }
        if (null === $course) {
            throw new \RuntimeException('Course not found');
        }

        $this->setDefaultData($course, $params);

        $userIds = $this->userCourseService->getAllUsersIds($course, $params);

        $saveReport = $this->courseDetailsExcel($course, $userIds, true);

        $this->logger->info('Course_generateReport[Completed]');

        return $saveReport;
    }

    private function chapterExcelDetails(Course $course)
    {
        $this->logger->info('Course_chapterExcelDetails[Started]');
        $chaptersIds = $this->courseReportService->getChaptersParams();
        if (empty($chaptersIds)) {
            return;
        }

        /** @var SpreadsheetUtil $report */
        $report = $this->courseReportService->createSpreadSheet(
            'report.excel.chapter_detail',
            'report.excel.chapter_detail.general_info'
        );

        $chaptersInfoPage = $this->courseReportService->getDataPagesGeneralInfo($this->dataChapters);

        $this->chapterDetailGeneralInfo($report, $chaptersInfoPage); // genera la pagina Informacion general dentro del excel
        $this->chapterDetailsEvaluation($report);
        $report->activateSheetByName($this->getTransReport('report.excel.chapter_detail.general_info'));
        $report->saveReport($this->courseReportService->getCourseSaveFilesDirectoryName($course));
        $this->logger->info('Course_chapterExcelDetails[Completed] : ');
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    private function chapterDetailsEvaluation(SpreadsheetUtil $report)
    {
        foreach ($this->dataChapters as $dataChapter) {
            if (empty($dataChapter['questions'])) {
                continue;
            }

            $questions = $dataChapter['questions'];
            if (\count($questions) > 0) {
                $headerAnswer = 0;
                $chapterId = $dataChapter['id'];
                $dataInfoEvaluations = $this->courseReportService->getChapertsDetailsEvaluationData($questions, $headerAnswer);
                $this->chapterDetailsEvaluationHeader($report, $chapterId, $headerAnswer);
                $report->fromArray($dataInfoEvaluations, '--', 'A2');

                $dataUserEvaluation = $this->courseReportService->getUserChapterCourseEvaluationData($this->dataUsersChapters[$chapterId]);
                $this->chapterDetailsEvaluationActivityHeaders($report, $chapterId);
                $report->fromArray($dataUserEvaluation, '--', 'A2');
            }
        }
    }

    public function chapterDetailsEvaluationActivityHeaders(SpreadsheetUtil $report, $idChapter)
    {
        $report->addSheet($this->getTransReport('report.excel.chapter_detail.evaluation_activity') . ' ' . $idChapter);

        $headers = [
            $this->getTransReport('report.excel.course_details.course_statics.id_user'),
            $this->getTransReport('report.excel.course_details.course_statics.code'),
            $this->getTransReport('report.excel.course_details.course_statics.names'),
            $this->getTransReport('report.excel.course_details.course_statics.surnames'),
            $this->getTransReport('report.excel.headers.participants.email'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_activity.attemptId'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_detail.idquestion'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_detail.question'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_activity.right'),
        ];

        $report->setHeaders($headers, true, true, false, 1, [
            'fontSize' => 14,
            'bold' => true,
            'color' => '000000',
            'fill' => [
                'A' => 'fce5cd',
                'B' => 'fff2cc',
                'C' => 'd9d2e9',
                'D' => 'd9d2e9',
                'E' => 'd9d2e9',
                'F' => 'fff2cc',
                'G' => 'f4cccc',
                'H' => 'a4c2f4',
                'I' => 'd9ead3',
            ],
        ]);

        $report->setCellAlignmentHorizontal(['A', 'B', 'F', 'G'], Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal('I', Alignment::HORIZONTAL_CENTER);
    }

    private function chapterDetailsEvaluationHeader(SpreadsheetUtil $report, $idChapter, $headerAnswer)
    {
        $report->addSheet($this->getTransReport('report.excel.chapter_detail.evaluation_detail') . ' ' . $idChapter, true);

        $headers = [
            $this->getTransReport('report.excel.chapter_detail.evaluation_detail.idquestion'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_detail.question'),
            '% ' . $this->getTransReport('report.excel.chapter_detail.evaluation_detail.correct_answer'),
            $this->getTransReport('report.excel.chapter_detail.evaluation_detail.success'),
        ];

        if ($headerAnswer > 0) {
            $i = 1;
            while ($i <= $headerAnswer) {
                $headers[] = $this->getTransReport('report.excel.chapter_detail.evaluation_detail.answers') . ' ' . $i;
                ++$i;
            }
        }

        $report->setHeaders($headers, true, true, false, 1, [
            'fontSize' => 14,
            'bold' => true,
            'color' => '000000',
        ]);

        $report->setCellAlignmentHorizontal(['A', 'B', 'C'], Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal('D', Alignment::HORIZONTAL_CENTER);
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    private function chapterDetailGeneralInfo(SpreadsheetUtil $report, $chaptersInfoPage)
    {
        $report->addSheet($this->getTransReport('report.excel.chapter_detail.general_info'));
        $report->setHeaders(
            $this->chapterDetailGeneralInfoHeader(),
            true,
            true,
            true,
            1,
            [
                'fontSize' => 14,
                'bold' => true,
                'color' => '000000',
                'fill' => [
                    'A' => 'f4cccc',
                    'B' => 'a4c2f4',
                    'C' => 'd9d2e9',
                    'D' => 'd9ead3',
                    'E' => 'b4a7d6',
                    'F' => 'c9daf8',
                    'G' => 'fce5cd',
                ],
            ]
        );

        $report->setCellAlignmentHorizontal('A', Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal(['E', 'F', 'G'], Alignment::HORIZONTAL_CENTER);
        $report->fromArray($chaptersInfoPage, '--', 'A2');
    }

    private function chapterDetailGeneralInfoHeader(): array
    {
        return [
            $this->getTransReport('report.excel.course_details.chapters.activity'),
            $this->getTransReport('report.excel.course_details.chapters.name'),
            $this->getTransReport('report.excel.course_details.chapters.type'),
            $this->getTransReport('report.excel.course_details.chapters.class'),
            $this->getTransReport('report.excel.chapter_detail.general_info.items'),
            $this->getTransReport('report.excel.course_details.general.total_time'),
            '% ' . $this->getTransReport('report.excel.course_details.general.ending'),
        ];
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    private function courseDetailsExcel(Course $course, $userIds, bool $activeTitleCourse = false): string
    {
        $this->logger->info('course_Details_Excel[Started]');

        $excelName = 'report.excel.course_details';
        if ($activeTitleCourse) {
            $excelName = ToolsUtils::str_without_accents(trim($course->getName()));
            $excelName .= '-' . $this->getTransReport('report.excel.course_details');
        }

        /** @var SpreadsheetUtil $report */
        $report = $this->courseReportService->createSpreadSheet(
            $excelName,
            'report.excel.course_details.title'
        );

        $this->courseExcelConfigStats($report, $course);
        $this->courseExcelDetailsStats($report, $course, $userIds);
        $this->courseExcelActivitiesInfo($report);
        if ($this->validateUsersAndChapterIds($userIds)) {
            $this->courseExcelCourseStatistics($report, $course, $userIds);
            $this->courseExcelFormativeActivity($report, $course, $userIds);
        }
        $report->activateSheetByName($this->getTransReport('report.excel.course_details.title'));
        $saveReport = $this->courseReportService->getCourseSaveFilesDirectoryName($course);
        $report->saveReport($saveReport);
        $this->logger->info('course_Details_Excel[Completed] : ');

        return $saveReport . DIRECTORY_SEPARATOR . $excelName;
    }

    /**
     * @throws Exception
     *                   * @throws \Exception
     */
    private function courseExcelConfigStats(SpreadsheetUtil $report, Course $course)
    {
        $params = $this->params;
        $report->addSheet($this->getTransReport('report.excel.course_details.config'));
        $dateFrom = $course->getCreatedAt()->format('d/m/Y');
        $dateFromDisplay = false;
        if (isset($params['dateFrom'])) {
            $dateFrom = \DateTimeImmutable::createFromFormat('Y-m-d', $params['dateFrom']);
            $dateFrom = $dateFrom->format('d/m/Y');
            $dateFromDisplay = true;
        }

        $dateTo = $this->courseReportService->getLastStartDateCourse($course);
        $datetodisplay = false;
        if (isset($params['dateTo'])) {
            $dateTo = \DateTimeImmutable::createFromFormat('Y-m-d', $params['dateTo']);
            $dateTo = $dateTo->format('d/m/Y');
            $datetodisplay = true;
        }

        $report->mergeCells('A2:C2')
            ->setCellValue(
                'A2',
                $this->getTransReport('report.excel.course_details.config.title_config'),
                ['fontSize' => 16, 'bold' => true]
            );

        $report->mergeCells('A4:E4')
            ->setCellValue(
                'A4',
                $this->getTransReport('report.excel.course_details.config.period'),
                ['fontSize' => 16, 'bold' => true]
            );

        $report->setCellValue(
            'A6',
            $this->getTransReport('report.excel.course_details.config.from'),
            ['fontSize' => 14]
        );

        $report->setCellValue(
            'B6',
            $dateFrom,
            ['fontSize' => 14]
        );

        $report->setCellValue(
            'A7',
            $this->getTransReport('report.excel.course_details.config.until'),
            ['fontSize' => 14]
        );

        $report->setCellValue(
            'B7',
            $dateTo,
            ['fontSize' => 14]
        );

        $report->mergeCells('A10:E10')
            ->setCellValue(
                'A10',
                $this->getTransReport('report.excel.course_details.config.filter'),
                ['fontSize' => 16, 'bold' => true]
            );

        $report->setCellValue(
            'A12',
            'CourseID',
            ['fontSize' => 14]
        );

        $report->setCellValue(
            'B12',
            $course->getId(),
            ['fontSize' => 14]
        );

        if (isset($params['userId'])) {
            $report->setCellValue(
                'A13',
                $this->getTransReport('stats.export.filter.activeUsers', 'messages'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'B13',
                $this->getTransReport('stats.export.filter.activeUsers_val_yes', 'messages'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'A14',
                $this->getTransReport('report.excel.course_details.config.range_date_start'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'B14',
                $dateFromDisplay ?
                    $this->getTransReport('report.excel.course_details.course_statics.yes') :
                    $this->getTransReport('report.excel.course_details.course_statics.no'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'A15',
                $this->getTransReport('report.excel.course_details.config.end_range_date'),
                ['fontSize' => 14]
            );

            $report->setCellValue(
                'B15',
                $datetodisplay ?
                    $this->getTransReport('report.excel.course_details.course_statics.yes') :
                    $this->getTransReport('report.excel.course_details.course_statics.no'),
                ['fontSize' => 14]
            );

            $report->setAutosize('A', 'D');
            $report->setCellAlignmentHorizontal(['A2', 'A4', 'A10'], Alignment::HORIZONTAL_CENTER);
        }
    }

    private function headersCouserExcelDetails(): array
    {
        return [
            $this->getTransReport('report.excel.course_details.general.id'),
            $this->getTransReport('report.excel.course_details.general.name'),
            $this->getTransReport('report.excel.course_details.general.activities'),
            $this->getTransReport('report.excel.course_details.general.total_people'),
            $this->getTransReport('report.excel.course_details.general.not_started'),
            '% ' . $this->getTransReport('report.excel.course_details.general.not_started'),
            $this->getTransReport('report.excel.course_details.general.in_progress'),
            '% ' . $this->getTransReport('report.excel.course_details.general.in_progress'),
            $this->getTransReport('report.excel.course_details.general.finalized'),
            '% ' . $this->getTransReport('report.excel.course_details.general.finalized'),
            $this->getTransReport('report.excel.course_details.general.total_time'),
            $this->getTransReport('report.excel.course_details.general.estimed'),
            $this->getTransReport('report.excel.course_details.general.average'),
            $this->getTransReport('report.excel.course_details.general.authorship'),
            $this->getTransReport('report.excel.course_details.general.create_date'),
        ];
    }

    private function fillColorHeaders(): array
    {
        return [
            'A' => 'f4cccc',
            'B' => 'a4c2f4',
            'C' => 'd9d2e9',
            'D' => 'fce5cd',
            'E' => 'e6b8af',
            'F' => 'e6b8af',
            'G' => 'c9daf8',
            'H' => 'c9daf8',
            'I' => 'd9d2e9',
            'J' => 'd9d2e9',
            'k' => 'd9ead3',
            'L' => 'b6d7a8',
            'M' => 'b4a7d6',
            'N' => 'fff2cc',
            'O' => 'f4cccc',
        ];
    }

    /**
     * @throws Exception
     *                   * @throws \Exception
     */
    private function courseExcelDetailsStats(SpreadsheetUtil $report, Course $course, $userIds)
    {
        $report->addSheet($this->getTransReport('report.excel.course_details.general'), true);
        $report->setHeaders(
            $this->headersCouserExcelDetails(),
            true,
            true,
            true,
            1,
            [
                'fontSize' => 14,
                'bold' => true,
                'color' => '000000',
                'fill' => $this->fillColorHeaders(),
            ]
        );
        $report->setCellAlignmentHorizontal('A', Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal(['C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'], Alignment::HORIZONTAL_CENTER);

        $courseInfo = $this->courseReportService->courseExcelDetailsGetInfoArray($course, $userIds);
        $report->fromArray([$courseInfo], '--', 'A2');
    }

    private function courseExcelActivitiesInfo(SpreadsheetUtil $report)
    {
        $report->addSheet($this->getTransReport('report.excel.course_details.chapters'));
        $headers = [
            $this->getTransReport('report.excel.course_details.chapters.activity'),
            $this->getTransReport('report.excel.course_details.chapters.name'),
            $this->getTransReport('report.excel.course_details.chapters.type'),
            $this->getTransReport('report.excel.course_details.chapters.class'),
            '% ' . $this->getTransReport('report.excel.course_details.general.ending'),
            $this->getTransReport('report.excel.course_details.general.spent'),
        ];
        $report->setHeaders($headers, true, true, true, 1, [
            'fontSize' => 14,
            'bold' => true,
            'color' => '000000',
            'fill' => [
                'A' => 'f4cccc',
                'B' => 'a4c2f4',
                'C' => 'd9d2e9',
                'D' => 'f9cb9c',
                'E' => 'b4a7d6',
                'F' => 'ead1dc',
            ],
        ]);

        $report->setCellAlignmentHorizontal('A', Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal(['E', 'F', 'B1', 'C1', 'D1'], Alignment::HORIZONTAL_CENTER);
        $chaptersInfoPage = $this->courseReportService->getDataCoursePagesGeneralInfo($this->dataChapters);
        $report->fromArray($chaptersInfoPage, '--', 'A2');
        $this->logger->info('Course_courseExcel_activitiesInfo[Completed]');
    }

    private function courseExcelCourseStatisticsHeaders()
    {
        return [
            $this->getTransReport('report.excel.course_details.course_statics.id_user'),
            $this->getTransReport('report.excel.course_details.course_statics.code'),
            $this->getTransReport('report.excel.course_details.course_statics.names'),
            $this->getTransReport('report.excel.course_details.course_statics.surnames'),
            $this->getTransReport('report.excel.headers.participants.email'),
            $this->getTransReport('fundae_catalogs.fields.state.active', 'messages'),
            $this->getTransReport('report.excel.course_details.course_statics.languages_course'),
            $this->getTransReport('report.excel.headers.announcement.start_date'),
            $this->getTransReport('report.excel.headers.announcement.end_date'),
            $this->getTransReport('user.configureFields.time_spent', 'messages'),
            $this->getTransReport('report.excel.headers.announcement.status'),
            // $this->getTransReport('report.excel.course_details.course_statics.points'),
        ];
    }

    private function courseExcelCourseStatisticsFill()
    {
        return [
            'A' => 'fce5cd',
            'B' => 'fff2cc',
            'C' => 'a4c2f4',
            'D' => 'c9daf8',
            'E' => 'd9d2e9',
            'F' => 'c9daf8',
            'G' => 'fce5cd',
            'H' => 'd9ead3',
            'I' => 'f4cccc',
            'J' => 'd9d2e9',
            'K' => 'e6b8af',
            // 'L' => 'd9d2e9',
        ];
    }

    private function courseExcelCourseStatisticsData(Course $course, $userIds): array
    {
        $data = $this->courseReportService->getCourseStatisticsData($course, $userIds);

        $locales = Locales::getNames();
        foreach ($data as $key => $fila) {
            $data[$key]['startedAt'] = $fila['startedAt'] ?? '-';
            $data[$key]['finishedAt'] = $fila['finishedAt'] ?? '-';
            $data[$key]['timeSpent'] = $fila['timeSpent'] ? TimeUtils::formatTime($fila['timeSpent']) : '-';
            $data[$key]['status'] = $this->getTransReport('report.excel.course_details.course_statics.' . $fila['status']);
            $data[$key]['active'] = $this->getTransReport('report.excel.course_details.course_statics.' . $fila['active']);
            $data[$key]['courseLocale'] = $fila['courseLocale'] ? $locales[$fila['courseLocale']] : '-';
        }

        return $data;
    }

    private function courseExcelCourseStatistics(SpreadsheetUtil $report, Course $course, array $userIds)
    {
        $report->addSheet($this->getTransReport('report.excel.course_details.course_statics'));

        $report->setHeaders(
            $this->courseExcelCourseStatisticsHeaders(),
            true,
            true,
            true,
            1,
            [
                'fontSize' => 14,
                'bold' => true,
                'color' => '000000',
                'fill' => $this->courseExcelCourseStatisticsFill(),
            ]
        );

        $row = 2;

        $data = $this->courseExcelCourseStatisticsData($course, $userIds);
        $report->setCellAlignmentHorizontal(['A', 'B'], Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal(['F', 'G', 'H', 'I', 'J', 'K', 'L'], Alignment::HORIZONTAL_CENTER);
        $report->fromArray($data, '--', "A$row");
    }

    private function courseExcelFormativeActivityHeaders(): array
    {
        return [
            $this->getTransReport('report.excel.course_details.course_statics.id_user'),
            $this->getTransReport('report.excel.course_details.course_statics.code'),
            $this->getTransReport('report.excel.course_details.course_statics.names'),
            $this->getTransReport('report.excel.course_details.course_statics.surnames'),
            $this->getTransReport('report.excel.headers.participants.email'),
            $this->getTransReport('report.excel.course_details.chapters.activity'),
            $this->getTransReport('report.excel.course_details.chapters.name'),
            $this->getTransReport('report.excel.course_details.chapters.type'),
            $this->getTransReport('report.excel.headers.announcement.start_date'),
            $this->getTransReport('report.excel.headers.announcement.end_date'),
            $this->getTransReport('fundae_catalogs.fields.state.title', 'messages'),
            $this->getTransReport('report.excel.course_details.training_activity.time_spent'),
        ];
    }

    private function courseExcelFormativeActivityFill(): array
    {
        return [
            'A' => 'fce5cd',
            'B' => 'fff2cc',
            'C' => 'a4c2f4',
            'D' => 'c9daf8',
            'E' => 'd9d2e9',
            'F' => 'c9daf8',
            'G' => 'fce5cd',
            'H' => 'd9ead3',
            'I' => 'f4cccc',
            'J' => 'd9d2e9',
            'K' => 'e6b8af',
            'L' => 'd9d2e9',
        ];
    }

    private function courseExcelFormativeActivityDataReport(SpreadsheetUtil $report, Course $course, array $userIds)
    {
        $baseQb = $this->courseReportService->courseFormativeActivityData($course, $userIds);
        $offset = 0;
        $pageSize = 500;

        $row = 2;

        do {
            $lastRow = null;
            $toInsert = [];
            /** @var array $userCourseInfo */
            $paginatedData = $this->courseReportService->courseFormativeActivityPagination($baseQb, $offset, $pageSize);
            foreach ($paginatedData as $userCourseInfo) {
                /** @var \DateTimeImmutable|null $started */
                $started = $userCourseInfo['startedAt'] ?? null;
                /** @var \DateTimeImmutable|null $finished */
                $finished = $userCourseInfo['finishedAt'] ?? null;
                $status = $this->getTransReport('report.excel.course_details.course_statics.' . $userCourseInfo['status']);
                $toInsert[] = [
                    'USER_ID' => $userCourseInfo['userId'],
                    'USER_CODE' => $userCourseInfo['userCode'],
                    'USER_NAME' => $userCourseInfo['firstName'],
                    'LAST_NAME' => $userCourseInfo['lastName'],
                    'USER_EMAIL' => $userCourseInfo['email'],
                    'CHAPTER_ID' => $userCourseInfo['chapterId'],
                    'CHAPTER_NAME' => $userCourseInfo['chapterName'],
                    'CHAPTER_TYPE' => $userCourseInfo['chapterType'],
                    'STARTED' => $started ? $started->format('d-m-Y H:i:s') : '-',
                    'FINISHED' => $finished ? $finished->format('d-m-Y H:i:s') : '-',
                    'STATUS' => $status,
                    'TOTAL_TIME' => TimeUtils::formatTime($userCourseInfo['timeSpent']) ?? '00:00:00',
                ];
                $lastRow = empty($userCourseInfo) ? null : $userCourseInfo;
            }
            ++$offset;
            $report->fromArray($toInsert, '--', "A$row");
            $row = $report->getLastRow();
        } while (null !== $lastRow);
    }

    private function courseExcelFormativeActivity(SpreadsheetUtil $report, Course $course, array $userIds)
    {
        $report->addSheet($this->getTransReport('report.excel.course_details.training_activity'));

        $report->setHeaders(
            $this->courseExcelFormativeActivityHeaders(),
            true,
            true,
            true,
            1,
            [
                'fontSize' => 14,
                'bold' => true,
                'color' => '000000',
                'fill' => $this->courseExcelFormativeActivityFill(),
            ]
        );

        $report->setCellAlignmentHorizontal(['A', 'B', 'F'], Alignment::HORIZONTAL_LEFT);
        $report->setCellAlignmentHorizontal(['L', 'J', 'I'], Alignment::HORIZONTAL_CENTER);

        $this->courseExcelFormativeActivityDataReport($report, $course, $userIds);
    }

    private function validateUsersAndChapterIds(array $userIds): bool
    {
        if (empty($userIds)) {
            $this->logger->error('Course_courseExcel_formativeActivity[No usersIds]');

            return false;
        }

        $chaptersIds = $this->courseReportService->getChaptersParams();
        if (empty($chaptersIds)) {
            $this->logger->error('Course_courseExcel_formativeActivity[No chapters]');

            return false;
        }

        return true;
    }

    private function getTransReport(string $wordTranslate, string $tag = 'reports'): string
    {
        return $this->courseReportService->getTransReport($wordTranslate, $tag);
    }
}
