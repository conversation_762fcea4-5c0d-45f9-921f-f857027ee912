<?php

declare(strict_types=1);

namespace App\Campus\Controller\General;

use App\Campus\Service\Ranking\RankingService;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\NotificationRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController.
 *
 * @Route("/api")
 */
class ApiRankingController extends ApiBaseController
{
    private $em;
    private RankingService $rankingService;

    /**
     * ApiController constructor.
     */
    public function __construct(
        LoggerInterface $logger,
        UserRepository $userRepository,
        CourseRepository $courseRepository,
        AnnouncementRepository $announcementRepository,
        EntityManagerInterface $em,
        NotificationRepository $notificationRepository,
        TranslatorInterface $translator,
        SettingsService $settings,
        RankingService $rankingService
    ) {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settings);
        $this->em = $em;
        $this->rankingService = $rankingService;
    }

    /**
     * @Rest\Get("/ranking/{id}", name="api_ranking_get")
     *
     * @return Response
     */
    public function getRanking(Request $request)
    {
        try {
            $response = [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $this->rankingService->getRanking()
            ];

            return $this->sendResponse($response, ['groups' => ['ranking', 'ranking_filter']]);
        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'message' => $e->getMessage()
            ];

            return $this->sendResponse($response);
        }
    }

    /**
     * @Rest\Get("/ranking/getVisualMode", name="api_ranking_visualMode")
     *
     * @return Response
     */
    public function getVisualMode()
    {
        $response = [
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'visualModel' => $this->settings->get('ranking.mode'),
            ]
        ];

        return $this->sendResponse($response);
    }
}
