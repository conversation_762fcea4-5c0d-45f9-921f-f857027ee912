<?php


namespace App\Campus\Controller\General;


use App\Entity\UserCourseChapter;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;



/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class ApiRouletteWheelController extends ApiBaseController
{
    protected $em;

    /**
     * @Rest\Get("/chapters/roulette/{id}/fetchQuestion", name="api_roulette_start")
     *
     * @return Response
     */
    public function getRouleeteLetters(UserCourseChapter $userCourseChapter){
        try {
            $code = Response::HTTP_OK;

            $letters = $userCourseChapter->getChapter()->getRouletteWords();
            $letterArray = [];
            $time = 0;

            foreach ($letters as $letter){
                if (!is_null($letter->getWord())){
                    $letterArray[] = $letter;
                    $time += $this->getParameter('app.roulette.time.letter');
                }
            }

            if(isset($userCourseChapter->getData()['answers'])){
                $time -= count($userCourseChapter->getData()['answers']) * $this->getParameter('app.roulette.time.letter');
            }

            $response = [
                'status' => $code,
                'error' => false,
                'data' =>  [
                    'questions' => $letterArray,
                    'time' => $time,
                ]
            ];

        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to register the user - Error: {' . $e->getMessage() . '}'
            ];
        }


        return $this->sendResponse($response, array('groups' => array('roulette')));
    }

    /**
     * @Rest\Post("/chapters/roulette/{id}/check", name="api_roulette_check")
     *
     * @return Response
     */
    public function checkRouleeteLetter(Request $request, UserCourseChapter $userCourseChapter){
        try {
            $code = Response::HTTP_OK;
            $requestData = \json_decode($request->getContent(), true);

            $letterId = $requestData['questionId'];
            $value = $requestData['value'];

            $letters = $userCourseChapter->getChapter()->getRouletteWords();

            $correct = false;

            foreach ($letters as $letter){
                if ($letter->getId() === $letterId && $letter->getWord() === $value){
                   $correct = true;
                }
            }

            $response = [
                'status' => $code,
                'error' => false,
                'data' =>  [
                    'correct' => $correct,
                ]
            ];

        } catch (\Exception $e) {
            $response = [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => 'An error has occurred trying to check the word - Error: {' . $e->getMessage() . '}'
            ];
        }

        return $this->sendResponse($response);
    }
}
