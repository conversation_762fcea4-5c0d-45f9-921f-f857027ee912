<?php

namespace App\Campus\Controller\General;

use App\Entity\FilterCategory;
use App\Entity\Library;
use App\Entity\LibraryCategory;
use App\Entity\LibraryCategoryTranslation;
use App\Entity\LibraryComment;
use App\Entity\LibraryViews;
use App\Entity\User;
use App\Repository\AnnouncementRepository;
use App\Repository\CourseRepository;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class ApiController
 * @package App\Controller
 *
 * @Route("/api")
 */
class ApiLibraryController extends ApiBaseController
{
    private EntityManagerInterface $em;

    public function __construct(
        LoggerInterface $logger,
        UserRepository $userRepository,
        CourseRepository $courseRepository,
        AnnouncementRepository $announcementRepository,
        TranslatorInterface $translator,
        EntityManagerInterface $em,
        SettingsService $settings
    ) {
        parent::__construct($logger, $userRepository, $courseRepository, $announcementRepository, $translator, $settings);
        $this->em = $em;
    }

    /**
     * @Rest\Get("/library/filters", name="api_library_filters")
     *
     * @return Response
     */
    public function libraryFilters(): Response
    {
        $filterCategories = $this->em->getRepository(FilterCategory::class)->findAll();

        $response = [
            'status' => Response::HTTP_OK,
            'error'  => false,
            'data'   => [
                'filters' => [
                    "categories"    => $filterCategories,
                ],
            ],
        ];

        return $this->sendResponse($response, array('groups' => array('library')));
    }

    /**
     * @Rest\Get("/library/categories", name="api_get_categories")
     * @return Response
     */
    public function getCategories(): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $locale = $user->getLocale() ?? $this->settings->get('app.defaultLanguage');
        $categories = $this->em->getRepository(LibraryCategory::class)->findAll();

        $data = [];
        foreach ($categories as $category) {
            $libraries = $category->getLibraries()->toArray();
            $total = count($libraries);
            $selected = [];
            if ($total > 0) {
                $entries = array_rand($libraries, min($total, 4));
                if (is_array($entries)) {
                    foreach ($entries as $entry) {
                        $selected[] = $libraries[$entry];
                    }
                } else {
                    $selected[] = $libraries[$entries];
                }
            }

            /** @var LibraryCategoryTranslation $translation */
            $translation = $category->translate($locale);
            $data[] = [
                'id' => $category->getId(),
                'name' => $translation->getName() ?? $category->getName(),
                'libraries' => $selected
            ];
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $data,
            'error' => false
        ], ['groups' => ['category', 'library']]);
    }

    /**
     * @Rest\Get("/library/{category}/libraries/{page}", name="api_get_category_libraries", requirements={"page"="\d+"})
     * @param Request $request
     * @param LibraryCategory $category
     * @param int $page
     * @return Response
     */
    public function getLibraries(Request $request, LibraryCategory $category, int $page = 1): Response
    {
        $query = $request->get('query', '');
        $user = $this->getUser();
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'category' => $category,
                'data' => $this->em->getRepository(Library::class)->getLibrariesPaginated($category, $page, '')
            ]
        ], ['groups' => ['library', 'category']]);
    }

    /**
     * @Rest\Get("/library/ramdom/{category}", name="api_get_random_library_elements")
     * @param LibraryCategory $category
     * @return Response
     */
    public function getRandomLibraryElements(LibraryCategory $category): Response
    {
        $user = $this->getUser();
        $libraries = $this->em->getRepository(Library::class)->createQueryBuilder('l')->select('l')
            ->where('l.category = :category AND l.locale = :locale')
            ->setParameters(['category' => $category, 'locale' => $user->getLocale()])
            ->setMaxResults(5)
            ->getQuery()
            ->getResult();
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $libraries
        ], ['groups' => ['library']]);
    }

    /**
     * @Rest\Get("/library/{id}", name="api_get_library")
     * @param Library $library
     * @return Response
     */
    public function getLibrary(Library $library): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $library
        ], ['groups' => ['library']]);
    }

    /**
     * @Rest\Post("/library/{library}/comment", name="api_save_library_comment")
     * @param Request $request
     * @param Library $library
     * @return Response
     */
    public function saveComment(Request $request, Library $library): Response
    {
        try {
            if (!$library->isEnableComments() && !$library->isEnableRating()) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'Library does not allow comments or rating'
                ]);
            }

            $content = json_decode($request->getContent(), true);
            $libraryComment = new LibraryComment();
            $libraryComment->setLibrary($library);

            $comment = empty($content['comment']) ? null : $content['comment'];
            $rating = isset($content['rating']) ? (int)$content['rating'] : null;

            $saveComment = false;
            if ($library->isEnableComments() && $comment) {
                $libraryComment->setVisible($this->settings->get('app.library.comments.default_is_visible'));
                $libraryComment->setComment($comment);
                $saveComment = true;
            }
            if ($library->isEnableRating() && $rating != null) {
                $libraryComment->setRating($rating);
                $saveComment = true;
            }

            if ($saveComment) {
                $this->em->persist($libraryComment);
                $this->em->flush();
                $createdBy = $libraryComment->getCreatedBy();

                return $this->sendResponse([
                    'status' => Response::HTTP_CREATED,
                    'error' => false,
                    'data' => $libraryComment->isVisible() ? [
                        'id' => $libraryComment->getId(),
                        'comment' => $libraryComment->getComment(),
                        'rating' => $libraryComment->getRating(),
                        'user' => [
                            'firstName' => $createdBy->getFirstName(),
                            'lastName' => $createdBy->getLastName(),
                            'avatar' => $createdBy->getAvatar()
                        ]
                    ] : null
                ]);
            }

            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'No data has been provided'
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage()
            ]);
        }
    }


    /**
     * @Rest\Get("/library/{library}/comments/{page}", name="api_get_library_comments", requirements={"page"="\d+"})
     * @param Library $library
     * @param int $page
     * @return Response
     */
    public function getLibraryComments(Library $library, int $page = 1): Response
    {
        $query = $this->em->getRepository(LibraryComment::class)->createQueryBuilder('l')
            ->andWhere('l.library = :library AND l.visible = true')
            ->setParameters(['library' => $library])
            ->orderBy('l.createdAt', 'DESC');

        $countQuery = clone $query;
        $data = [];
        if ($page !== null) {
            $pageSize = $this->settings->get('app.library.data.page_size');
            $offset = ($page - 1) * $pageSize;
            $totalItems = $countQuery->select('COUNT(l.id) as total')->getQuery()->getSingleScalarResult();
            $query->setMaxResults($pageSize)
                ->setFirstResult($offset);
            $data['total-items'] = (int)$totalItems;
        }

        $query->leftJoin('l.createdBy', 'u')
            ->select('l');
        $comments = $query->getQuery()->getResult();
        $data['items'] = [];

        /** @var LibraryComment $comment */
        foreach ($comments as $comment) {
            /** @var User $createdBy */
            $createdBy = $comment->getCreatedBy();
            $data['items'][] = [
                'id' => $comment->getId(),
                'comment' => $comment->getComment(),
                'rating' => $comment->getRating(),
                'user' => [
                    'firstName' => $createdBy->getFirstName(),
                    'lastName' => $createdBy->getLastName(),
                    'avatar' => $createdBy->getAvatar()
                ]
            ];
        }

        try {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data
            ], ['groups' => ['comment']]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage()
            ]);
        }
    }

    /**
     * @Rest\Post("/library/{library}/view", name="api_add_library_view")
     * @param Library $library
     * @return Response
     */
    public function addLibraryView(Library $library): Response
    {
        try {
            $user = $this->getUser();

            $libraryView = new LibraryViews();
            $libraryView->setLibrary($library)
                ->setUser($user)
                ->setCreatedAt(new \DateTimeImmutable());

            $this->em->persist($libraryView);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false,
                'data' => 'Created'
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage()
            ]);
        }
    }
}
