<?php

declare(strict_types=1);

namespace App\Campus\Controller\Survey;

use App\Campus\Controller\Base\BaseController;
use App\Campus\Service\Survey\SurveyQuestionService;
use App\Campus\Service\Survey\SurveyService;
use App\Entity\Course;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @Route("/api")
 */
class SurveyController extends BaseController
{
    private SurveyService $surveyService;

    private SurveyQuestionService $surveyQuestionService;

    public function __construct(
        SettingsService $settings,
        EntityManagerInterface $em,
        TranslatorInterface $translator,
        SurveyService $surveyService,
        SurveyQuestionService $surveyQuestionService
    ) {
        parent::__construct($settings, $em, $translator);
        $this->surveyService = $surveyService;
        $this->surveyQuestionService = $surveyQuestionService;
    }

    /**
     * @Rest\Post("/evaluation", name="api-evaluation")
     */
    public function saveEvaluation(Request $request): Response
    {
        $requestData = json_decode($request->getContent(), true);
        $this->surveyService->validateRequestData($requestData);

        try {
            $message = $this->translator->trans(
                'message_api.controller.evaluation_course',
                [],
                'message_api',
                $this->getUser()->getLocaleCampus()
            );

            $this->surveyService->processEvaluation($requestData);

            return $this->sendResponse([
                'status' => Response::HTTP_CREATED,
                'error' => false,
                'data' => $message,
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }

    /**
     * @Rest\Get("/nps/questions/{id}", name="api_nps_questions")
     */
    public function getQuestions(Course $course): Response
    {
        try {
            $questions = $this->surveyQuestionService->getQuestionSurvey($course);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $questions,
            ]);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }
    }
}
