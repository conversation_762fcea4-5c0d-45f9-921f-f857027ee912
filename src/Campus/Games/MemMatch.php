<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Chapter;
use App\Entity\Parejas;
use App\Entity\TimeGame;
use App\Enum\Games as EnumGameFormula;
use App\Enum\Games as GamesEnum;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;

class MemMatch extends Game
{
    protected EntityManagerInterface $em;
    protected RequestStack $requestStack;
    protected SettingsService $settings;

    public function __construct(EntityManagerInterface $em, SettingsService $settings, RequestStack $requestStack)
    {
        $this->em = $em;
        $this->requestStack = $requestStack;
        $this->settings = $settings;
        parent::__construct($em);
    }

    public function getQuestions($userCourseChapter): array
    {
        $repository = $this->em->getRepository(Parejas::class);
        $memoryQuestions = $repository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        return $this->getFormattedQuestions($memoryQuestions, $userCourseChapter);
    }

    private function getFormattedQuestions($memoryQuestions, $userCourseChapter): array
    {
        $host = $this->requestStack->getCurrentRequest()->getSchemeAndHttpHost();
        $path = $host . '/' . $this->settings->get(GamesEnum::MATCH_UPLOADS_PATH_KEY) . '/';
        $questions = [];
        $timeGame = $this->em->getRepository(TimeGame::class)->findOneBy(['chapter' => $userCourseChapter->getChapter()->getId()]);
        $time = $timeGame ? $timeGame->getTime() : 30;

        foreach ($memoryQuestions as $match) {
            foreach ($match->getParejasImagens() as $answer) {
                $text = $answer->getTexto();
                $image = $answer->getImagen() ? $path . $answer->getImagen() : '';

                $originalQuestion = [
                    'id' => $answer->getId(),
                    'text' => $text,
                    'image' => $image,
                ];

                $copyQuestion = [
                    'id' => $answer->getId() * -1,
                    'text' => $text,
                    'image' => $image,
                ];

                $questions[] = $originalQuestion;
                $questions[] = $copyQuestion;
            }
        }

        return [
            'questions' => $questions,
            'time' => \intval($time),
        ];
    }

    public function check($userCourseChapter, $answers): array
    {
        return [
            'correct' => $this->isCorrect($answers),
        ];
    }

    private function isCorrect($answers): bool
    {
        if (!isset($answers->cards)) {
            return false;
        }

        $cards = $answers->cards;

        return abs($cards[1]) === abs($cards[0]);
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal']) || !isset($data['totalQuestions'])) {
            return 0;
        }

        $answers = $data['answers'];
        $nAnswers = \count($answers);
        $rightAnswers = 0;
        $time = 0;
        $nAttempts = \array_key_exists('attempts', $data) ? \count($data['attempts']) : 1;
        $maxTime = \array_key_exists('timeTotal', $data) ? \intval($data['timeTotal']) : 0;

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                if (isset($answer['correct']) && $answer['correct']) {
                    ++$rightAnswers;
                }
                $time = $answer['time'];
            }
        }

        if ($time >= $maxTime || 0 == $rightAnswers) {
            return 0;
        }

        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF
            + (EnumGameFormula::BASE_HALF * ($remainingTime / $maxTime));
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
