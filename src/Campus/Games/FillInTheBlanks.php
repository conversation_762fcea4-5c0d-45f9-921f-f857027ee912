<?php

declare(strict_types=1);

namespace App\Campus\Games;

use App\Entity\Chapter;
use App\Entity\Fillgaps as FillgapsEntity;
use App\Entity\Holes;
use App\Enum\Games as EnumGameFormula;

class FillInTheBlanks extends Game
{
    public function getQuestions($userCourseChapter): array
    {
        $gapsRepository = $this->em->getRepository(FillgapsEntity::class);
        $gapsQuestions = $gapsRepository->findBy([
            'chapter' => $userCourseChapter->getChapter()->getId(),
        ]);

        $questions = $this->getFormattedQuestions($gapsQuestions);
        $timeTotal = $this->getTotalTime($questions);

        return [
            'questions' => $questions,
            'time' => $timeTotal,
        ];
    }

    private function getFormattedQuestions($gapsQuestions): array
    {
        $questions = [];
        $timeTotal = 0;
        foreach ($gapsQuestions as $question) {
            $timeTotal += $question->getTime();

            $answers = [];
            foreach (json_decode($question->getAnswers()) as $answer) {
                $answers[] = [
                    'id' => $answer->id,
                    'answer' => $answer->text,
                ];
            }

            $questions[] = [
                'id' => $question->getId(),
                'text' => $question->getText(),
                'answers' => $answers,
                'time' => $question->getTime(),
            ];
        }

        return $questions;
    }

    private function getTotalTime($questions)
    {
        $time = 0;
        foreach ($questions as $question) {
            $time += $question['time'];
        }

        return $time;
    }

    public function check($userCourseChapter, $answers): array
    {
        $questionId = $answers->questionId;
        $attempts = $answers->attempts;

        $gapsRepository = $this->em->getRepository(FillgapsEntity::class);
        $holesRepository = $this->em->getRepository(Holes::class);
        $fillgap = $gapsRepository->find($questionId);

        $data = [];
        if ($fillgap) {
            usort($attempts, function ($a, $b) {
                return $a->id - $b->id;
            });

            foreach ($attempts as $att) {
                $hollow = $holesRepository->findOneBy([
                    'fillgap' => $fillgap->getId(),
                    'hole' => $att->id,
                ]);

                $isCorrect = $hollow && $hollow->getAnswer() == $att->word;
                $data[] = [
                    'id' => $att->id,
                    'word' => $att->word,
                    'correct' => $isCorrect ? 1 : 0,
                ];
            }
        }

        return [
            'attempts' => $data,
        ];
    }

    public function calculateGamePoints($data, $args = null)
    {
        if (!$args instanceof Chapter) {
            return null;
        }

        if (!isset($data['answers']) || !$data['answers'] > 0
            || !isset($data['timeTotal']) || !isset($data['holes'])) {
            return 0;
        }

        $chapterType = $args->getType();
        $answers = $data['answers'];
        $nAnswers = \count($answers);
        $nAnswersAttempts = 0;
        $rightQuestions = 0;
        $time = isset($data['time']) ? $data['time'] : 0;
        $nAttempts = \array_key_exists('attempts', $data) && \is_array($data['attempts']) ? \count($data['attempts']) : 1;
        $failsAnswers = 0;
        $maxTime = $data['timeTotal'];

        if($nAnswers > 0){
            foreach ($answers as $answer) {
                if (\count($answer['attempts']) > 0) {
                    ++$nAnswersAttempts;
                }
                foreach ($answer['attempts'] as $answerAttempt) {
                    if(isset($answerAttempt['correct'])){
                        if ($answerAttempt['correct']) {
                            ++$rightQuestions;
                        }
                        if (!$answerAttempt['correct']) {
                            ++$failsAnswers;
                        }
                    }
                }
                $time += $answer['time'];
            }
        }

        $completionPercentage = $rightQuestions > 0 ? ($nAnswersAttempts / $nAnswers) : 0;
        if ($completionPercentage < $chapterType->getPercentageCompleted()) {
            return 0;
        }
        $remainingTime = ($maxTime - $time) > 0 ? ($maxTime - $time) : 1;
        $basePercentage = EnumGameFormula::BASE_HALF +
            (EnumGameFormula::BASE_QUARTER * pow(EnumGameFormula::FILL_IN_THE_BLANKS_PENALTY, $failsAnswers) + EnumGameFormula::BASE_QUARTER * ($remainingTime / $maxTime));
        $adjustedPercentage = pow($basePercentage, EnumGameFormula::E_L_COEF);
        $attemptsCorrectionPercentage = $adjustedPercentage * pow(EnumGameFormula::ATTEMPTS_COEF, $nAttempts - 1);

        return $attemptsCorrectionPercentage;
    }
}
