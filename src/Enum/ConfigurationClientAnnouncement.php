<?php

namespace App\Enum;

final class ConfigurationClientAnnouncement
{
    public const COMMUNICATION = 'COMMUNICATION';
    public const CERTIFICATE = 'CERTIFICATE';
    public const SURVEY = 'SURVEY';
    public const ALERT = 'ALERT';
    public const TEMPORALIZATION = 'TEMPORALIZATION';
    public const BONIFICATION = 'BONIFICATION';
    public const ACCESS_CONTENT = 'ACCESS_CONTENT';
    public const DIGITAL_SIGNATURE = 'DIGITAL_SIGNATURE';
    public const COST = 'COST';
    public const NOTIFICATION_ACTIVATE_ANNOUNCEMENT = 'NOTIFICATION_ACTIVATE_ANNOUNCEMENT';  
    public const CONFIGURATION_IBEROSTAR = 'CONFIGURATION_IBEROSTAR';   
    public const REPORT = 'REPORT';   

}
