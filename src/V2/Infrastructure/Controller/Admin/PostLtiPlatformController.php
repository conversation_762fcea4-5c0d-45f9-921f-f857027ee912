<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\PostLtiPlatformCommand;
use App\V2\Domain\Shared\Url\InvalidUrlException;
use App\V2\Domain\Shared\Url\Url;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Validator\Admin\LTI\LtiPlatformValidator;
use App\V2\Infrastructure\Validator\Uuid\UuidValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostLtiPlatformController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     * @throws InvalidUrlException
     * @throws InvalidUuidException
     */
    public function __invoke(Request $request, string $registrationId): Response
    {
        UuidValidator::validateUuid($registrationId);

        $data = json_decode($request->getContent(), true);

        LtiPlatformValidator::validatePostLtiPlatform($data);

        $this->execute(
            new PostLtiPlatformCommand(
                registrationId: new Uuid($registrationId),
                name: $data['name'],
                audience: $data['audience'],
                oidcAuthenticationUrl: new Url($data['oidc_authentication_url']),
                oauth2AccessTokenUrl: new Url($data['oauth2_access_token_url']),
                jwksUrl: new Url($data['jwks_url']),
            )
        );

        return new JsonResponse(
            data: [],
            status: Response::HTTP_CREATED
        );
    }
}
