<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Query\Admin\GetCourseCreators;
use App\V2\Domain\Course\Creator\CourseCreatorCriteria;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\Course\Creator\CourseCreatorTransformer;
use App\V2\Infrastructure\Response\ApiResponseContent;
use App\V2\Infrastructure\Validator\Id\IdValidator;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetCourseCreatorsController extends QueryBusAccessor
{
    public function __invoke(Request $request, int $courseId): Response
    {
        IdValidator::validateId($courseId);

        $courseCreatorsCollection = $this->ask(
            new GetCourseCreators(
                criteria: CourseCreatorCriteria::createEmpty()
                    ->filterByCourseId($courseId),
                withCreators: true,
            )
        );

        return new JsonResponse(
            data: ApiResponseContent::createFromData(
                CourseCreatorTransformer::fromCollectionToArray($courseCreatorsCollection)
            )->toArray(),
            status: Response::HTTP_OK,
        );
    }
}
