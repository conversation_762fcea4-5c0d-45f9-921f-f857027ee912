<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\Entity\User;
use App\V2\Application\DTO\User\PaginatedUserListItemDTOCollection;
use App\V2\Application\Query\Admin\GetUsers;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\Sort;
use App\V2\Domain\Shared\Criteria\SortableField;
use App\V2\Domain\Shared\Criteria\SortCollection;
use App\V2\Domain\Shared\Criteria\SortDirection;
use App\V2\Infrastructure\Bus\QueryBusAccessor;
use App\V2\Infrastructure\User\UserCriteriaTransformer;
use App\V2\Infrastructure\User\UserListItemDTOTransformer;
use App\V2\Infrastructure\Validator\Admin\GetUsersValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class GetManagersController extends QueryBusAccessor
{
    /**
     * @throws CollectionException
     * @throws ValidatorException
     */
    public function __invoke(Request $request): Response
    {
        $queryParameters = $request->query->all();

        GetUsersValidator::validatePaginatedSearchRequest($queryParameters);

        $userCriteria = UserCriteriaTransformer::fromArray($queryParameters);
        $userCriteria->filterByRole(User::ROLE_MANAGER)
            ->filterByIsActive(true)
            ->sortBy(
                new SortCollection([
                    new Sort(
                        new SortableField('firstName'),
                        SortDirection::ASC
                    ),
                    new Sort(
                        new SortableField('lastName'),
                        SortDirection::ASC
                    ),
                ])
            );

        /** @var PaginatedUserListItemDTOCollection $paginatedDTOCollection */
        $paginatedDTOCollection = $this->ask(new GetUsers(
            criteria: $userCriteria
        ));

        return new JsonResponse(
            data: [
                'data' => UserListItemDTOTransformer::fromPaginatedDTOCollectionToMinimalArray(
                    $paginatedDTOCollection,
                ),
            ],
            status: Response::HTTP_OK,
        );
    }
}
