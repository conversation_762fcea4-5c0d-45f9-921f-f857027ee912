<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Controller\Admin;

use App\V2\Application\Command\PostLtiRegistrationCommand;
use App\V2\Infrastructure\Bus\CommandBusAccessor;
use App\V2\Infrastructure\Validator\Admin\LTI\LtiRegistrationValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PostLtiRegistrationController extends CommandBusAccessor
{
    /**
     * @throws ValidatorException
     */
    public function __invoke(Request $request): Response
    {
        $data = json_decode($request->getContent(), true);

        LtiRegistrationValidator::validatePostRegistration($data);

        $this->execute(
            new PostLtiRegistrationCommand(
                name: $data['name'],
                clientId: $data['client_id'],
            )
        );

        return new JsonResponse(
            data: [],
            status: Response::HTTP_CREATED
        );
    }
}
