<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Response;

use App\V2\Domain\Shared\Collection\PaginatedCollection;
use App\V2\Domain\Shared\Criteria\Criteria;

class ApiResponseContent
{
    public function __construct(
        private readonly ?array $data = null,
        private readonly string $message = '',
        private readonly int $errorCode = 0,
        private array $metadata = [],
    ) {
    }

    public static function createFromData(array $data): self
    {
        return new self($data);
    }

    public static function createFromMessage(string $message): self
    {
        return new self(
            message: $message,
            errorCode: 1,
        );
    }

    public function setMetadata(array $metadata): self
    {
        $this->metadata = $metadata;

        return $this;
    }

    public function addMetadata(array $metadata): self
    {
        $this->metadata = array_merge($this->metadata, $metadata);

        return $this;
    }

    public function toArray(): array
    {
        return array_merge(
            null !== $this->data ? ['data' => $this->data] : [],
            empty($this->message) ? [] : ['message' => $this->message],
            0 === $this->errorCode ? [] : ['error' => $this->errorCode],
            empty($this->metadata) ? [] : ['metadata' => $this->metadata],
        );
    }

    public function addPaginationMetadata(PaginatedCollection $paginatedCollection, Criteria $criteria): self
    {
        if (null === $criteria->getPagination()) {
            return $this;
        }

        return $this->addMetadata([
            'page' => $criteria->getPagination()->page(),
            'total_pages' => (int) ceil($paginatedCollection->getTotalItems() / $criteria->getPagination()->limit()),
            'limit' => $criteria->getPagination()->limit(),
        ]);
    }
}
