<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Validator\Admin\LTI;

use App\V2\Infrastructure\Validator\CommonValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use Symfony\Component\Validator\Constraints\Collection;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;

class LtiRegistrationValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validatePostRegistration(array $data): void
    {
        $constraints = [
            new NotBlank(message: 'Body cannot be empty'),
            new Collection([
                'name' => [
                    new NotBlank(),
                    new Type('string'),
                ],
                'client_id' => [
                    new NotBlank(),
                    new Type('string'),
                ],
            ])];

        parent::validate($data, $constraints);
    }
}
