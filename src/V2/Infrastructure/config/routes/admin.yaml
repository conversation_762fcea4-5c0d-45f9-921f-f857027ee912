get_admin_users:
  path: /users
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetUsersController

get_admin_creators:
  path: users/creators
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetCreatorsController

get_admin_managers:
  path: users/managers
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetManagersController

get_admin_course_creators:
  path: /courses/{courseId}/creators
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetCourseCreatorsController

put_admin_course_creators:
  path: /courses/{courseId}/creators/{userId}
  methods: PUT
  controller: App\V2\Infrastructure\Controller\Admin\PutCourseCreatorController

delete_admin_course_creator:
  path: courses/{courseId}/creators/{userId}
  methods: DELETE
  controller: App\V2\Infrastructure\Controller\Admin\DeleteCourseCreatorController

post_lti_registration:
  path: /lti/registrations
  methods: POST
  controller: App\V2\Infrastructure\Controller\Admin\PostLtiRegistrationController

post_lti_platform:
  path: /lti/registrations/{registrationId}/platform
  methods: POST
  controller: App\V2\Infrastructure\Controller\Admin\PostLtiPlatformController

post_lti_tool:
  path: /lti/registrations/{registrationId}/tool
  methods: POST
  controller: App\V2\Infrastructure\Controller\Admin\PostLtiToolController

get_launch_lti_chapter:
  path: /courses/{courseId}/chapter/{chapterId}/lti-launch
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetLaunchLtiChapterController

get_announcement_managers:
  path: /announcements/{announcementId}/managers
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetAnnouncementManagersController
