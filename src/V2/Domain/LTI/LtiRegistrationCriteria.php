<?php

declare(strict_types=1);

namespace App\V2\Domain\LTI;

use App\V2\Domain\Shared\Criteria\CriteriaWithUuid;

/**
 * @extends CriteriaWithUuid<LtiRegistrationCriteria>
 */
class LtiRegistrationCriteria extends CriteriaWithUuid
{
    private ?string $clientId = null;
    private ?string $searchString = null;

    public function isEmpty(): bool
    {
        return parent::isEmpty()
            && null === $this->clientId
            && null === $this->searchString;
    }

    public function getClientId(): ?string
    {
        return $this->clientId;
    }

    public function getSearchString(): ?string
    {
        return $this->searchString;
    }

    public function filterByClientId(string $clientId): self
    {
        $this->clientId = $clientId;

        return $this;
    }

    public function filterBySearchString(string $searchString): self
    {
        $this->searchString = $searchString;

        return $this;
    }
}
