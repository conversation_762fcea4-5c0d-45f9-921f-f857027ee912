<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Domain\Bus\Command;

readonly class PostLtiRegistrationCommand implements Command
{
    public function __construct(
        private string $name,
        private string $clientId,
    ) {
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getClientId(): string
    {
        return $this->clientId;
    }
}
