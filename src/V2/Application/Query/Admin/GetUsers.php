<?php

declare(strict_types=1);

namespace App\V2\Application\Query\Admin;

use App\Entity\User;
use App\V2\Domain\Bus\Query;
use App\V2\Domain\User\UserCriteria;

readonly class GetUsers implements Query
{
    public function __construct(
        private UserCriteria $criteria,
        private ?User $requestUser = null
    ) {
    }

    public function getCriteria(): UserCriteria
    {
        return $this->criteria;
    }

    public function getRequestUser(): ?User
    {
        return $this->requestUser;
    }
}
