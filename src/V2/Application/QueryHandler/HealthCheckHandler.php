<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler;

use App\V2\Application\Query\HealthCheck;
use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;

readonly class HealthCheckHandler
{
    public function __construct(
        private UserRepository $userRepository
    ) {
    }

    public function handle(HealthCheck $query): void
    {
        $userCriteria = UserCriteria::createEmpty()->filterByEmail(
            new Email('<EMAIL>')
        );

        $this->userRepository->countBy($userCriteria);
    }
}
