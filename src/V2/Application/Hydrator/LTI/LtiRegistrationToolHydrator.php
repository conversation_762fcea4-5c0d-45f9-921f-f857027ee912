<?php

declare(strict_types=1);

namespace App\V2\Application\Hydrator\LTI;

use App\V2\Application\Hydrator\Hydrator;
use App\V2\Application\Hydrator\HydratorPriority;
use App\V2\Domain\LTI\LtiRegistration;
use App\V2\Domain\LTI\LtiRegistrationCollection;
use App\V2\Domain\LTI\LtiRegistrationHydrationCriteria;
use App\V2\Domain\LTI\LtiToolCriteria;
use App\V2\Domain\LTI\LtiToolRepository;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Hydrator\HydrationCriteria;
use App\V2\Domain\Shared\Uuid\UuidCollection;

class LtiRegistrationToolHydrator implements Hydrator
{
    private const HydratorPriority PRIORITY = HydratorPriority::First;

    public function __construct(
        private readonly LtiToolRepository $ltiToolRepository,
    ) {
    }

    public function getPriority(): HydratorPriority
    {
        return self::PRIORITY;
    }

    public function supports(HydrationCriteria $criteria): bool
    {
        return $criteria instanceof LtiRegistrationHydrationCriteria && $criteria->needsTool();
    }

    public function hydrate(Collection $collection, HydrationCriteria $criteria): void
    {
        if (!$collection instanceof LtiRegistrationCollection) {
            return;
        }

        if ($collection->isEmpty()) {
            return;
        }

        $ids = $collection->reduce(
            callback: function (array $carry, LtiRegistration $registration) {
                $carry[] = $registration->getId();

                return $carry;
            },
            initial: []
        );

        $ids = array_unique($ids);

        $toolCollection = $this->ltiToolRepository->findBy(
            LtiToolCriteria::createEmpty()->filterByRegistrationIds(new UuidCollection($ids))
        );

        $toolsByRegistration = [];
        foreach ($toolCollection->all() as $tool) {
            $toolsByRegistration[$tool->getRegistrationId()->value()] = $tool;
        }

        foreach ($collection->all() as $registration) {
            $tool = $toolsByRegistration[$registration->getId()->value()] ?? null;
            if (null === $tool) {
                continue;
            }
            $registration->setTool($tool);
        }
    }
}
