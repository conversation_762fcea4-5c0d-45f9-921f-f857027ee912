<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\MaterialCourse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MaterialCourse>
 *
 * @method MaterialCourse|null find($id, $lockMode = null, $lockVersion = null)
 * @method MaterialCourse|null findOneBy(array $criteria, array $orderBy = null)
 * @method MaterialCourse[]    findAll()
 * @method MaterialCourse[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MaterialCourseRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MaterialCourse::class);
    }

    public function add(MaterialCourse $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(MaterialCourse $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findAnnouncementMaterials(Announcement $announcement)
    {
        return $this->createQueryBuilder('mc')
            ->select('mc.id', 'mc.name', 'mc.isDownload', 'mc.isVisible', 'mc.isActive', 'mc.mimeType', 'mc.createdAt', 'mc.type', 'mc.typeMaterial', 'mc.filename')
            ->where('mc.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
    }
}
