<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\Parejas;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Parejas>
 *
 * @method Parejas|null find($id, $lockMode = null, $lockVersion = null)
 * @method Parejas|null findOneBy(array $criteria, array $orderBy = null)
 * @method Parejas[]    findAll()
 * @method Parejas[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ParejasRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Parejas::class);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function add(Parejas $entity, bool $flush = true): void
    {
        $this->_em->persist($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function remove(Parejas $entity, bool $flush = true): void
    {
        $this->_em->remove($entity);
        if ($flush) {
            $this->_em->flush();
        }
    }
}
