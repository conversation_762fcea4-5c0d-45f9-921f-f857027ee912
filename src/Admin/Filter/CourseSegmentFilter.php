<?php

namespace App\Admin\Filter;

use App\Entity\CourseSegment;
use App\Entity\Filter;
use App\Form\Type\Admin\Filter\CourseSegmentFilterType;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Contracts\Filter\FilterInterface;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FilterDataDto;
use EasyCorp\Bundle\EasyAdminBundle\Filter\FilterTrait;

class CourseSegmentFilter implements FilterInterface
{
    use FilterTrait;

    public static function new (string $propertyName, $label = null): self
    {
        return (new self())
            ->setFilterFqcn(__CLASS__)
            ->setProperty($propertyName)
            ->setLabel($label)
            ->setFormType(CourseSegmentFilterType::class);
    }


    public function apply (QueryBuilder $queryBuilder, FilterDataDto $filterDataDto, ?FieldDto $fieldDto, EntityDto $entityDto): void
    {
        if (in_array($filterDataDto->getComparison(), ['IN', 'NOT IN'])) {

            $em = $queryBuilder->getEntityManager();
            /**
             * @var $segments CourseSegment[]
             */
            $segments = $em->getRepository(CourseSegment::class)->getSegmentsByArray($filterDataDto->getValue());
            $segments_id = [];
            foreach ($segments as $segment) {
                // $this->logger->error("Manejo el filtro " . $filter->getName());
                $segments_id[$segment->getCourseSegmentCategory()->getId()][] = $segment->getId();
            }
            if (!empty($segments_id)) {
                foreach ($segments_id as $category_id => $segments) {
                    $queryBuilder->innerJoin('entity.courseSegments', 'cs' . $category_id);
                    if($filterDataDto->getComparison() == 'IN') $queryBuilder->andWhere($queryBuilder->expr()->in('cs' . $category_id . '.id', $segments));
                    else $queryBuilder->andWhere($queryBuilder->expr()->notIn('cs' . $category_id . '.id', $segments));
                }
            }
        }
    }
}
