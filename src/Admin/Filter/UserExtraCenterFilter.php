<?php

namespace App\Admin\Filter;


use App\Form\Type\Admin\Filter\UserExtraCenterFilterType;
use Doctrine\ORM\QueryBuilder;
use EasyCorp\Bundle\EasyAdminBundle\Contracts\Filter\FilterInterface;
use EasyCorp\Bundle\EasyAdminBundle\Dto\EntityDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FieldDto;
use EasyCorp\Bundle\EasyAdminBundle\Dto\FilterDataDto;
use EasyCorp\Bundle\EasyAdminBundle\Filter\FilterTrait;

class UserExtraCenterFilter implements FilterInterface
{
    use FilterTrait;

    public static function new (string $propertyName, $label = null): self
    {
        return (new self())
            ->setFilterFqcn(__CLASS__)
            ->setProperty($propertyName)
            ->setLabel($label)
            ->setFormType(UserExtraCenterFilterType::class);
    }


    public function apply (QueryBuilder $queryBuilder, FilterDataDto $filterDataDto, ?FieldDto $fieldDto, EntityDto $entityDto): void
    {
        $queryBuilder
            ->leftJoin($filterDataDto->getEntityAlias() . '.extra', 'extra')
            ->andWhere('extra.center = :center')
            ->setParameter('center', $filterDataDto->getValue());
    }
}