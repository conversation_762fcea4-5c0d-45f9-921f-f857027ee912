<?php

declare(strict_types=1);

namespace App\Admin\Traits;

use Doctrine\ORM\QueryBuilder;

trait UserCourseTrait
{
    public function addFilterByAnnouncement(QueryBuilder $qb, $announcement)
    {
        if ($announcement) {
            $qb->andWhere('uc.announcement = :announcement')
                ->setParameter('announcement', $announcement);
        } else {
            $qb->andWhere('uc.announcement is NULL');
        }
    }
}
