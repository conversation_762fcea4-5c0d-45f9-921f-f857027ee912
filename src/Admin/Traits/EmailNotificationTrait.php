<?php

declare(strict_types=1);

namespace App\Admin\Traits;

use App\Entity\EmailNotification;

trait EmailNotificationTrait
{
    protected function retrieveInfo($notification, $lang)
    {
        $emailNotificationRepository = $this->em->getRepository(EmailNotification::class);
        if (self::ITINERARY === $notification['type']) {
            $item = $emailNotificationRepository->findItineraryUser($lang, $notification['id']);
            if (empty($item)) {
                return $notification;
            }

            $notification['message'] = \sprintf(
                $this->translator->trans('notification.itinerary.subject', [], 'emailNotification', $lang),
                $item[0]['name']
            );

            $notification['title'] = \sprintf(
                $this->translator->trans('notification.itinerary.title', [], 'emailNotification', $lang),
                $item[0]['firstName']
            );
        } elseif (self::ANNOUNCED === $notification['type']) {
            $item = $emailNotificationRepository->findAnnouncementUser($lang, $notification['id']);
            if (empty($item)) {
                return $notification;
            }

            $notification['message'] = \sprintf(
                $this->translator->trans('notification.announcement.subject', [], 'emailNotification', $lang),
                $item[0]['name']
            );
            $notification['title'] = \sprintf(
                $this->translator->trans('notification.announcement.title', [], 'emailNotification', $lang),
                $item[0]['name']
            );
        }

        return $notification;
    }

    public function addNotificationInfo($notifications, $lang): array
    {
        $output = [];
        foreach ($notifications as $item) {
            $item = $this->retrieveInfo($item, $lang);
            $item['title'] = strip_tags($item['title']);
            $item['message'] = strip_tags($item['message']);
            array_push($output, $item);
        }

        return $output;
    }
}
