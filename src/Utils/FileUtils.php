<?php

namespace App\Utils;

class FileUtils
{

    public function getNameFileClean($filename, $extension)
    {
        $cleanedFilename = preg_replace('/[^a-zA-Z0-9\-\_]/', '_', $filename);

        // Convertir caracteres a minúsculas y eliminar tildes
        $cleanedFilename = strtolower($cleanedFilename);
        $cleanedFilename = str_replace(
            ['á', 'é', 'í', 'ó', 'ú', 'ñ'],
            ['a', 'e', 'i', 'o', 'u', 'n'],
            $cleanedFilename
        );

        $fileName = "{$cleanedFilename}.{$extension}";
        return "{$this->limitFileName($fileName)}";
    }

    private function limitFileName($fileName, $maxLength = 255)
    {
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $baseName = pathinfo($fileName, PATHINFO_FILENAME);

        // Si el nombre base es más largo que el límite, lo recortamos
        if (mb_strlen($baseName) > $maxLength - mb_strlen($extension) - 1) {
            $baseName = mb_substr($baseName, 0, $maxLength - mb_strlen($extension) - 1);
        }

        return $baseName . '.' . $extension;
    }
}
