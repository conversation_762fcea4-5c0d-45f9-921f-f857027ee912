<?php

namespace App\Utils\TimeZoneConverter;

trait UtcTimezoneTrait
{
    /**
     * Always convert the DateTime to UTC
     * @return void
     */
    public function toUtc(array $args = [])
    {
        $availableVariables = get_object_vars($this);
        $utcTimeZone = new \DateTimeZone('UTC');
        foreach ($availableVariables as $key => $value)
        {
            $variable = $this->{$key};
            if ($variable instanceof \DateTime || $variable instanceof \DateTimeImmutable)
            {
                if ($variable->getOffset() === 0) continue;//Already in UTC
                $this->{$key} = $variable->setTimezone($utcTimeZone);
            }
        }
    }

    /**
     * Always convert the time from UTC
     * @return void
     */
    public function fromUtc(array $args = [])
    {
        $availableVariables = get_object_vars($this);

        $utcTimeZone = new \DateTimeZone('UTC');
        $newTimeZone = null;
        if (array_key_exists('timezone', $availableVariables))
        {
            if (!empty($this->timezone)) $newTimeZone = new \DateTimeZone($this->timezone);
        }

        if (empty($newTimeZone)) $newTimeZone = new \DateTimeZone(date_default_timezone_get());

        foreach ($availableVariables as $key => $value)
        {
            $variable = $this->{$key};
            if ($variable instanceof \DateTime || $variable instanceof \DateTimeImmutable)
            {
                $variable = new \DateTimeImmutable($variable->format('Y-m-d H:i:s'), $utcTimeZone);// Set utc timezone to avoid errors
                $this->{$key} = $variable->setTimezone($newTimeZone);
            }
        }
    }
}
