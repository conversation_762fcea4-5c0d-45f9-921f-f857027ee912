<?php

namespace App\Form\Type\Admin;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Routing\RouterInterface;

 final class CourseTagFormType extends AbstractType
{
    private RouterInterface $router;

    public function __construct(RouterInterface $router)
    {
        $this->router = $router;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'required' => false,
            'label' => 'Tags',
            'attr' => [
                'placeholder' => 'separate tags with comma',
                'data-ajax' => $this->router->generate('tags'),
            ],
        ]);
    }

    public function getParent(): string
    {
        return TextType::class;
    }
}