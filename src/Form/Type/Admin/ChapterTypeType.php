<?php

namespace App\Form\Type\Admin;

use App\Entity\ChapterType;
use App\Form\DataTransformer\ChapterTypeToIntTransformer;
use App\Repository\ChapterTypeRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use App\Service\SettingsService;
use Symfony\Component\Security\Core\Security;

class ChapterTypeType extends AbstractType
{

    private ChapterTypeRepository $chapterTypeRepository;
    private ChapterTypeToIntTransformer $transformer;
    private SettingsService $settings;
    private Security $security;


    public function __construct (ChapterTypeRepository $chapterTypeRepository, ChapterTypeToIntTransformer $transformer, SettingsService $settings, Security $security)
    {
        $this->chapterTypeRepository = $chapterTypeRepository;
        $this->transformer           = $transformer;
        $this->settings = $settings;
        $this->security = $security;
    }


    public function buildForm (FormBuilderInterface $builder, array $options): void
    {
        $builder->addModelTransformer($this->transformer);
    }


    public function configureOptions (OptionsResolver $resolver)
    {
        
       $locale  = $this->security->getUser()->getLocale() ?? $this->settings->get('app.adminDefaultLanguage');
        $resolver->setDefaults([
            'choices'     => $this->chapterTypeRepository->getChoices(),
            'choice_attr' => $this->chapterTypeRepository->getChoicesAttr($locale),
            'expanded'    => true,
        ]);
    }


    public function getParent (): string
    {
        return ChoiceType::class;
    }
}
