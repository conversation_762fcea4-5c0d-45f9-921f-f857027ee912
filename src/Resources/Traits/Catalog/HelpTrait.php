<?php

namespace App\Resources\Traits\Catalog;

use App\Entity\HelpCategory;
use App\Entity\HelpText;
use App\Resources\DataFixtureBase\Help\HelpData;

trait HelpTrait
{
    public function saveHelp(){
        try {
            foreach (HelpData::DEFAULT_DATA as $type) {
                //    $helpText = $this->em->getRepository(HelpText::class)->findOneBy(['title' => $type['title']]);
                $helpCategory = $this->em->getRepository(HelpCategory::class)->findOneBy(['id' => $type['id']]);

                if (!$helpCategory) {
                    $helpCategory = new HelpCategory();
                }

                $helpCategory->setName($type['name']);
                $this->em->persist($helpCategory);

                foreach ($type['helpText'] as $helpTextNew) {
                    $helpText = $this->em->getRepository(HelpText::class)->findOneBy(['title' => $helpTextNew['title']]);
                    if (!$helpText) {
                        $helpText = new HelpText();
                        $helpText->setTitle($helpTextNew['title'])
                            ->setText($helpTextNew['text']);
                    }

                    $helpText->setTitle($helpTextNew['title'])
                        ->setText($helpTextNew['text'])
                        ->setCategory($helpCategory);

                    $this->em->persist($helpText);
                }
            }

            $this->em->flush();
        }catch (\Exception $e){
            throw new \Exception('Error saving HelpFixtures: ' . $e->getMessage());
        }
    }
}