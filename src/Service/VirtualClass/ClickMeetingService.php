<?php

declare(strict_types=1);

namespace App\Service\VirtualClass;

use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\Classroomvirtual;
use App\Entity\ClassroomvirtualType;
use App\Entity\ClassroomvirtualUser;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use App\Utils\FileUtils;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;

class ClickMeetingService extends BaseVirtualClassService implements ClassroomVirtualInterface
{
    protected $em;
    protected $settings;
    private $userRepository;
    private $excelReportGeneratorService;
    private $fileUtils;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        UserRepository $userRepository,
        ExcelReportGeneratorService $excelReportGeneratorService,
        FileUtils $fileUtils
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->userRepository = $userRepository;
        $this->excelReportGeneratorService = $excelReportGeneratorService;
        $this->fileUtils = $fileUtils;
    }

    private function getMeetingRestClient()
    {
        return new MeetingRestClientService(['api_key' => $this->settings->get('app.clickMeetingApiKey')]);
    }

    public function createRoom(array $parameters): array
    {
        try {
            // Si se proporciona un ID de Classroomvirtual, editar la reunión existente
            if (isset($parameters['idClassroomvirtual'])) {
                $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);
                if ($classroomvirtual) {
                    $this->editClickmeeting($parameters, (int)$classroomvirtual->getRoomId());
                    $announcementGroupSession = $classroomvirtual->getGroupSession();
                    $this->updateClassroomvirtual($classroomvirtual, $parameters);

                    return [
                        'status' => Response::HTTP_OK,
                        'error' => false,
                        'tutor_url' => $announcementGroupSession->getUrl(),
                    ];
                }
            }

            // Si no se proporciona un ID de Classroomvirtual, crear una nueva reunión Clickmeeting
            $meetingData = $this->registerUsersInTheProvider($parameters);
            $tutor_url = $meetingData['room_url'] . '/?l=' . $meetingData['access_role_hashes']->presenter;
            $parameters['id'] = $meetingData['id'];
            $parameters['type'] = $meetingData['type'];
            $parameters['start_url'] = $tutor_url;

            $this->saveRoomAndUsersInEasylearning($parameters, $meetingData);

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'tutor_url' => $tutor_url,
            ];
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $errorData = json_decode($errorMessage, true);
            if (JSON_ERROR_NONE === json_last_error() && isset($errorData['errors'][0]['message'])) {
                $errorMessage = $errorData['errors'][0]['message'];
            }

            return [
                'status' => 500,
                'error' => true,
                'data' => "$errorMessage",
            ];
        }
    }

    public function registerUsersInTheProvider(array $parameters): array
    {
        $client = $this->getMeetingRestClient();
        $duration = $this->convertirDuration($parameters['duration']);

        $params = [
            'name' => $parameters['name'],
            'starts_at' => $parameters['startsat'],
            'ends_at' => $parameters['endsat'],
            'duration' => $duration,
            'lobby_description' => $parameters['description'],
            'timezone' => $parameters['timezone'],
            'room_type' => 'meeting',
            'access_type' => 1,
            'permanent_room' => false,
            'lobby_enabled' => true,
            'password' => '',
            'registration' => [
                'template' => 1,
                'enabled' => false,
            ],
        ];

        $conference = $client->addConference($params);

        return (array) $conference->room;
    }

    public function saveRoomAndUsersInEasylearning(array $parameters, array $answerApiProvider): void
    {
        $classVirtualType = $this->em->getRepository(ClassroomvirtualType::class)->find(2);

        $classroomvirtual = $this->addClassroomvirtual($parameters, $classVirtualType);
        $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($parameters['idAnnouncementGroup']);

        $this->registerUsersClassroomvirtualClickmeeting($classroomvirtual, $announcementGroup);
    }

    public function deleteRoom(array $parameters): array
    {
        try {
            $client = $this->getMeetingRestClient();
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);

            $conference = $client->deleteConference($classroomvirtual->getRoomId());

            if ($conference) {
                $resultado = $this->em->getRepository(ClassroomvirtualUser::class)->deleteClassroomvirtualUser($classroomvirtual);
                $classroomvirtual->setState('Deleted');
                $this->em->persist($classroomvirtual);
                $this->em->flush();

                return [
                    'error' => 'task performed correctly',
                ];
            } else {
                return [
                    'error' => true,
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}",
            ];
        }
    }

    public function getAssistanceRoomUser(array $parameters): array
    {
        try {
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);
            $hoy = new \DateTime();

            if ($hoy <= $classroomvirtual->getStartsat()) {
                return [
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => 'the meeting did not take place',
                ];
            }

            $dataAttendees = $this->getParticipantsClickmeeting($classroomvirtual->getRoomid())['attendees'];
            $announcementGroupSession = $this->em->getRepository(AnnouncementGroupSession::class)->find($parameters['idAnnouncementGroupSession']);
            if ($dataAttendees) {
                $realDuration = $this->getDurationClickmeeting($classroomvirtual->getRoomId())['duration'];
                $resultado = $this->updateAssistanceAnnouncementGroupSession($announcementGroupSession, $dataAttendees, $realDuration);
                $this->updateStateClassroomvirtual($classroomvirtual, 'Realized');
            } else {
                $this->updateAssistanceAnnouncementGroupSessionNoMeeting($announcementGroupSession);
                $this->updateStateClassroomvirtual($classroomvirtual, 'Unrealized');
            }

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'correctly updated information',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error hasss occurred - Error: {$e->getMessage()}",
            ];
        }
    }

    public function getParticipantsclickMeeting(int $roomId): array
    {
        try {
            $client = $this->getMeetingRestClient();

            $sessions = $client->conferenceSessions($roomId);
            $asistentes = $client->conferenceSession($roomId, $sessions[0]->id);

            return [
                'attendees' => $asistentes->attendees,
            ];
        } catch (\Exception $e) {
            return [
                'data' => false,
            ];
        }
    }

    private function editClickmeeting(array $parameters, int $roomId)
    {
        $client = $this->getMeetingRestClient();
        $duration = $this->convertirDuration($parameters['duration']);

        $params = [
            'starts_at' => $parameters['startsat'],
            'duration' => $duration,
            'timezone' => $parameters['timezone'],
            'status' => 'active',
            'permanent_room' => false,
        ];

        $conference = $client->editConference($roomId, $params);

        if ($conference) {
            return $conference;
        } else {
            return false;
        }
    }

    public function registerUsersClickmeeting(int $roomId, User $user): array
    {
        $client = $this->getMeetingRestClient();

        try {
            $paramsRegistro = [
                'email' => $user->getEmail(),
                'nickname' => $user->getFirstName() . ' ' . $user->getLastName(),
                'role' => 'listener',
            ];
            $registration = $client->conferenceAutologinHash($roomId, $paramsRegistro);
            $urlListener = $this->getURLClickmeeting($roomId)['room_url'] . '/?l=' . $registration->autologin_hash;

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $urlListener,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}",
            ];
        }
    }

    public function getDurationClickmeeting(int $roomId): array
    {
        try {
            $client = $this->getMeetingRestClient();
            $conference = $client->conference($roomId);

            return [
                'duration' => $this->diferenciaMinutes($conference->conference->starts_at, $conference->conference->ends_at),
            ];
        } catch (\Exception $e) {
            return [
                'data' => false,
            ];
        }
    }

    public function getURLClickmeeting(int $roomId): array
    {
        try {
            $client = $this->getMeetingRestClient();
            $conference = $client->conference($roomId);

            return [
                'room_url' => $conference->conference->room_url,
            ];
        } catch (\Exception $e) {
            return [
                'data' => false,
            ];
        }
    }

    private function registerUsersClassroomvirtualClickmeeting(
        Classroomvirtual $classroomvirtual,
        AnnouncementGroup $announcementGroup
    ): void {
        $announcementUsers = $this->em->getRepository(AnnouncementUser::class)->findBy(['announcementGroup' => $announcementGroup]);

        foreach ($announcementUsers as $user) {
            $registerUserClickmeeting = $this->registerUsersClickmeeting($classroomvirtual->getRoomId(), $user->getUser());
            $this->addClassroomvirtualUser($user, $classroomvirtual, $registerUserClickmeeting['data']);
        }
    }

    public function updateAssistanceAnnouncementGroupSession(
        AnnouncementGroupSession $announcementGroupSession,
        array $attendees,
        int $duration
    ): array {
        $listParticipants = [];
        $participantsEmail = [];
        $attendeesEmail = $this->getAttendeesEmail($attendees);
        $announcementUsers = $this->em->getRepository(AnnouncementUser::class)->findBy(['announcementGroup' => $announcementGroupSession->getAnnouncementGroup()]);

        foreach ($announcementUsers as $announcementUser) {
            $paramParticipant = $this->getParamParticipant($announcementUser, $attendees, $attendeesEmail, $duration);
            array_push($participantsEmail, $paramParticipant->email);
            array_push($listParticipants, $paramParticipant);
        }

        $attendeesMissingEmail = array_diff($attendeesEmail, $participantsEmail);
        foreach ($attendeesMissingEmail as $missingEmail) {
            $paramParticipant = $this->getParamParticipantMissing($missingEmail, $attendees, $attendeesEmail, $duration);
            array_push($listParticipants, $paramParticipant);
        }

        $announcementGroupSession->setStudentAssistance($listParticipants);
        $this->em->persist($announcementGroupSession);
        $this->em->flush();

        return $listParticipants;
    }

    private function diferenciaMinutes(string $startsat, string $endsat): int
    {
        $fecha1 = new \DateTime($startsat); // fecha inicial
        $fecha2 = new \DateTime($endsat); // fecha de cierre
        $diferencia = $fecha1->diff($fecha2);
        $diferenciaMinutes = $diferencia->h * 60 + $diferencia->i;

        return $diferenciaMinutes;
    }

    private function convertirDuration(int $duration): string
    {
        // Calcula las horas
        $horas = floor($duration / 3600);

        // Calcula los minutos
        $minutos = floor(($duration % 3600) / 60);

        // Formatea la salida
        $horaFormateada = str_pad($horas, 2, '0', STR_PAD_LEFT); // Asegura dos dígitos en horas
        $minutoFormateado = str_pad($minutos, 2, '0', STR_PAD_LEFT); // Asegura dos dígitos en minutos

        return $horaFormateada . ':' . $minutoFormateado;
    }

    private function getAttendeesEmail(array $attendees): array
    {
        $attendeesEmail = [];
        foreach ($attendees as $key => $attendee) {
            $attendeesEmail[$key] = $attendee->email;
        }

        return $attendeesEmail;
    }

    private function getParamParticipant(
        AnnouncementUser $announcementUser,
        array $attendees,
        array $attendeesEmail,
        int $duration
    ) {
        $paramParticipant = new \stdClass();

        if (\in_array($announcementUser->getUser()->getEmail(), $attendeesEmail)) {
            $assistance = $attendees[array_search($announcementUser->getUser()->getEmail(), $attendeesEmail)];
            $PercentMinutesAttended = $this->diferenciaMinutes($assistance->start_date, $assistance->end_date) / $duration;
            $paramParticipant->assistance = $PercentMinutesAttended >= Classroomvirtual::MINIMUM_ATTENDANCE_TIME ? true : false;
        } else {
            $paramParticipant->assistance = false;
        }

        $paramParticipant->id = $announcementUser->getUser()->getId();
        $paramParticipant->email = $announcementUser->getUser()->getEmail();

        return $paramParticipant;
    }

    private function getParamParticipantMissing(string $email, array $attendees, array $attendeesEmail, int $duration)
    {
        $paramParticipant = new \stdClass();

        $assistant = $attendees[array_search($email, $attendeesEmail)];
        $paramParticipant->id = $assistant->id;
        $paramParticipant->email = $assistant->email;

        $PercentMinutesAttended = $this->diferenciaMinutes($assistant->start_date, $assistant->end_date) / $duration;
        $paramParticipant->assistance = $PercentMinutesAttended >= Classroomvirtual::MINIMUM_ATTENDANCE_TIME ? true : false;

        return $paramParticipant;
    }

    public function updateAssistanceAnnouncementGroupSessionNoMeeting(AnnouncementGroupSession $announcementGroupSession): void
    {
        $listParticipantes = [];
        $announcementUsers = $this->em->getRepository(AnnouncementUser::class)->findBy(['announcementGroup' => $announcementGroupSession->getAnnouncementGroup()]);

        foreach ($announcementUsers as $user) {
            $paramParticipant = new \stdClass();
            $paramParticipant->id = $user->getUser()->getId();
            $paramParticipant->email = $user->getUser()->getEmail();
            $paramParticipant->assistance = false;
            array_push($listParticipantes, $paramParticipant);
        }

        $announcementGroupSession->setStudentAssistance($listParticipantes);
        $this->em->persist($announcementGroupSession);
        $this->em->flush();
    }

    /****************** métodos para reportes/estadísticas */

    public function addResultProviders(array $parameters): array
    {
        try {
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);

            $header = $this->getConferenceSessionsclickMeeting($classroomvirtual->getRoomId())['data'];
            if ($header) {
                $reportClickmeeting = new \stdClass();
                $reportClickmeeting->header = $this->getHeaderClickmeeting($header);
                $reportClickmeeting->body = $this->getAttendeesclickMeeting(
                    $classroomvirtual->getRoomId(),
                    $header->id,
                    $reportClickmeeting->header->duration
                )['data'];
                $this->addClassroomvirtualResult($classroomvirtual, json_encode($reportClickmeeting));
            } else {
                $reportClickmeeting = false;
            }

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'reportClickmeeting' => $reportClickmeeting,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}",
            ];
        }
    }

    private function getHeaderClickmeeting($header)
    {
        $headerClickmeeting = new \stdClass();
        $headerClickmeeting->participants = $header->max_visitors;
        $headerClickmeeting->start_time = $header->start_date;
        $headerClickmeeting->end_time = $header->end_date;
        $headerClickmeeting->duration = $this->calculateDuration($header->start_date, $header->end_date);

        return $headerClickmeeting;
    }

    public function getConferenceXStatusclickMeeting(array $parameters): array
    {
        try {
            $sessionsInfo = [];
            $client = $this->getMeetingRestClient();

            $sessions = $client->conference($parameters['status']);
            foreach ($sessions as $session) {
                $sessionInfo = new \stdClass();
                $sessionInfo->id = $session->id;
                $sessionInfo->name = $session->name;
                $sessionInfo->starts_at = $session->starts_at;
                $sessionInfo->ends_at = $session->ends_at;
                $sessionInfo->timezone = $session->timezone;
                $sessionInfo->created_at = $session->created_at;
                $sessionInfo->room_url = $session->room_url;
                $sessionInfo->recorder_list = $session->recorder_list;

                array_push($sessionsInfo, $sessionInfo);
            }

            return [
                'data' => $sessionsInfo,
            ];
        } catch (\Exception $e) {
            return [
                'data' => false,
            ];
        }
    }

    public function getConferenceSessionsclickMeeting(int $roomId): array
    {
        try {
            $client = $this->getMeetingRestClient();

            $conferenceSessions = $client->conferenceSessions($roomId);

            return [
                'data' => $conferenceSessions[0],
            ];
        } catch (\Exception $e) {
            return [
                'data' => false,
            ];
        }
    }

    public function getAttendeesclickMeeting(int $roomId, int $sessionsId, int $durationMeeting): array
    {
        try {
            $meetingUsersInfo = [];
            $client = $this->getMeetingRestClient();

            $result = $client->conferenceSession($roomId, $sessionsId);
            if ($result) {
                foreach ($result->attendees as $participant) {
                    $userInfo = new \stdClass();
                    $userInfo->join_time = $participant->start_date;
                    $userInfo->leave_time = $participant->end_date;
                    $userInfo->duration = $this->calculateDuration($participant->start_date, $participant->end_date);
                    $userInfo->email = $participant->email;
                    $user = $this->userRepository->findOneBy(['email' => $participant->email]);
                    $user ?
                        $userInfo->name = $user->getFirstName() . ' ' . $user->getLastName() :
                        $userInfo->name = $participant->login;
                    $PercentMinutesAttended = $userInfo->duration / $durationMeeting;
                    $userInfo->assistance = $PercentMinutesAttended >= Classroomvirtual::MINIMUM_ATTENDANCE_TIME ? true : false;
                    array_push($meetingUsersInfo, $userInfo);
                }
            }

            return [
                'data' => $meetingUsersInfo,
            ];
        } catch (\Exception $e) {
            return [
                'data' => $e,
            ];
        }
    }

    private function calculateDuration(string $timeJoin, string $timeLeft): int
    {
        $date1 = date_create($timeJoin);
        $date2 = date_create($timeLeft);
        $diffTime = $date1->diff($date2);
        $time = explode(':', $diffTime->format('%H:%i:%s'));
        $minutes = $time[0] * 60 + $time[1];

        return $minutes;
    }

    // **generar reporte de excel */
    public function generateSessionExcelReport(array $parameters): ?array
    {
        try {
            $clickMeetingUrlReports = $this->settings->get('app.clickMeetingUrlReports');
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);

            if (!empty($classroomvirtual->getClassroomVirtualResult())) {
                $announcement = $classroomvirtual->getAnnouncementgroup()->getAnnouncement();
                $dateMeeting = date_format($classroomvirtual->getStartsat(), 'Y-m-d');
                $reportName = $clickMeetingUrlReports . 'clickMeetingMeeting_' . $announcement->getId() . '_' . $dateMeeting . '.xls';
                $complementaryData = $this->getComplementaryData($classroomvirtual);

                $reportData = json_decode($classroomvirtual->getClassroomVirtualResult()->getResult());
                $urlReport = $this->excelReportGeneratorService->generateExcelReportVirtualClass($reportData, $complementaryData, $reportName);

                return [
                    'data' => $urlReport,
                ];
            }

            return null;
        } catch (\Exception $e) {
            return [
                'data' => $e,
            ];
        }
    }

    public function generateAnnouncementExcelReport(array $parameters): ?array
    {
        try {
            $reportData = [];
            $complementaryData = [];
            $clickMeetingUrlReports = $this->settings->get('app.clickMeetingUrlReports');
            $announcementgroup = $this->em->getRepository(AnnouncementGroup::class)->find($parameters['announcementGroupId']);
            $classroomvirtuals = $this->em->getRepository(Classroomvirtual::class)->findBy(['announcementgroup' => $announcementgroup]);
            $reportName = $clickMeetingUrlReports . 'AnnouncementMeetings_' . $announcementgroup->getId() . '.xls';

            foreach ($classroomvirtuals as $classroomvirtual) {
                array_push($reportData, json_decode($classroomvirtual->getClassroomVirtualResult()->getResult()));
                array_push($complementaryData, $this->getComplementaryData($classroomvirtual));
            }

            if ($reportData) {
                $urlReport = $this->excelReportGeneratorService->generateExcelReportAnnouncement($reportData, $complementaryData, $reportName);

                return [
                    'data' => $urlReport,
                ];
            }

            return null;
        } catch (\Exception $e) {
            return [
                'data' => $e,
            ];
        }
    }

    private function getComplementaryData(Classroomvirtual $classroomvirtual)
    {
        $announcement = $classroomvirtual->getAnnouncementgroup()->getAnnouncement();
        $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(['announcement' => $announcement]);

        $complementaryData = new \stdClass();
        $complementaryData->courseName = $announcement->getCourse()->getName();
        $complementaryData->tutor = $announcementTutor->getTutor()->getFirstName() . ' ' . $announcementTutor->getTutor()->getLastName();

        return $complementaryData;
    }
}
