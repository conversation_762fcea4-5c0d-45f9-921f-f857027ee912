<?php

namespace App\Service\VirtualClass;

use App\Service\VirtualClass\ExcelReportGeneratorService;
use App\Utils\FileUtils;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\Classroomvirtual;
use App\Entity\ClassroomvirtualType;
use App\Entity\ClassroomVirtualResult;
use App\Entity\MeetingzoomToken;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request; 
use Symfony\Component\Security\Core\Security;



class ZoomMeetingService extends BaseVirtualClassService implements ClassroomVirtualInterface
{
    private $userRepository;
    private $security;
    private $logger;
    private $excelReportGeneratorService;
    private $fileUtils;

    public function __construct(
        EntityManagerInterface          $em,
        SettingsService           $settings,
        UserRepository                  $userRepository,
        Security $security,
        ExcelReportGeneratorService $excelReportGeneratorService,
        LoggerInterface $logger,
        FileUtils              $fileUtils
    ) {
        parent::__construct($em, $settings);
        $this->userRepository = $userRepository;
        $this->security = $security;
        $this->logger = $logger;
        $this->excelReportGeneratorService = $excelReportGeneratorService;
        $this->fileUtils = $fileUtils;
    }

    private function getZoomClient()
    {
        return new Client(['base_uri' => 'https://api.zoom.us']);;
    }

    private function getUser(): User
    {
        return $this->userRepository->find($this->security->getUser());
    }


    public function createRoom(array $parameters): array 
    {
        try {
            if (isset($parameters['idClassroomvirtual'])) { 
                $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);
                if ($classroomvirtual) {
                    $this->editMeetingZoom($parameters, $classroomvirtual->getRoomId());
                    $announcementGroupSession = $classroomvirtual->getGroupSession();
                    $this->updateClassroomvirtual($classroomvirtual, $parameters);
                   

                    return [
                        'status' => Response::HTTP_OK,
                        'error' => false,
                        'tutor_url' => $announcementGroupSession->getUrl(),
                    ];
                }
                else{
                    $meetingData = $this->registerUsersInTheProvider($parameters);
                    $this->saveRoomAndUsersInEasylearning($parameters, $meetingData);
                    return [
                        'status' => Response::HTTP_OK,
                        'error' => false,
                        'tutor_url' => $meetingData['start_url'],
                    ];
                }
            }

            // Si no se proporciona un ID de Classroomvirtual, crear una nueva reunión Zoom
            $meetingData = $this->registerUsersInTheProvider($parameters);
            $this->saveRoomAndUsersInEasylearning($parameters, $meetingData);
            
            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'tutor_url' => $meetingData['start_url'],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }
    }

    public function registerUsersInTheProvider(array $parametros): array
    {
        $token = $this->getOAuthToken()["token"];
        $settings = new \stdClass();
        $settings->approval_type = 0;
        $settings->close_registration = true;
        $settings->registrants_confirmation_email =  false;
        $settings->registrants_email_notification =  false;
        $settings->email_notification =  false;

        $client = $this->getZoomClient();

        $response = $client->request('POST', '/v2/users/me/meetings', [
            "headers" => [
                "Authorization" => "Bearer $token"
            ],
            'json' => [
                "agenda" =>     $parametros['name'],
                "topic" =>      $parametros['description'],
                "type" =>       2,
                "start_time" => $parametros['startsat'],
                "duration" =>   (string) $parametros['duration'],
                "timezone" =>   $parametros["timezone"],
                "settings" =>   $settings,
                "default_password" => false,
                "alternative_hosts_email_notification" => false,
            ],
        ]);

        $meetingData = json_decode($response->getBody()->getContents(), true);
        $meetingData['name'] = $parametros['name'];
        $meetingData['description'] = $parametros['description'];
        $meetingData['sessionNumber'] = $parametros['sessionNumber'];
        $meetingData['idAnnouncementGroup'] = $parametros['idAnnouncementGroup'];
        $meetingData['idAnnouncementGroupSession'] = $parametros['idAnnouncementGroupSession'];
        $meetingData['startsat'] = $parametros['startsat'];

        return $meetingData;
    }

    public function saveRoomAndUsersInEasylearning(array $parametros, array $answerApiProvider): void
    {
        $classVirtualType = $this->em->getRepository(ClassroomvirtualType::class)->find(1);

        $classroomvirtual = $this->addClassroomvirtual($answerApiProvider, $classVirtualType);
        $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($parametros['idAnnouncementGroup']);

        $this->registerUsersClassroomvirtualZoom($classroomvirtual, $announcementGroup);
    }

    private function registerUsersClassroomvirtualZoom(Classroomvirtual $classroomvirtual, AnnouncementGroup $announcementGroup): void
    {
        $user = $this->security->getUser();
        $announcementUsers = $this->em->getRepository(AnnouncementUser::class)->findBy(["announcementGroup" => $announcementGroup]);

        foreach ($announcementUsers as $user) {
            $registerUserzoom = $this->registerUserZomm($classroomvirtual->getRoomId(), $user->getUser());
            $urlZoom = $registerUserzoom["data"]["join_url"];

            $this->addClassroomvirtualUser($user, $classroomvirtual, $urlZoom);
        }
    }

    /**
     *  register participant meeting
     * 
     * @param int $userId
     * @param int $roomId
     * @param User $user
     * @return array
     */
    public function registerUserZomm(int $roomId, User $user): array
    {
        $token = $this->getOAuthToken()["token"];
        $client = $this->getZoomClient();

        try {
            $resultado = $client->request('POST', "/v2/meetings/$roomId/registrants", [
                "headers" => [
                    "Authorization" => "Bearer $token"
                ],
                'json' => [
                    "first_name" => $user->getFirstName(),
                    "last_name" => $user->getLastName(),
                    "email" => $user->getEmail(),
                    "auto_approve" => true
                ],
            ]);

            $data = json_decode($resultado->getBody()->getContents(), true);

            return  [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }
    }

    /**
     * delete a meeting from the platform
     *    
     * @param int $roomId
     * @return array
     */
    public function deleteRoom(array $parameters): array
    {
        $token = $this->getOAuthToken()["token"];
        $client = $this->getZoomClient();

        $roomId = $parameters['roomId'];

        try {
            $response = $client->request('DELETE', "/v2/meetings/$roomId", [
                "headers" => [
                    "Authorization" => "Bearer $token"
                ]
            ]);


            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);
            $this->deleteClassroomVirtual($classroomvirtual);


            if ($response->getStatusCode() == 204) {
                return [
                    'message' => 'The meeting was successfully deleted',
                ];
            }
        } catch (\Exception $e) {
            return  ["message" => "An error has occurred - Error: {$e->getMessage()}"];
        }
    }

    /**
     * update  announcementGroupSession->assistance
     * 
     * @param array $parametros
     * @return array
     */
    public function getAssistanceRoomUser(array $parameters): array
    {
        try {
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);
            $hoy =  new \DateTime();

            /*             if ($hoy <= $classroomvirtual->getStartsat())
                return [
                    'status' => Response::HTTP_OK,
                    'error' => false,
                    'data' => 'the meeting did not take place'
                ]; */

            $data = $this->getParticipantsMeetingZoom($classroomvirtual->getRoomid())["data"];
            $announcementGroupSession = $this->em->getRepository(AnnouncementGroupSession::class)->find($parameters['idAnnouncementGroupSession']);
            if ($data) {
                $realDuration = $this->getDurationMeetingZoom($classroomvirtual->getRoomId());
                $this->updateAssistanceAnnouncementGroupSession($announcementGroupSession, $data->participants, $realDuration['duration']);
                $this->updateStateClassroomvirtual($classroomvirtual, "Realized");
            } else {
                $this->updateAssistanceAnnouncementGroupSessionNoMeeting($announcementGroupSession);
                $this->updateStateClassroomvirtual($classroomvirtual, "Unrealized");
            }

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'correctly updated information',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error hasss occurred - Error: {$e->getMessage()}"
            ];
        }
    }


    /**
     * meeting information
     * 
     * @param int $roomId
     * @return array
     */
    public function getInformationMeetingZoom(int $roomId): array
    {
        $token = $this->getOAuthToken()["token"];
        $client = $this->getZoomClient();

        try {
            $resultado = $client->request('get', "/v2/meetings/$roomId", [
                'headers' => [
                    'Authorization' => "Bearer $token"
                ]
            ]);

            $data = json_decode($resultado->getBody());

            return  [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }
    }

    /**
     * meeting participant information--report
     * 
     * @param int $roomId
     * @return array
     */
    public function getParticipantsMeetingZoom(int $roomId): array
    {
        $token = $this->getOAuthToken()["token"];
        $client = $this->getZoomClient();

        try {
            $resultado = $client->request('get', "/v2/report/meetings/$roomId/participants", [
                'headers' => [
                    'Authorization' => "Bearer $token"
                ]
            ]);

            return  [
                'data' => json_decode($resultado->getBody())
            ];
        } catch (\Exception $e) {
            return [
                'data' => false
            ];
        }
    }


    /**
     * updates basic meeting information
     * 
     * @param array $parametros
     * @param int $roomId
     * @return array
     */
    public function editMeetingZoom(array $parametros, int $roomId): array
    {
        $client = $this->getZoomClient();
        $token = $this->getOAuthToken()["token"];

        try {
            $response = $client->request('PATCH', "/v2/meetings/$roomId", [
                "headers" => [
                    "Authorization" => "Bearer $token"
                ],
                'json' => [
                    "start_time" => $parametros['startsat'],
                    "timezone" =>   $parametros["timezone"],
                    "duration" =>   (string)$parametros["duration"],
                    "default_password" => false,
                    "registrants_email_notification" => false,
                    "registrants_confirmation_email" => false,
                    "alternative_hosts_email_notification" => false,
                    "email_notification" => false
                ],
            ]);

            return  [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $response->getStatusCode()
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }
    }


    public function getListsPastMeetingZoom(): array
    {
        $token = $this->getOAuthToken()["token"];
        $client = $this->getZoomClient();

        try {//*****mirar--> no está funcionando
            $resultado = $client->request('get', "/v2/users/me/meetings?from=2023-09-20,to=2023-09-22", [
                "headers" => [
                    "Authorization" => "Bearer $token"
                ]
            ]);
            $data = json_decode($resultado->getBody());

            return  [
                'duration' => $data
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }
    }



    /**
     * Get details on live or past meetings. (dashboard)
     * 
     * @param int $roomId
     * @return array
     */
    public function getDurationMeetingZoom(int $roomId): array
    {
        $token = $this->getOAuthToken()["token"];
        $client = $this->getZoomClient();

        try {
            $resultado = $client->request('get', "/v2/past_meetings/$roomId", [
                "headers" => [
                    "Authorization" => "Bearer $token"
                ]
            ]);
            $data = json_decode($resultado->getBody());

            return  [
                'duration' => $data->duration
            ];
        } catch (\Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }
    }

    public function updateAssistanceAnnouncementGroupSessionNoMeeting(AnnouncementGroupSession $announcementGroupSession)
    {
        $listParticipantes = [];
        $announcementUsers = $this->em->getRepository(AnnouncementUser::class)->findBy(["announcementGroup" => $announcementGroupSession->getAnnouncementGroup()]);

        foreach ($announcementUsers as $user) {
            $paramParticipant = new \stdClass();
            $paramParticipant->id =  $user->getUser()->getId();
            $paramParticipant->email =  $user->getUser()->getEmail();
            $paramParticipant->assistance = false;
            array_push($listParticipantes, $paramParticipant);
        }

        $announcementGroupSession->setStudentAssistance($listParticipantes);
        $this->em->persist($announcementGroupSession);
        $this->em->flush();
    }

    public function updateAssistanceAnnouncementGroupSession(
        AnnouncementGroupSession $announcementGroupSession,
        array $attendees,
        int $duration
    ) {
        $listParticipants = [];
        $participantsEmail = [];
        $attendeesEmail = $this->getAttendeesEmail($attendees);
        $announcementUsers = $this->em->getRepository(AnnouncementUser::class)->findBy(["announcementGroup" => $announcementGroupSession->getAnnouncementGroup()]);

        foreach ($announcementUsers as $announcementUser) {
            $paramParticipant = $this->getParamParticipant($announcementUser, $attendees, $attendeesEmail, $duration);
            array_push($participantsEmail, $paramParticipant->email);
            array_push($listParticipants, $paramParticipant);
        }

        $attendeesMissingEmail = array_diff($attendeesEmail, $participantsEmail);
        foreach ($attendeesMissingEmail as $missingEmail) {
            $paramParticipant = $this->getParamParticipantMissing($missingEmail, $attendees, $attendeesEmail, $duration);
            array_push($listParticipants, $paramParticipant);
        }

        $announcementGroupSession->setStudentAssistance($listParticipants);
        $this->em->persist($announcementGroupSession);
        $this->em->flush();

        return $listParticipants;
    }

    private function getParamParticipant(
        AnnouncementUser $announcementUser,
        array $attendees,
        array $attendeesEmail,
        int $duration
    ) {
        $paramParticipant = new \stdClass();

        if (in_array($announcementUser->getUser()->getEmail(), $attendeesEmail)) {
            $assistance =  $attendees[array_search($announcementUser->getUser()->getEmail(), $attendeesEmail)];
            $PercentMinutesAttended = ($assistance->duration / 60) / $duration;
            $paramParticipant->assistance = $PercentMinutesAttended >= Classroomvirtual::MINIMUM_ATTENDANCE_TIME ? true : false;
        } else
            $paramParticipant->assistance = false;

        $paramParticipant->id =  $announcementUser->getUser()->getId();
        $paramParticipant->email =  $announcementUser->getUser()->getEmail();

        return  $paramParticipant;
    }

    private function getParamParticipantMissing(string $email, array $attendees, array $attendeesEmail, int $duration)
    {
        $paramParticipant = new \stdClass();

        $assistant =  $attendees[array_search($email, $attendeesEmail)];
        $paramParticipant->id =  $assistant->user_id;
        $paramParticipant->email =  $assistant->user_email;

        $PercentMinutesAttended = ($assistant->duration / 60) / $duration;
        $paramParticipant->assistance = $PercentMinutesAttended >= Classroomvirtual::MINIMUM_ATTENDANCE_TIME ? true : false;

        return  $paramParticipant;
    }


    /**
     * generates authorization token for the zoom platform
     * 
     * @param int $userId
     * @return array
     * @throws GuzzleException
     */
    public function getOAuthToken(): array
    {
        try {
            $user  = $this->getUser();
            $userId = $user->getId();

            $fecha_inicial =  new \DateTime();
            $client = $this->getZoomClient();
            $intervalo = 0;
            $generarToken = false;

            $meetingzoomToken = $this->em->getRepository(MeetingzoomToken::class)->findOneBy(["user_id" => $userId, "estado" => "activo"]);

            if (!$meetingzoomToken) {

                $generarToken = true;
            }

            if ($meetingzoomToken) {
                $intervalo = $fecha_inicial->diff($meetingzoomToken->getCreatedAt());
                $tiempo = explode(":", $intervalo->format('%H:%i:%s'));
                $minutos = $tiempo[0] * 60 + $tiempo[1];
                //   dd("no hay token" .   $minutos);
                if ($minutos > 55) {
                    $generarToken = true;
                    //  dd("no hay token" . $minutos);
                    $this->updateStateToken($meetingzoomToken, "inactivo");
                } else {
                    $accessToken = $meetingzoomToken->getToken();
                }
            } else {
                $generarToken = true;
            }

            if ($generarToken) {
                $response = $client->request(
                    'POST',
                    '/oauth/token',
                    [
                        'query' => ['grant_type' => 'account_credentials', 'account_id' => $this->settings->get('app.zoomAccountId')],
                        'Host' => 'zoom.us',
                        'headers' => [
                            'Authorization' => 'Basic ' .
                                Base64_Encode(
                                    $this->settings->get('app.zoomClientId') . ':' . $this->settings->get('app.zoomClientSecret')
                                )
                        ]
                    ]
                );

                $accessToken = json_decode($response->getBody()->getContents(), true)["access_token"];
                $this->addMeetingzoomToken($accessToken, $fecha_inicial, $userId);
            }

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'token' => $accessToken,
            ];
        } catch (Exception $e) {
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }
    }

    public function addMeetingzoomToken($accessToken, $fecha_inicial, $userId)
    {
        $meetingzoomToken = new MeetingzoomToken();
        $meetingzoomToken->setToken($accessToken);
        $meetingzoomToken->setCreatedAt($fecha_inicial);
        $meetingzoomToken->setUserId($userId);
        $meetingzoomToken->setEstado("activo");
        $this->em->persist($meetingzoomToken);
        $this->em->flush();
    }

    public function  updateStateToken(MeetingzoomToken $meetingzoomToken, string $estado): void
    {
        $meetingzoomToken->setEstado($estado);
        $this->em->persist($meetingzoomToken);
        $this->em->flush();
    }

    private function getAttendeesEmail(array $attendees): array
    {
        $attendeesEmail = [];
        foreach ($attendees as $key => $attendee)
            $attendeesEmail[$key] = $attendee->user_email;

        return $attendeesEmail;
    }

    /****************** métodos para reportes/estadísticas */

    public function addResultProviders(array $parameters): array
    {
        try {            
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']);  
            
           $header = $this->getDetailMeetingZoomReport($classroomvirtual->getRoomId())["data"];
            if($header){   
                $reportZoom = new \stdClass();       
                $reportZoom->header = $this->getHeaderZoom($header); 
                $reportZoom->body = $this->getAssistanceMeetingZoomReport($classroomvirtual->getRoomId(), $header->duration)["data"];
                $this->addClassroomvirtualResult($classroomvirtual, json_encode($reportZoom));
            }else $reportZoom = false;

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'reportZoom' => $reportZoom,
            ];
        } catch (\Exception $e) { 
            return [
                'status' => 500,
                'error' => true,
                'data' => "An error has occurred - Error: {$e->getMessage()}"
            ];
        }
        
    }

    private function getHeaderZoom($header){  
        $headerZoom = new \stdClass();
        $headerZoom->participants = $header->participants_count;
        $headerZoom->start_time = $header->start_time;
        $headerZoom->end_time = $header->end_time;
        $headerZoom->duration = $header->duration;

        return $headerZoom;
    }

    public function  getAssistanceMeetingZoomReport(int $roomId, int $durationMeeting):array
    {
        try {
            $meetingUsersInfo = [];
            
            $result = $this->getParticipantsMeetingZoomPast($roomId)["data"];
            if($result){
                foreach($result->participants as $participant){
                    $userInfo = new \stdClass();
                    $userInfo->join_time = $participant->join_time;
                    $userInfo->leave_time = $participant->leave_time;
                    $userInfo->duration = $this->calculateDuration($participant->join_time, $participant->leave_time);
                    $userInfo->email = $participant->user_email;
                    $user = $this->userRepository->findOneBy(["email" => $participant->user_email]);
                    $user ?
                        $userInfo->name = $user->getFirstName().' '. $user->getLastName() :
                        $userInfo->name = $participant->name;
                    $PercentMinutesAttended = $userInfo->duration/$durationMeeting;
                    $userInfo->assistance = $PercentMinutesAttended >= Classroomvirtual::MINIMUM_ATTENDANCE_TIME ? true : false;
                    array_push($meetingUsersInfo, $userInfo);
                }
            }

            return [
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $meetingUsersInfo,
            ];
        } catch (\Exception $e) {
            return [
                'data' => $e
            ];
        }
    }

    private function calculateDuration(string $timeJoin, string $timeLeft):int
    {
        $date1=date_create($timeJoin);
        $date2=date_create($timeLeft);
        $diffTime = $date1->diff($date2);
        $time = explode(":", $diffTime->format('%H:%i:%s'));
        $minutes = $time[0] * 60 + $time[1];

        return $minutes;
    }

    public function  getDetailMeetingZoomReport(int $roomId):array
    {
        $token = $this->getOAuthToken()["token"];
        $client = $this->getZoomClient();

        try {
            $resultado = $client->request('get', "/v2/past_meetings/$roomId", [
                "headers" => [
                    "Authorization" => "Bearer $token"
                ]
            ]);
            $data = json_decode($resultado->getBody());

            return  [
                'data' => $data
            ];
        } catch (\Exception $e) {
            return [
                'data' => false
            ];
        }
    }    

    public function getParticipantsMeetingZoomPast(int $roomId): array
    {
        $token = $this->getOAuthToken()["token"];
        $client = $this->getZoomClient();

        try {
            $resultado = $client->request('get', "/v2/past_meetings/$roomId/participants", [
                'headers' => [
                    'Authorization' => "Bearer $token"
                ]
            ]);

            return  [
                'data' => json_decode($resultado->getBody())
            ];
        } catch (\Exception $e) {
            return [
                'data' => $e
            ];
        }
    }

    //**generar reporte de excel */
    public function generateSessionExcelReport(array $parameters): ?array
    {
        try {        
            $zoomUrlReports = $this->settings->get('app.zoomUrlReports');
            $classroomvirtual = $this->em->getRepository(Classroomvirtual::class)->find($parameters['idClassroomvirtual']); 

            $dateMeeting = date_format($classroomvirtual->getStartsat(), "Y-m-d"); 
            if (!empty($classroomvirtual->getClassroomVirtualResult())) {          
                $announcement = $classroomvirtual->getAnnouncementgroup()->getAnnouncement(); 
                $dateMeeting = date_format($classroomvirtual->getStartsat(), "Y-m-d");   
                $reportName = $zoomUrlReports.'zoomMeeting_'.$announcement->getId().'_'.$dateMeeting.'.xls';
                $complementaryData = $this->getComplementaryData($classroomvirtual);

             
                $reportData = json_decode($classroomvirtual->getClassroomVirtualResult()->getResult());
                $urlReport = $this->excelReportGeneratorService->generateExcelReportVirtualClass($reportData, $complementaryData, $reportName);

                return [
                    'data' => $urlReport
                ];
            }

            return null;
        } catch (\Exception $e) {
            return [
                'data' => $e
            ];
        }
    }

    public function generateAnnouncementExcelReport(array $parameters): ?array
    { 
        try {     
            $reportData = []; 
            $complementaryData = [];
            $zoomUrlReports = $this->settings->get('app.zoomUrlReports');
            $announcementgroup = $this->em->getRepository(AnnouncementGroup::class)->find($parameters['announcementGroupId']);
            $classroomvirtuals = $this->em->getRepository(Classroomvirtual::class)->findBy(["announcementgroup" => $announcementgroup]);            
            $reportName = $zoomUrlReports.'AnnouncementMeetings_'.$announcementgroup->getId().'.xls';

            foreach($classroomvirtuals as $classroomvirtual){
                array_push($reportData,json_decode($classroomvirtual->getClassroomVirtualResult()->getResult()));
                array_push($complementaryData, $this->getComplementaryData($classroomvirtual));
            }

            if ($reportData) {
                $urlReport = $this->excelReportGeneratorService->generateExcelReportAnnouncement($reportData, $complementaryData, $reportName);

                return [
                    'data' => $urlReport
                ];
            }

            return null;
        } catch (\Exception $e) {
            return [
                'data' => $e
            ];
        }
    }

    private function getComplementaryData(Classroomvirtual $classroomvirtual)
    {
        $announcement = $classroomvirtual->getAnnouncementgroup()->getAnnouncement(); 
        $announcementTutor = $this->em->getRepository(AnnouncementTutor::class)->findOneBy(["announcement" => $announcement]);          
        
        $complementaryData = new \stdClass();
        $complementaryData->courseName = $announcement->getCourse()->getName();
        $complementaryData->tutor = $announcementTutor->getTutor()->getFirstName().' '.$announcementTutor->getTutor()->getLastName();

        return $complementaryData;
    }
}
