<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241107181222 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE adivina_imagen (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, image VARCHAR(255) NOT NULL, words VARCHAR(255) NOT NULL, clue VARCHAR(255) DEFAULT NULL, time INT NOT NULL, title VARCHAR(255) NOT NULL, INDEX IDX_EF1B569E579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE alert_type_tutor (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_1604A96FB03A8386 (created_by_id), INDEX IDX_1604A96F896DBBDE (updated_by_id), INDEX IDX_1604A96FC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE alert_type_tutor_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_EC131BA42C2AC5D3 (translatable_id), UNIQUE INDEX alert_type_tutor_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement (id INT AUTO_INCREMENT NOT NULL, course_id INT NOT NULL, subsidizer_id INT DEFAULT NULL, type_diploma_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, code VARCHAR(100) DEFAULT NULL, start_at DATETIME NOT NULL, finish_at DATETIME NOT NULL, total_hours INT DEFAULT NULL, users_per_group INT NOT NULL, objective_and_contents LONGTEXT DEFAULT NULL, subsidized TINYINT(1) NOT NULL, action_type VARCHAR(100) DEFAULT NULL, action_code VARCHAR(100) DEFAULT NULL, denomination VARCHAR(255) DEFAULT NULL, contact_person VARCHAR(255) DEFAULT NULL, contact_person_email VARCHAR(255) DEFAULT NULL, contact_person_telephone VARCHAR(20) DEFAULT NULL, notified_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', max_users INT DEFAULT NULL, formative_action_type VARCHAR(255) DEFAULT NULL, format VARCHAR(255) DEFAULT NULL, place VARCHAR(255) DEFAULT NULL, training_center VARCHAR(255) DEFAULT NULL, training_center_address VARCHAR(255) DEFAULT NULL, training_center_nif VARCHAR(255) DEFAULT NULL, training_center_teacher VARCHAR(255) DEFAULT NULL, training_center_teacher_dni VARCHAR(255) DEFAULT NULL, training_center_phone VARCHAR(255) DEFAULT NULL, training_center_email VARCHAR(255) DEFAULT NULL, subsidizer_entity VARCHAR(255) DEFAULT NULL, general_information LONGTEXT DEFAULT NULL, is_confirmation_required_diploma TINYINT(1) DEFAULT NULL, timezone VARCHAR(150) DEFAULT NULL, status VARCHAR(40) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_4DB9D91C591CC992 (course_id), INDEX IDX_4DB9D91CB8778369 (subsidizer_id), INDEX IDX_4DB9D91CB4991890 (type_diploma_id), INDEX IDX_4DB9D91CB03A8386 (created_by_id), INDEX IDX_4DB9D91C896DBBDE (updated_by_id), INDEX IDX_4DB9D91CC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_alert_tutor (id INT AUTO_INCREMENT NOT NULL, alert_type_tutor_id INT DEFAULT NULL, announcement_tutor_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_DDE31BD5D628A82 (alert_type_tutor_id), INDEX IDX_DDE31BD5BC2EE02 (announcement_tutor_id), INDEX IDX_DDE31BDB03A8386 (created_by_id), INDEX IDX_DDE31BD896DBBDE (updated_by_id), INDEX IDX_DDE31BDC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_aproved_criteria (id INT AUTO_INCREMENT NOT NULL, announcement_id INT DEFAULT NULL, announcement_criteria_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, value NUMERIC(10, 0) NOT NULL, extra LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_7324EB3E913AEA17 (announcement_id), INDEX IDX_7324EB3EE7886A0B (announcement_criteria_id), INDEX IDX_7324EB3EB03A8386 (created_by_id), INDEX IDX_7324EB3E896DBBDE (updated_by_id), INDEX IDX_7324EB3EC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_configuration (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, configuration_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_9C1B67AB913AEA17 (announcement_id), INDEX IDX_9C1B67AB73F32DD8 (configuration_id), INDEX IDX_9C1B67ABB03A8386 (created_by_id), INDEX IDX_9C1B67AB896DBBDE (updated_by_id), INDEX IDX_9C1B67ABC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_configuration_type (id INT AUTO_INCREMENT NOT NULL, configuration_client_announcement_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, code VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_BD7ACF885E9A4305 (configuration_client_announcement_id), INDEX IDX_BD7ACF88B03A8386 (created_by_id), INDEX IDX_BD7ACF88896DBBDE (updated_by_id), INDEX IDX_BD7ACF88C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_configuration_type_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_E60E04A22C2AC5D3 (translatable_id), UNIQUE INDEX announcement_configuration_type_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_criteria (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, extra JSON DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_CD521172B03A8386 (created_by_id), INDEX IDX_CD521172896DBBDE (updated_by_id), INDEX IDX_CD521172C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_criteria_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_B9B4419B2C2AC5D3 (translatable_id), UNIQUE INDEX announcement_criteria_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_didatic_guide (id INT AUTO_INCREMENT NOT NULL, announcement_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) DEFAULT NULL, file_size INT DEFAULT NULL, mime_type VARCHAR(60) DEFAULT NULL, UNIQUE INDEX UNIQ_BAC51D60913AEA17 (announcement_id), INDEX IDX_BAC51D60B03A8386 (created_by_id), INDEX IDX_BAC51D60896DBBDE (updated_by_id), INDEX IDX_BAC51D60C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_group (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, sessions_announcement_id INT DEFAULT NULL, type_money_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, company_profile VARCHAR(255) DEFAULT NULL, code VARCHAR(100) DEFAULT NULL, company_cif VARCHAR(255) DEFAULT NULL, denomination VARCHAR(255) DEFAULT NULL, file_number VARCHAR(100) DEFAULT NULL, num_sessions INT DEFAULT NULL, place LONGTEXT DEFAULT NULL, group_number INT DEFAULT NULL, cost NUMERIC(10, 2) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_F316812913AEA17 (announcement_id), UNIQUE INDEX UNIQ_F316812770AF0AD (sessions_announcement_id), INDEX IDX_F31681272D614D0 (type_money_id), INDEX IDX_F316812B03A8386 (created_by_id), INDEX IDX_F316812896DBBDE (updated_by_id), INDEX IDX_F316812C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_group_session (id INT AUTO_INCREMENT NOT NULL, announcement_group_id INT NOT NULL, type_money_id INT DEFAULT NULL, modality_id INT DEFAULT NULL, start_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', finish_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', url LONGTEXT DEFAULT NULL, assistance JSON DEFAULT NULL, session_number INT DEFAULT NULL, student_assistance JSON DEFAULT NULL, entry_margin INT DEFAULT NULL, exit_margin INT DEFAULT NULL, timezone VARCHAR(150) DEFAULT NULL, place VARCHAR(255) DEFAULT NULL, cost NUMERIC(10, 2) DEFAULT NULL, type VARCHAR(100) DEFAULT NULL, INDEX IDX_6FBD43A4851953B4 (announcement_group_id), INDEX IDX_6FBD43A472D614D0 (type_money_id), INDEX IDX_6FBD43A42D6D889B (modality_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_group_session_assistance_files (id INT AUTO_INCREMENT NOT NULL, announcement_group_session_id INT NOT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) DEFAULT NULL, file_size INT DEFAULT NULL, mime_type VARCHAR(60) DEFAULT NULL, INDEX IDX_9362DE17D6B4E314 (announcement_group_session_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_inspector_access (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, token_id INT NOT NULL, user VARCHAR(45) NOT NULL, password VARCHAR(255) NOT NULL, roles JSON NOT NULL, UNIQUE INDEX UNIQ_628C140B913AEA17 (announcement_id), UNIQUE INDEX UNIQ_628C140B41DEE7B9 (token_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_modality (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) DEFAULT NULL, is_active TINYINT(1) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_modality_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_ECD0153C2C2AC5D3 (translatable_id), UNIQUE INDEX announcement_modality_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_notification (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, text LONGTEXT NOT NULL, state INT NOT NULL, is_active TINYINT(1) NOT NULL, send_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_4525C829913AEA17 (announcement_id), INDEX IDX_4525C829B03A8386 (created_by_id), INDEX IDX_4525C829896DBBDE (updated_by_id), INDEX IDX_4525C829C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_notification_group (id INT AUTO_INCREMENT NOT NULL, announcement_notification_id INT DEFAULT NULL, announcement_group_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, num_group INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_FF834F9A19FA311B (announcement_notification_id), INDEX IDX_FF834F9A851953B4 (announcement_group_id), INDEX IDX_FF834F9AB03A8386 (created_by_id), INDEX IDX_FF834F9A896DBBDE (updated_by_id), INDEX IDX_FF834F9AC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_observation (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, course_status VARCHAR(255) DEFAULT NULL, comunicado_fundae TINYINT(1) NOT NULL, comunicado_abilitia TINYINT(1) NOT NULL, provider_cost DOUBLE PRECISION NOT NULL, hedima_management_cost DOUBLE PRECISION NOT NULL, travel_and_maintenance_cost DOUBLE PRECISION NOT NULL, total_cost DOUBLE PRECISION NOT NULL, economic_module VARCHAR(255) DEFAULT NULL, final_pax DOUBLE PRECISION NOT NULL, maximum_bonus DOUBLE PRECISION NOT NULL, subsidized_amount DOUBLE PRECISION NOT NULL, private_amount DOUBLE PRECISION NOT NULL, provider_invoice_number VARCHAR(100) DEFAULT NULL, hedima_management_invoice_number VARCHAR(100) DEFAULT NULL, travel_and_maintenance VARCHAR(255) DEFAULT NULL, invoice_status VARCHAR(255) DEFAULT NULL, observations LONGTEXT DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_BBF55BFE913AEA17 (announcement_id), INDEX IDX_BBF55BFEB03A8386 (created_by_id), INDEX IDX_BBF55BFE896DBBDE (updated_by_id), INDEX IDX_BBF55BFEC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_observation_document (id INT AUTO_INCREMENT NOT NULL, announcement_observation_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, type VARCHAR(20) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) DEFAULT NULL, file_size INT DEFAULT NULL, mime_type VARCHAR(60) DEFAULT NULL, INDEX IDX_E69ACE464D83195D (announcement_observation_id), INDEX IDX_E69ACE46B03A8386 (created_by_id), INDEX IDX_E69ACE46896DBBDE (updated_by_id), INDEX IDX_E69ACE46C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_step_creation (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) DEFAULT NULL, position INT DEFAULT NULL, extra JSON DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_temporalization (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, started_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', finished_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', minimum_time INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_CEC55850579F4768 (chapter_id), INDEX IDX_CEC55850913AEA17 (announcement_id), INDEX IDX_CEC55850B03A8386 (created_by_id), INDEX IDX_CEC55850896DBBDE (updated_by_id), INDEX IDX_CEC55850C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_tutor (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, tutor_id INT NOT NULL, announcement_group_id INT DEFAULT NULL, cv_files_manager_id INT DEFAULT NULL, user_company_id INT DEFAULT NULL, dni VARCHAR(100) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, telephone VARCHAR(20) DEFAULT NULL, tutoring_time LONGTEXT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, filename VARCHAR(255) DEFAULT NULL, original_name VARCHAR(255) DEFAULT NULL, file_size INT DEFAULT NULL, mime_type VARCHAR(60) DEFAULT NULL, INDEX IDX_FBF66A9F913AEA17 (announcement_id), INDEX IDX_FBF66A9F208F64F1 (tutor_id), UNIQUE INDEX UNIQ_FBF66A9F851953B4 (announcement_group_id), INDEX IDX_FBF66A9F911DD53E (cv_files_manager_id), INDEX IDX_FBF66A9F30FCDC3A (user_company_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_tutor_connection (id INT AUTO_INCREMENT NOT NULL, announcement_tutor_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, ip VARCHAR(100) NOT NULL, time INT NOT NULL, extra LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_817BA0395BC2EE02 (announcement_tutor_id), INDEX IDX_817BA039B03A8386 (created_by_id), INDEX IDX_817BA039896DBBDE (updated_by_id), INDEX IDX_817BA039C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_user (id INT AUTO_INCREMENT NOT NULL, announcement_id INT NOT NULL, user_id INT NOT NULL, announcement_group_id INT DEFAULT NULL, user_company_id INT DEFAULT NULL, user_professional_category_id INT DEFAULT NULL, user_work_center_id INT DEFAULT NULL, user_work_department_id INT DEFAULT NULL, user_study_level_id INT DEFAULT NULL, notified DATETIME DEFAULT NULL, is_aproved TINYINT(1) DEFAULT NULL, date_approved DATETIME DEFAULT NULL, is_download_diploma TINYINT(1) DEFAULT NULL, is_read_didactic_guide TINYINT(1) DEFAULT NULL, date_read_didactic_guide DATETIME DEFAULT NULL, is_download_didactic_guide TINYINT(1) DEFAULT NULL, date_download_didactic_guide DATETIME DEFAULT NULL, valued_course_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', is_confirmation_assistance TINYINT(1) DEFAULT NULL, date_confirmation_assistance DATETIME DEFAULT NULL, external TINYINT(1) NOT NULL, INDEX IDX_A1A2DE15913AEA17 (announcement_id), INDEX IDX_A1A2DE15A76ED395 (user_id), INDEX IDX_A1A2DE15851953B4 (announcement_group_id), INDEX IDX_A1A2DE1530FCDC3A (user_company_id), INDEX IDX_A1A2DE15BC194B79 (user_professional_category_id), INDEX IDX_A1A2DE1550BF847B (user_work_center_id), INDEX IDX_A1A2DE1542196A10 (user_work_department_id), INDEX IDX_A1A2DE1597BD8177 (user_study_level_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE announcement_user_digital_signature (id INT AUTO_INCREMENT NOT NULL, announcement_user_id INT DEFAULT NULL, announcement_group_session_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, INDEX IDX_41FE20DA5BCF8E5F (announcement_user_id), INDEX IDX_41FE20DAD6B4E314 (announcement_group_session_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE answer (id INT AUTO_INCREMENT NOT NULL, question_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, answer LONGTEXT NOT NULL, correct TINYINT(1) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_DADD4A251E27F6BF (question_id), INDEX IDX_DADD4A25B03A8386 (created_by_id), INDEX IDX_DADD4A25896DBBDE (updated_by_id), INDEX IDX_DADD4A25C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE answers_video_quiz (id INT AUTO_INCREMENT NOT NULL, question_id INT DEFAULT NULL, answer VARCHAR(255) NOT NULL, is_correct TINYINT(1) NOT NULL, INDEX IDX_1CFB516C1E27F6BF (question_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE api_key_request (id INT AUTO_INCREMENT NOT NULL, api_key_user_id INT NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', ip VARCHAR(255) NOT NULL, endpoint VARCHAR(255) NOT NULL, INDEX IDX_3336212FAFCF7D25 (api_key_user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE api_key_user (id INT AUTO_INCREMENT NOT NULL, api_key VARCHAR(255) NOT NULL, username VARCHAR(255) NOT NULL, active TINYINT(1) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE bots (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, last_name VARCHAR(255) NOT NULL, route VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE catalog (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, component VARCHAR(255) DEFAULT NULL, route VARCHAR(255) DEFAULT NULL, relation VARCHAR(255) NOT NULL, service VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE catalog_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_A5DD64C12C2AC5D3 (translatable_id), UNIQUE INDEX catalog_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE categorize (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, question VARCHAR(255) DEFAULT NULL, time INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, INDEX IDX_37DFDA7579F4768 (chapter_id), INDEX IDX_37DFDA7B03A8386 (created_by_id), INDEX IDX_37DFDA7896DBBDE (updated_by_id), INDEX IDX_37DFDA7C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE categorize_answers (id INT AUTO_INCREMENT NOT NULL, categorize_id INT DEFAULT NULL, options_id INT DEFAULT NULL, correct TINYINT(1) NOT NULL, INDEX IDX_71A5F63835D01CA7 (categorize_id), INDEX IDX_71A5F6383ADB05F1 (options_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE categorize_options (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, INDEX IDX_F140CAB9579F4768 (chapter_id), INDEX IDX_F140CAB9B03A8386 (created_by_id), INDEX IDX_F140CAB9896DBBDE (updated_by_id), INDEX IDX_F140CAB9C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE center (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge (id INT AUTO_INCREMENT NOT NULL, announcement_id INT DEFAULT NULL, translation_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, title VARCHAR(255) NOT NULL, start_date DATETIME NOT NULL, end_date DATETIME NOT NULL, image VARCHAR(255) DEFAULT NULL, locale VARCHAR(10) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_D7098951913AEA17 (announcement_id), INDEX IDX_D70989519CAA2B25 (translation_id), INDEX IDX_D7098951B03A8386 (created_by_id), INDEX IDX_D7098951896DBBDE (updated_by_id), INDEX IDX_D7098951C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge_answers (id INT AUTO_INCREMENT NOT NULL, pregunta_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, text VARCHAR(255) NOT NULL, correct TINYINT(1) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_BC40898331A5801E (pregunta_id), INDEX IDX_BC408983B03A8386 (created_by_id), INDEX IDX_BC408983896DBBDE (updated_by_id), INDEX IDX_BC408983C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge_answers_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, text VARCHAR(255) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_C1CB75B22C2AC5D3 (translatable_id), UNIQUE INDEX challenge_answers_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge_duel (id INT AUTO_INCREMENT NOT NULL, challenge_id INT DEFAULT NULL, user1_id INT DEFAULT NULL, user2_id INT DEFAULT NULL, assigned_bot_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, winner INT DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_921BA0E498A21AC6 (challenge_id), INDEX IDX_921BA0E456AE248B (user1_id), INDEX IDX_921BA0E4441B8B65 (user2_id), INDEX IDX_921BA0E4AD76C09 (assigned_bot_id), INDEX IDX_921BA0E4B03A8386 (created_by_id), INDEX IDX_921BA0E4896DBBDE (updated_by_id), INDEX IDX_921BA0E4C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge_duel_questions (id INT AUTO_INCREMENT NOT NULL, duel_id INT NOT NULL, question_id INT DEFAULT NULL, answer_user1_id INT DEFAULT NULL, answer_user2_id INT DEFAULT NULL, time_user1 INT DEFAULT NULL, time_user2 INT DEFAULT NULL, INDEX IDX_446B904758875E (duel_id), INDEX IDX_446B90471E27F6BF (question_id), INDEX IDX_446B904771E425EE (answer_user1_id), INDEX IDX_446B904763518A00 (answer_user2_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge_duel_questions_adn (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, challenge_id INT NOT NULL, adn JSON NOT NULL, INDEX IDX_5CCC6C92A76ED395 (user_id), INDEX IDX_5CCC6C9298A21AC6 (challenge_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge_questions (id INT AUTO_INCREMENT NOT NULL, desafio_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, random TINYINT(1) NOT NULL, text VARCHAR(500) NOT NULL, image VARCHAR(255) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_2A4B884A92A6DBB9 (desafio_id), INDEX IDX_2A4B884AB03A8386 (created_by_id), INDEX IDX_2A4B884A896DBBDE (updated_by_id), INDEX IDX_2A4B884AC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge_questions_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, text VARCHAR(500) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_8C2788B02C2AC5D3 (translatable_id), UNIQUE INDEX challenge_questions_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge_user (id INT AUTO_INCREMENT NOT NULL, challenge_id INT NOT NULL, user_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, notified TINYINT(1) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_843CD1CF98A21AC6 (challenge_id), INDEX IDX_843CD1CFA76ED395 (user_id), INDEX IDX_843CD1CFB03A8386 (created_by_id), INDEX IDX_843CD1CF896DBBDE (updated_by_id), INDEX IDX_843CD1CFC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE challenge_user_points (id INT AUTO_INCREMENT NOT NULL, challenge_id INT NOT NULL, user_id INT DEFAULT NULL, points INT NOT NULL, INDEX IDX_3B543DA998A21AC6 (challenge_id), INDEX IDX_3B543DA9A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE chapter (id INT AUTO_INCREMENT NOT NULL, course_id INT NOT NULL, type_id INT NOT NULL, season_id INT DEFAULT NULL, vcms_project_id INT DEFAULT NULL, roleplay_project_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, title VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, position INT NOT NULL, max_question INT DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, INDEX IDX_F981B52E591CC992 (course_id), INDEX IDX_F981B52EC54C8C93 (type_id), INDEX IDX_F981B52E4EC001D1 (season_id), UNIQUE INDEX UNIQ_F981B52E7631067 (vcms_project_id), UNIQUE INDEX UNIQ_F981B52EB6704F40 (roleplay_project_id), INDEX IDX_F981B52EB03A8386 (created_by_id), INDEX IDX_F981B52E896DBBDE (updated_by_id), INDEX IDX_F981B52EC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE chapter_type (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, type VARCHAR(60) NOT NULL, active TINYINT(1) NOT NULL, video VARCHAR(255) DEFAULT NULL, video_en VARCHAR(255) DEFAULT NULL, normalized VARCHAR(255) DEFAULT NULL, percentage_completed NUMERIC(10, 2) DEFAULT NULL, description LONGTEXT DEFAULT NULL, code VARCHAR(50) DEFAULT NULL, icon VARCHAR(100) DEFAULT NULL, playerurl VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE chapter_type_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_DEBCD0932C2AC5D3 (translatable_id), UNIQUE INDEX chapter_type_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE chat_channel (id INT AUTO_INCREMENT NOT NULL, server_id INT NOT NULL, parent_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, type VARCHAR(45) DEFAULT NULL, name VARCHAR(100) NOT NULL, available_from DATETIME DEFAULT NULL, available_until DATETIME DEFAULT NULL, entity_id VARCHAR(36) DEFAULT NULL, entity_type VARCHAR(100) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_EEF7422E1844E6B7 (server_id), INDEX IDX_EEF7422E727ACA70 (parent_id), INDEX IDX_EEF7422EB03A8386 (created_by_id), INDEX IDX_EEF7422E896DBBDE (updated_by_id), INDEX IDX_EEF7422EC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE chat_channel_user (id INT AUTO_INCREMENT NOT NULL, channel_id INT NOT NULL, user_id INT NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_EF2C1CB672F5A1AA (channel_id), INDEX IDX_EF2C1CB6A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE chat_message (id INT AUTO_INCREMENT NOT NULL, channel_id INT NOT NULL, user_id INT NOT NULL, reply_to_id INT DEFAULT NULL, message LONGTEXT NOT NULL, seen TINYINT(1) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_FAB3FC1672F5A1AA (channel_id), INDEX IDX_FAB3FC16A76ED395 (user_id), INDEX IDX_FAB3FC16FFDF7169 (reply_to_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE chat_message_like (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, chat_message_id INT NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', active TINYINT(1) NOT NULL, INDEX IDX_FB1DB2EBA76ED395 (user_id), INDEX IDX_FB1DB2EB948B568F (chat_message_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE chat_message_report (id INT AUTO_INCREMENT NOT NULL, message_id INT NOT NULL, user_id INT NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', active TINYINT(1) NOT NULL, INDEX IDX_E5FE6496537A1329 (message_id), INDEX IDX_E5FE6496A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE chat_server (id INT AUTO_INCREMENT NOT NULL, type VARCHAR(45) NOT NULL, entity_id VARCHAR(40) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE classroom_virtual_result (id INT AUTO_INCREMENT NOT NULL, classroom_virtual_id INT DEFAULT NULL, result LONGTEXT NOT NULL, created_at DATETIME NOT NULL, update_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_B6A56DE127076DC2 (classroom_virtual_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE classroomvirtual (id INT AUTO_INCREMENT NOT NULL, announcementgroup_id INT DEFAULT NULL, classroomvirtual_type_id INT DEFAULT NULL, group_session_id INT DEFAULT NULL, roomid BIGINT NOT NULL, roomtype INT NOT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, startsat DATETIME NOT NULL, duration INT NOT NULL, session_number INT DEFAULT NULL, state VARCHAR(50) NOT NULL, INDEX IDX_5D831E2F76BD65F0 (announcementgroup_id), INDEX IDX_5D831E2FFE14978B (classroomvirtual_type_id), UNIQUE INDEX UNIQ_5D831E2FB6F28D6D (group_session_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE classroomvirtual_type (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, state TINYINT(1) NOT NULL, extra JSON DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE classroomvirtual_user (id INT AUTO_INCREMENT NOT NULL, announcementuser_id INT DEFAULT NULL, announcementtutor_id INT DEFAULT NULL, classroomvirtual_id INT DEFAULT NULL, classroomvirtualuser_url LONGTEXT DEFAULT NULL, INDEX IDX_CF87B8EB4688824D (announcementuser_id), INDEX IDX_CF87B8EBA866D846 (announcementtutor_id), INDEX IDX_CF87B8EBF5A029F5 (classroomvirtual_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE comment_task (id INT AUTO_INCREMENT NOT NULL, history_delivery_task_id INT DEFAULT NULL, parent_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, comment LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_744879C95445F77F (history_delivery_task_id), INDEX IDX_744879C9727ACA70 (parent_id), INDEX IDX_744879C9B03A8386 (created_by_id), INDEX IDX_744879C9896DBBDE (updated_by_id), INDEX IDX_744879C9C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE configuration_client_announcement (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, extra JSON DEFAULT NULL, code VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_71AECC53B03A8386 (created_by_id), INDEX IDX_71AECC53896DBBDE (updated_by_id), INDEX IDX_71AECC53C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE content (id INT AUTO_INCREMENT NOT NULL, chapter_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, title VARCHAR(255) NOT NULL, content LONGTEXT NOT NULL, position INT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_FEC530A9579F4768 (chapter_id), INDEX IDX_FEC530A9B03A8386 (created_by_id), INDEX IDX_FEC530A9896DBBDE (updated_by_id), INDEX IDX_FEC530A9C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course (id INT AUTO_INCREMENT NOT NULL, translation_id INT DEFAULT NULL, category_id INT DEFAULT NULL, level_id INT DEFAULT NULL, type_course_id INT DEFAULT NULL, type_diploma_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, code VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, open TINYINT(1) NOT NULL, locale VARCHAR(10) NOT NULL, points INT DEFAULT NULL, general_information LONGTEXT DEFAULT NULL, active TINYINT(1) DEFAULT NULL, documentation LONGTEXT DEFAULT NULL, open_visible TINYINT(1) DEFAULT NULL, is_new TINYINT(1) NOT NULL, new_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', sort INT DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_169E6FB99CAA2B25 (translation_id), INDEX IDX_169E6FB912469DE2 (category_id), INDEX IDX_169E6FB95FB14BA7 (level_id), INDEX IDX_169E6FB9EDDA8882 (type_course_id), INDEX IDX_169E6FB9B4991890 (type_diploma_id), INDEX IDX_169E6FB9B03A8386 (created_by_id), INDEX IDX_169E6FB9896DBBDE (updated_by_id), INDEX IDX_169E6FB9C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_professional_category (course_id INT NOT NULL, professional_category_id INT NOT NULL, INDEX IDX_30476A2C591CC992 (course_id), INDEX IDX_30476A2CA0CCFBB2 (professional_category_id), PRIMARY KEY(course_id, professional_category_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_manager (course_id INT NOT NULL, user_id INT NOT NULL, INDEX IDX_F2E72055591CC992 (course_id), INDEX IDX_F2E72055A76ED395 (user_id), PRIMARY KEY(course_id, user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_tag (course_id INT NOT NULL, tag_id INT NOT NULL, INDEX IDX_760531B1591CC992 (course_id), INDEX IDX_760531B1BAD26311 (tag_id), PRIMARY KEY(course_id, tag_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_filter (course_id INT NOT NULL, filter_id INT NOT NULL, INDEX IDX_D1FC9E50591CC992 (course_id), INDEX IDX_D1FC9E50D395B25E (filter_id), PRIMARY KEY(course_id, filter_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_category (id INT AUTO_INCREMENT NOT NULL, parent_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, sort INT NOT NULL, order_type VARCHAR(20) DEFAULT NULL, order_properties JSON DEFAULT NULL, description LONGTEXT DEFAULT NULL, active TINYINT(1) NOT NULL, INDEX IDX_AFF87497727ACA70 (parent_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_category_type_course (course_category_id INT NOT NULL, type_course_id INT NOT NULL, INDEX IDX_5CE7DCD6628AD36 (course_category_id), INDEX IDX_5CE7DCDEDDA8882 (type_course_id), PRIMARY KEY(course_category_id, type_course_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_category_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_84EE3C232C2AC5D3 (translatable_id), UNIQUE INDEX course_category_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_level (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_7341CBFBB03A8386 (created_by_id), INDEX IDX_7341CBFB896DBBDE (updated_by_id), INDEX IDX_7341CBFBC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_section (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, slug VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, active TINYINT(1) NOT NULL, sort INT NOT NULL, hide_category_name TINYINT(1) DEFAULT \'0\' NOT NULL, type VARCHAR(100) DEFAULT NULL, is_main TINYINT(1) DEFAULT NULL, is_manual_selection TINYINT(1) DEFAULT NULL, meta LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:array)\', created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_25B07F03989D9B62 (slug), INDEX IDX_25B07F03B03A8386 (created_by_id), INDEX IDX_25B07F03896DBBDE (updated_by_id), INDEX IDX_25B07F03C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_section_course_category (course_section_id INT NOT NULL, course_category_id INT NOT NULL, INDEX IDX_2FCB6D087C1ADF9 (course_section_id), INDEX IDX_2FCB6D086628AD36 (course_category_id), PRIMARY KEY(course_section_id, course_category_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_section_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_B600CF812C2AC5D3 (translatable_id), UNIQUE INDEX course_section_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_segment (id INT AUTO_INCREMENT NOT NULL, course_segment_category_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_1042F089ED988ED (course_segment_category_id), INDEX IDX_1042F089B03A8386 (created_by_id), INDEX IDX_1042F089896DBBDE (updated_by_id), INDEX IDX_1042F089C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_segment_course (course_segment_id INT NOT NULL, course_id INT NOT NULL, INDEX IDX_959B62B44CB242E (course_segment_id), INDEX IDX_959B62B4591CC992 (course_id), PRIMARY KEY(course_segment_id, course_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_segment_category (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_segment_category_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_858433AF2C2AC5D3 (translatable_id), UNIQUE INDEX course_segment_category_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_segment_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_A8F650602C2AC5D3 (translatable_id), UNIQUE INDEX course_segment_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE course_stat (id INT AUTO_INCREMENT NOT NULL, course_id INT DEFAULT NULL, valoration_nps NUMERIC(10, 2) DEFAULT NULL, count_valoration_nps NUMERIC(10, 2) DEFAULT NULL, INDEX IDX_E81A2227591CC992 (course_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cron_job (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(191) NOT NULL, command VARCHAR(1024) NOT NULL, schedule VARCHAR(191) NOT NULL, description VARCHAR(191) NOT NULL, enabled TINYINT(1) NOT NULL, UNIQUE INDEX un_name (name), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE cron_report (id INT AUTO_INCREMENT NOT NULL, job_id INT DEFAULT NULL, run_at DATETIME NOT NULL, run_time DOUBLE PRECISION NOT NULL, exit_code INT NOT NULL, output LONGTEXT NOT NULL, error LONGTEXT NOT NULL, INDEX IDX_B6C6A7F5BE04EA9 (job_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE department (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE documentation (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, title VARCHAR(255) NOT NULL, description LONGTEXT NOT NULL, roles JSON NOT NULL, type VARCHAR(10) NOT NULL, url VARCHAR(255) DEFAULT NULL, file VARCHAR(255) DEFAULT NULL, locale VARCHAR(20) NOT NULL, position INT NOT NULL, thumbnail VARCHAR(255) DEFAULT NULL, video_identifier VARCHAR(100) DEFAULT NULL, filename VARCHAR(255) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_73D5A93BB03A8386 (created_by_id), INDEX IDX_73D5A93B896DBBDE (updated_by_id), INDEX IDX_73D5A93BC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE email_notification (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, itinerary_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, forum_post_id INT DEFAULT NULL, sent TINYINT(1) DEFAULT NULL, type VARCHAR(50) DEFAULT NULL, title VARCHAR(100) DEFAULT NULL, message VARCHAR(250) DEFAULT NULL, attributes JSON DEFAULT NULL, translation_text VARCHAR(255) DEFAULT NULL, translation_title VARCHAR(255) DEFAULT NULL, extra JSON DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_EA479099A76ED395 (user_id), INDEX IDX_EA47909915F737B2 (itinerary_id), INDEX IDX_EA479099913AEA17 (announcement_id), INDEX IDX_EA479099BA454E5D (forum_post_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE email_recipient (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, template_id INT NOT NULL, created_at DATETIME NOT NULL, sent_at DATETIME DEFAULT NULL, status VARCHAR(255) DEFAULT NULL, INDEX IDX_670F6462A76ED395 (user_id), INDEX IDX_670F64625DA0FB8 (template_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE email_template (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, subject VARCHAR(255) NOT NULL, body LONGTEXT NOT NULL, sent_at DATETIME DEFAULT NULL, completed_at DATETIME DEFAULT NULL, status VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_9C0600CAB03A8386 (created_by_id), INDEX IDX_9C0600CA896DBBDE (updated_by_id), INDEX IDX_9C0600CAC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE export (id INT AUTO_INCREMENT NOT NULL, task_id INT DEFAULT NULL, created_at DATETIME NOT NULL, created_by INT NOT NULL, updated_at DATETIME NOT NULL, updated_by INT NOT NULL, deleted_at DATETIME DEFAULT NULL, deleted_by INT DEFAULT NULL, finished_at DATETIME DEFAULT NULL, available_until DATETIME DEFAULT NULL, filename VARCHAR(255) DEFAULT NULL, meta JSON DEFAULT NULL, type VARCHAR(50) NOT NULL, UNIQUE INDEX UNIQ_428C16948DB60186 (task_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE files_history_task (id INT AUTO_INCREMENT NOT NULL, history_delivery_task_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) DEFAULT NULL, file_size INT DEFAULT NULL, mime_type VARCHAR(60) DEFAULT NULL, INDEX IDX_E94FD4805445F77F (history_delivery_task_id), INDEX IDX_E94FD480B03A8386 (created_by_id), INDEX IDX_E94FD480896DBBDE (updated_by_id), INDEX IDX_E94FD480C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE files_manager (id INT AUTO_INCREMENT NOT NULL, files_manager_extra_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) NOT NULL, file_size INT NOT NULL, mime_type VARCHAR(255) DEFAULT NULL, required_role VARCHAR(50) NOT NULL, file_path VARCHAR(255) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_5BF93AEE74034854 (files_manager_extra_id), INDEX IDX_5BF93AEEB03A8386 (created_by_id), INDEX IDX_5BF93AEE896DBBDE (updated_by_id), INDEX IDX_5BF93AEEC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE files_manager_extra (id INT AUTO_INCREMENT NOT NULL, type VARCHAR(50) NOT NULL, entity_id VARCHAR(40) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE files_task (id INT AUTO_INCREMENT NOT NULL, task_course_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, type_material VARCHAR(255) DEFAULT NULL, url_material LONGTEXT DEFAULT NULL, is_download TINYINT(1) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) DEFAULT NULL, file_size INT DEFAULT NULL, mime_type VARCHAR(60) DEFAULT NULL, INDEX IDX_D5B6DE44263604C3 (task_course_id), INDEX IDX_D5B6DE44B03A8386 (created_by_id), INDEX IDX_D5B6DE44896DBBDE (updated_by_id), INDEX IDX_D5B6DE44C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE fillgaps (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, text LONGTEXT NOT NULL, answers LONGTEXT NOT NULL, time INT NOT NULL, label VARCHAR(255) NOT NULL, extra LONGTEXT DEFAULT NULL, INDEX IDX_47AB1DD3579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE filter (id INT AUTO_INCREMENT NOT NULL, filter_category_id INT DEFAULT NULL, parent_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, code VARCHAR(255) DEFAULT NULL, source VARCHAR(10) DEFAULT NULL, INDEX IDX_7FC45F1D6B444833 (filter_category_id), INDEX IDX_7FC45F1D727ACA70 (parent_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE filter_category (id INT AUTO_INCREMENT NOT NULL, parent_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, INDEX IDX_3B231C61727ACA70 (parent_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE filter_category_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_F5C167262C2AC5D3 (translatable_id), UNIQUE INDEX filter_category_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE filter_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_CD6B90BE2C2AC5D3 (translatable_id), UNIQUE INDEX filter_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE forum_likes (id INT AUTO_INCREMENT NOT NULL, forum_post_id INT DEFAULT NULL, user_id INT DEFAULT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_51BEE8AABA454E5D (forum_post_id), INDEX IDX_51BEE8AAA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE forum_post (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, parent_id INT DEFAULT NULL, course_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, response_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, title VARCHAR(255) DEFAULT NULL, message LONGTEXT NOT NULL, last_response_at DATETIME DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_996BCC5AA76ED395 (user_id), INDEX IDX_996BCC5A727ACA70 (parent_id), INDEX IDX_996BCC5A591CC992 (course_id), INDEX IDX_996BCC5A913AEA17 (announcement_id), INDEX IDX_996BCC5AFBF32840 (response_id), INDEX IDX_996BCC5AB03A8386 (created_by_id), INDEX IDX_996BCC5A896DBBDE (updated_by_id), INDEX IDX_996BCC5AC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE forum_report (id INT AUTO_INCREMENT NOT NULL, forum_post_id INT DEFAULT NULL, user_id INT DEFAULT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_DC804455BA454E5D (forum_post_id), INDEX IDX_DC804455A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE gamesword (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, question VARCHAR(255) NOT NULL, word VARCHAR(255) NOT NULL, time INT NOT NULL, gametype VARCHAR(255) NOT NULL, INDEX IDX_2CD5DB38579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE generic_token (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, token VARCHAR(255) NOT NULL, type VARCHAR(100) NOT NULL, entity_id INT NOT NULL, extra JSON DEFAULT NULL, valid_from DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', valid_until DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', revoked TINYINT(1) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_360B1929B03A8386 (created_by_id), INDEX IDX_360B1929896DBBDE (updated_by_id), INDEX IDX_360B1929C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE guessword (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, question VARCHAR(255) NOT NULL, word VARCHAR(255) NOT NULL, time INT NOT NULL, INDEX IDX_32444B08579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE help_category (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE help_category_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_ED35F7482C2AC5D3 (translatable_id), UNIQUE INDEX help_category_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE help_text (id INT AUTO_INCREMENT NOT NULL, category_id INT DEFAULT NULL, title VARCHAR(255) NOT NULL, text LONGTEXT NOT NULL, INDEX IDX_7E01925C12469DE2 (category_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE help_text_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, title VARCHAR(255) NOT NULL, text LONGTEXT NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_857DDBF02C2AC5D3 (translatable_id), UNIQUE INDEX help_text_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE higher_lower (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, title VARCHAR(255) NOT NULL, time INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_7C0E1150579F4768 (chapter_id), INDEX IDX_7C0E1150B03A8386 (created_by_id), INDEX IDX_7C0E1150896DBBDE (updated_by_id), INDEX IDX_7C0E1150C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE higher_lower_words (id INT AUTO_INCREMENT NOT NULL, higher_lower_id INT DEFAULT NULL, position INT NOT NULL, word VARCHAR(255) NOT NULL, INDEX IDX_177FA2D2BE9CE5EB (higher_lower_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE history_delivery_task (id INT AUTO_INCREMENT NOT NULL, task_user_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, state INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_A9B9C8D8B88FF97F (task_user_id), INDEX IDX_A9B9C8D8B03A8386 (created_by_id), INDEX IDX_A9B9C8D8896DBBDE (updated_by_id), INDEX IDX_A9B9C8D8C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE history_seen_material (id INT AUTO_INCREMENT NOT NULL, course_id INT DEFAULT NULL, user_id INT DEFAULT NULL, material_course_id INT DEFAULT NULL, INDEX IDX_C83ABC55591CC992 (course_id), INDEX IDX_C83ABC55A76ED395 (user_id), INDEX IDX_C83ABC556182D372 (material_course_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE holes (id INT AUTO_INCREMENT NOT NULL, fillgap_id INT DEFAULT NULL, hole INT NOT NULL, answer VARCHAR(255) NOT NULL, correct TINYINT(1) NOT NULL, INDEX IDX_9C6EA1E4477D657A (fillgap_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE integration_group (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(100) NOT NULL, active TINYINT(1) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE integration_mapping (id INT AUTO_INCREMENT NOT NULL, integration_group_id INT DEFAULT NULL, entity VARCHAR(255) DEFAULT NULL, identifier VARCHAR(255) DEFAULT NULL, type VARCHAR(20) DEFAULT NULL, mapping JSON NOT NULL, INDEX IDX_24CA96D3EC8E1CAD (integration_group_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE itinerary (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, tags LONGTEXT DEFAULT NULL, active TINYINT(1) NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_FF2238F6B03A8386 (created_by_id), INDEX IDX_FF2238F6896DBBDE (updated_by_id), INDEX IDX_FF2238F6C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE itinerary_filter (itinerary_id INT NOT NULL, filter_id INT NOT NULL, INDEX IDX_C74482815F737B2 (itinerary_id), INDEX IDX_C744828D395B25E (filter_id), PRIMARY KEY(itinerary_id, filter_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE itinerary_course (id INT AUTO_INCREMENT NOT NULL, itinerary_id INT NOT NULL, course_id INT NOT NULL, position INT NOT NULL, INDEX IDX_652E788C15F737B2 (itinerary_id), INDEX IDX_652E788C591CC992 (course_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE itinerary_manager (id INT AUTO_INCREMENT NOT NULL, itinerary_id INT DEFAULT NULL, user_id INT DEFAULT NULL, INDEX IDX_ACE4518D15F737B2 (itinerary_id), INDEX IDX_ACE4518DA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE itinerary_tags (id INT AUTO_INCREMENT NOT NULL, filter_id INT NOT NULL, itinerary_id INT NOT NULL, INDEX IDX_689FFE72D395B25E (filter_id), INDEX IDX_689FFE7215F737B2 (itinerary_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE itinerary_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_D12AC99A2C2AC5D3 (translatable_id), UNIQUE INDEX itinerary_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE itinerary_user (id INT AUTO_INCREMENT NOT NULL, itinerary_id INT NOT NULL, user_id INT NOT NULL, INDEX IDX_8AB0BC1D15F737B2 (itinerary_id), INDEX IDX_8AB0BC1DA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE library (id INT AUTO_INCREMENT NOT NULL, category_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(100) NOT NULL, description LONGTEXT DEFAULT NULL, type VARCHAR(10) NOT NULL, enable_rating TINYINT(1) NOT NULL, enable_comments TINYINT(1) NOT NULL, thumbnail VARCHAR(255) DEFAULT NULL, url VARCHAR(255) DEFAULT NULL, locale VARCHAR(4) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_A18098BC12469DE2 (category_id), INDEX IDX_A18098BCB03A8386 (created_by_id), INDEX IDX_A18098BC896DBBDE (updated_by_id), INDEX IDX_A18098BCC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE library_filter (library_id INT NOT NULL, filter_id INT NOT NULL, INDEX IDX_D426C796FE2541D7 (library_id), INDEX IDX_D426C796D395B25E (filter_id), PRIMARY KEY(library_id, filter_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE library_category (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(100) NOT NULL, active TINYINT(1) NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_20EFE8D5B03A8386 (created_by_id), INDEX IDX_20EFE8D5896DBBDE (updated_by_id), INDEX IDX_20EFE8D5C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE library_category_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(100) NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_F72523D02C2AC5D3 (translatable_id), UNIQUE INDEX library_category_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE library_comment (id INT AUTO_INCREMENT NOT NULL, library_id INT NOT NULL, allowed_by_id INT DEFAULT NULL, banned_by_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, comment LONGTEXT NOT NULL, rating SMALLINT NOT NULL, visible TINYINT(1) NOT NULL, allowed_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', banned_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_EEB5EA5CFE2541D7 (library_id), INDEX IDX_EEB5EA5C69463ADC (allowed_by_id), INDEX IDX_EEB5EA5C386B8E7 (banned_by_id), INDEX IDX_EEB5EA5CB03A8386 (created_by_id), INDEX IDX_EEB5EA5C896DBBDE (updated_by_id), INDEX IDX_EEB5EA5CC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE library_file (id INT AUTO_INCREMENT NOT NULL, library_id INT NOT NULL, name VARCHAR(100) NOT NULL, type VARCHAR(20) NOT NULL, url VARCHAR(255) DEFAULT NULL, filename VARCHAR(255) DEFAULT NULL, UNIQUE INDEX UNIQ_2A50FC7DFE2541D7 (library_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE library_video (id INT AUTO_INCREMENT NOT NULL, library_id INT NOT NULL, name VARCHAR(100) NOT NULL, type VARCHAR(20) NOT NULL, url VARCHAR(255) NOT NULL, source VARCHAR(20) NOT NULL, identifier VARCHAR(255) DEFAULT NULL, UNIQUE INDEX UNIQ_4F620803FE2541D7 (library_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE library_views (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, library_id INT NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_22554EA8A76ED395 (user_id), INDEX IDX_22554EA8FE2541D7 (library_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE main_course_evaluation (id INT AUTO_INCREMENT NOT NULL, course_id INT DEFAULT NULL, is_main_nps TINYINT(1) DEFAULT NULL, UNIQUE INDEX UNIQ_ABF7398B591CC992 (course_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE material_course (id INT AUTO_INCREMENT NOT NULL, course_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, mime_type VARCHAR(255) DEFAULT NULL, type_material VARCHAR(255) NOT NULL, url_material LONGTEXT DEFAULT NULL, is_download TINYINT(1) DEFAULT NULL, is_visible TINYINT(1) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) DEFAULT NULL, file_size INT DEFAULT NULL, INDEX IDX_87ACE482591CC992 (course_id), INDEX IDX_87ACE482913AEA17 (announcement_id), INDEX IDX_87ACE482B03A8386 (created_by_id), INDEX IDX_87ACE482896DBBDE (updated_by_id), INDEX IDX_87ACE482C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE material_download_history (id INT AUTO_INCREMENT NOT NULL, course_id INT DEFAULT NULL, user_id INT DEFAULT NULL, material_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_7180BF5B591CC992 (course_id), INDEX IDX_7180BF5BA76ED395 (user_id), INDEX IDX_7180BF5BE308AC6F (material_id), INDEX IDX_7180BF5BB03A8386 (created_by_id), INDEX IDX_7180BF5B896DBBDE (updated_by_id), INDEX IDX_7180BF5BC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE meetingzoom_token (id INT AUTO_INCREMENT NOT NULL, token LONGTEXT NOT NULL, created_at DATETIME NOT NULL, user_id INT DEFAULT NULL, estado VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE message (id INT AUTO_INCREMENT NOT NULL, sender_id INT NOT NULL, recipient_id INT NOT NULL, reply_to_id INT DEFAULT NULL, subject VARCHAR(255) NOT NULL, body LONGTEXT NOT NULL, open_at DATETIME DEFAULT NULL, sent_at DATETIME NOT NULL, INDEX IDX_B6BD307FF624B39D (sender_id), INDEX IDX_B6BD307FE92F8F78 (recipient_id), INDEX IDX_B6BD307FFFDF7169 (reply_to_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE message_attachment (id INT AUTO_INCREMENT NOT NULL, message_id INT NOT NULL, filename VARCHAR(255) DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, mime_type VARCHAR(255) DEFAULT NULL, INDEX IDX_B68FF524537A1329 (message_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE news (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, title LONGTEXT NOT NULL, text LONGTEXT NOT NULL, locale VARCHAR(10) NOT NULL, image VARCHAR(255) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_1DD39950B03A8386 (created_by_id), INDEX IDX_1DD39950896DBBDE (updated_by_id), INDEX IDX_1DD39950C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE news_course_section (news_id INT NOT NULL, course_section_id INT NOT NULL, INDEX IDX_BA5A78CAB5A459A0 (news_id), INDEX IDX_BA5A78CA7C1ADF9 (course_section_id), PRIMARY KEY(news_id, course_section_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE news_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, title LONGTEXT DEFAULT NULL, text LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_9D5CF3202C2AC5D3 (translatable_id), UNIQUE INDEX news_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE notification (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, message LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, read_at DATETIME DEFAULT NULL, data JSON DEFAULT NULL, INDEX IDX_BF5476CAA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE nps (id INT AUTO_INCREMENT NOT NULL, question_id INT NOT NULL, course_id INT DEFAULT NULL, user_id INT NOT NULL, announcement_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, value LONGTEXT DEFAULT NULL, to_post TINYINT(1) NOT NULL, main TINYINT(1) DEFAULT NULL, type VARCHAR(50) DEFAULT NULL, highlight TINYINT(1) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_5B3B66481E27F6BF (question_id), INDEX IDX_5B3B6648591CC992 (course_id), INDEX IDX_5B3B6648A76ED395 (user_id), INDEX IDX_5B3B6648913AEA17 (announcement_id), INDEX IDX_5B3B6648B03A8386 (created_by_id), INDEX IDX_5B3B6648896DBBDE (updated_by_id), INDEX IDX_5B3B6648C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE nps_question (id INT AUTO_INCREMENT NOT NULL, survey_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, type VARCHAR(255) NOT NULL, main TINYINT(1) NOT NULL, question LONGTEXT NOT NULL, position INT NOT NULL, source INT NOT NULL, entity_subsidizer VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, random_order TINYINT(1) DEFAULT NULL, is_required TINYINT(1) DEFAULT NULL, is_confidential TINYINT(1) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_27D40D91B3FE509D (survey_id), INDEX IDX_27D40D91B03A8386 (created_by_id), INDEX IDX_27D40D91896DBBDE (updated_by_id), INDEX IDX_27D40D91C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE nps_question_course (nps_question_id INT NOT NULL, course_id INT NOT NULL, INDEX IDX_5B1CA872AE807F50 (nps_question_id), INDEX IDX_5B1CA872591CC992 (course_id), PRIMARY KEY(nps_question_id, course_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE nps_question_announcement (nps_question_id INT NOT NULL, announcement_id INT NOT NULL, INDEX IDX_52B16383AE807F50 (nps_question_id), INDEX IDX_52B16383913AEA17 (announcement_id), PRIMARY KEY(nps_question_id, announcement_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE nps_question_detail (id INT AUTO_INCREMENT NOT NULL, nps_question_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, value LONGTEXT NOT NULL, position SMALLINT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_6384B858AE807F50 (nps_question_id), INDEX IDX_6384B858B03A8386 (created_by_id), INDEX IDX_6384B858896DBBDE (updated_by_id), INDEX IDX_6384B858C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE nps_question_detail_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, value LONGTEXT NOT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_BFEB50BF2C2AC5D3 (translatable_id), UNIQUE INDEX nps_question_detail_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE nps_question_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, question LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_6650B4BC2C2AC5D3 (translatable_id), UNIQUE INDEX nps_question_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ordenar_menormayor (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, words_array VARCHAR(255) NOT NULL, title VARCHAR(255) DEFAULT NULL, time INT NOT NULL, gametype VARCHAR(255) DEFAULT NULL, INDEX IDX_C414560B579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE pages (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, main TINYINT(1) NOT NULL, position INT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_2074E575B03A8386 (created_by_id), INDEX IDX_2074E575896DBBDE (updated_by_id), INDEX IDX_2074E575C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE pages_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_7D0CA9D12C2AC5D3 (translatable_id), UNIQUE INDEX pages_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE parejas (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, tipo INT NOT NULL, tiempo INT NOT NULL, INDEX IDX_CDEC53A2579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE parejas_imagen (id INT AUTO_INCREMENT NOT NULL, parejas_id INT DEFAULT NULL, texto VARCHAR(255) NOT NULL, image VARCHAR(255) DEFAULT NULL, INDEX IDX_4907912B8497EC37 (parejas_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE pdf (id INT AUTO_INCREMENT NOT NULL, chapter_id INT NOT NULL, pdf VARCHAR(255) DEFAULT NULL, is_downloadable TINYINT(1) NOT NULL, UNIQUE INDEX UNIQ_EF0DB8C579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE ppt (id INT AUTO_INCREMENT NOT NULL, chapter_id INT NOT NULL, ppt VARCHAR(255) DEFAULT NULL, is_downloadable TINYINT(1) NOT NULL, slides SMALLINT NOT NULL, UNIQUE INDEX UNIQ_D3E77D91579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE professional_category (id INT AUTO_INCREMENT NOT NULL, parent_id INT DEFAULT NULL, code VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, INDEX IDX_95CAC37C727ACA70 (parent_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE puzzle (id INT AUTO_INCREMENT NOT NULL, chapter_id INT NOT NULL, image VARCHAR(255) DEFAULT NULL, UNIQUE INDEX UNIQ_22A6DFDF579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE question (id INT AUTO_INCREMENT NOT NULL, chapter_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, question LONGTEXT NOT NULL, image VARCHAR(255) DEFAULT NULL, random INT DEFAULT NULL, is_feedback TINYINT(1) DEFAULT NULL, feedback_positive VARCHAR(255) DEFAULT NULL, feedback_negative VARCHAR(255) DEFAULT NULL, time NUMERIC(10, 0) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_B6F7494E579F4768 (chapter_id), INDEX IDX_B6F7494EB03A8386 (created_by_id), INDEX IDX_B6F7494E896DBBDE (updated_by_id), INDEX IDX_B6F7494EC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE questions_announcement (id INT AUTO_INCREMENT NOT NULL, section VARCHAR(255) NOT NULL, section_json LONGTEXT DEFAULT NULL, questions_json LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE recovery_code (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, date_recovery DATE NOT NULL, code_activation VARCHAR(255) NOT NULL, email VARCHAR(255) NOT NULL, state INT NOT NULL, INDEX IDX_2C8D0584A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE refresh_tokens (id INT AUTO_INCREMENT NOT NULL, refresh_token VARCHAR(128) NOT NULL, username VARCHAR(255) NOT NULL, valid DATETIME NOT NULL, extra JSON DEFAULT NULL, UNIQUE INDEX UNIQ_9BACE7E1C74F2195 (refresh_token), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE reset_password_request (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, selector VARCHAR(20) NOT NULL, hashed_token VARCHAR(100) NOT NULL, requested_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', expires_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_7CE748AA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE roleplay_beginning (id INT AUTO_INCREMENT NOT NULL, project_id INT NOT NULL, code VARCHAR(255) NOT NULL, type VARCHAR(255) NOT NULL, background LONGTEXT DEFAULT NULL, description LONGTEXT DEFAULT NULL, `order` INT NOT NULL, attached_tag LONGTEXT DEFAULT NULL, attached LONGTEXT DEFAULT NULL, INDEX IDX_E89A902B166D1F9C (project_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE roleplay_ending (id INT AUTO_INCREMENT NOT NULL, project_id INT NOT NULL, code VARCHAR(255) NOT NULL, type VARCHAR(255) NOT NULL, background LONGTEXT DEFAULT NULL, description LONGTEXT DEFAULT NULL, `order` INT NOT NULL, approval_by_points INT DEFAULT NULL, approval_by_history TINYINT(1) NOT NULL, approval_type VARCHAR(60) DEFAULT NULL, statement LONGTEXT DEFAULT NULL, suspended_description LONGTEXT DEFAULT NULL, INDEX IDX_3A4A6E5B166D1F9C (project_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE roleplay_project (id INT AUTO_INCREMENT NOT NULL, title VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE roleplay_scene (id INT AUTO_INCREMENT NOT NULL, sequence_id INT NOT NULL, code VARCHAR(255) NOT NULL, type VARCHAR(255) NOT NULL, attached_tag LONGTEXT DEFAULT NULL, attached LONGTEXT DEFAULT NULL, background LONGTEXT DEFAULT NULL, avatar LONGTEXT DEFAULT NULL, video LONGTEXT DEFAULT NULL, standby LONGTEXT DEFAULT NULL, statement LONGTEXT DEFAULT NULL, `order` INT NOT NULL, answers JSON NOT NULL, INDEX IDX_34CE7AA798FB19AE (sequence_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE roleplay_sequence (id INT AUTO_INCREMENT NOT NULL, project_id INT NOT NULL, color VARCHAR(255) NOT NULL, `order` INT NOT NULL, INDEX IDX_579C74B5166D1F9C (project_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE roulette_word (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, letter VARCHAR(2) NOT NULL, word VARCHAR(255) DEFAULT NULL, question VARCHAR(255) DEFAULT NULL, type TINYINT(1) DEFAULT NULL, INDEX IDX_EDB2A53C579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE scorm (id INT AUTO_INCREMENT NOT NULL, chapter_id INT NOT NULL, folder VARCHAR(255) NOT NULL, entry_point VARCHAR(255) DEFAULT NULL, menu JSON DEFAULT NULL, show_menu TINYINT(1) NOT NULL, UNIQUE INDEX UNIQ_3C42BF63579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE season (id INT AUTO_INCREMENT NOT NULL, course_id INT NOT NULL, name VARCHAR(255) NOT NULL, sort INT NOT NULL, type VARCHAR(20) DEFAULT \'sequential\' NOT NULL, INDEX IDX_F0E45BA9591CC992 (course_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE section_default_front (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, is_active TINYINT(1) DEFAULT NULL, id_section INT DEFAULT NULL, is_open_course TINYINT(1) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE section_default_front_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_B2D0C7942C2AC5D3 (translatable_id), UNIQUE INDEX section_default_front_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE sessions_announcement (id INT AUTO_INCREMENT NOT NULL, announcement_id INT DEFAULT NULL, hour_session INT DEFAULT NULL, name_announcement VARCHAR(255) DEFAULT NULL, classroom VARCHAR(255) DEFAULT NULL, num_sessions INT DEFAULT NULL, extra LONGTEXT DEFAULT NULL, assistance LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_6A027C4B913AEA17 (announcement_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE setting (id INT AUTO_INCREMENT NOT NULL, setting_group_id INT NOT NULL, code VARCHAR(255) NOT NULL, value LONGTEXT DEFAULT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, sort INT NOT NULL, options JSON NOT NULL, type VARCHAR(60) NOT NULL, INDEX IDX_9F74B89850DDE1BD (setting_group_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE setting_group (id INT AUTO_INCREMENT NOT NULL, title VARCHAR(255) NOT NULL, sort INT NOT NULL, code VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE survey (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, active TINYINT(1) NOT NULL, description LONGTEXT DEFAULT NULL, apply_to SMALLINT NOT NULL, meta LONGTEXT DEFAULT NULL COMMENT \'(DC2Type:array)\', is_main TINYINT(1) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_AD5F9BFCB03A8386 (created_by_id), INDEX IDX_AD5F9BFC896DBBDE (updated_by_id), INDEX IDX_AD5F9BFCC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE survey_announcement (id INT AUTO_INCREMENT NOT NULL, survey_id INT NOT NULL, announcement_id INT NOT NULL, INDEX IDX_3D6BD54CB3FE509D (survey_id), INDEX IDX_3D6BD54C913AEA17 (announcement_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE survey_course (id INT AUTO_INCREMENT NOT NULL, survey_id INT NOT NULL, course_id INT NOT NULL, INDEX IDX_3E90A7D5B3FE509D (survey_id), INDEX IDX_3E90A7D5591CC992 (course_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE survey_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_C919A6A2C2AC5D3 (translatable_id), UNIQUE INDEX survey_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE tag (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, slug VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE task (id INT AUTO_INCREMENT NOT NULL, task VARCHAR(255) NOT NULL, params JSON DEFAULT NULL, status SMALLINT NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', started_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', finished_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE task_course (id INT AUTO_INCREMENT NOT NULL, course_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, title VARCHAR(255) NOT NULL, description LONGTEXT NOT NULL, date_delivery DATETIME NOT NULL, date_delivery_announcement DATETIME DEFAULT NULL, start_date DATETIME DEFAULT NULL, is_visible TINYINT(1) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_2D37EB6A591CC992 (course_id), INDEX IDX_2D37EB6A913AEA17 (announcement_id), INDEX IDX_2D37EB6AB03A8386 (created_by_id), INDEX IDX_2D37EB6A896DBBDE (updated_by_id), INDEX IDX_2D37EB6AC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE task_course_group (id INT AUTO_INCREMENT NOT NULL, task_course_id INT DEFAULT NULL, announcement_group_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, num_group INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_4CFA6700263604C3 (task_course_id), INDEX IDX_4CFA6700851953B4 (announcement_group_id), INDEX IDX_4CFA6700B03A8386 (created_by_id), INDEX IDX_4CFA6700896DBBDE (updated_by_id), INDEX IDX_4CFA6700C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE task_user (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, task_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, timezone VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, timezone_created_at DATETIME DEFAULT NULL, timezone_updated_at DATETIME DEFAULT NULL, INDEX IDX_FE204232A76ED395 (user_id), INDEX IDX_FE2042328DB60186 (task_id), INDEX IDX_FE204232B03A8386 (created_by_id), INDEX IDX_FE204232896DBBDE (updated_by_id), INDEX IDX_FE204232C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE time_game (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, time NUMERIC(10, 0) DEFAULT NULL, INDEX IDX_8E9638A1579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE true_or_false (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, image VARCHAR(255) DEFAULT NULL, text VARCHAR(255) NOT NULL, result TINYINT(1) NOT NULL, trueorfalse_id INT DEFAULT NULL, time INT DEFAULT NULL, categorized TINYINT(1) NOT NULL, INDEX IDX_CA13A6C3579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_course (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description LONGTEXT DEFAULT NULL, active TINYINT(1) NOT NULL, code VARCHAR(50) DEFAULT NULL, denomination VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_35490481B03A8386 (created_by_id), INDEX IDX_35490481896DBBDE (updated_by_id), INDEX IDX_35490481C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_course_alerts (id INT AUTO_INCREMENT NOT NULL, type_course_id INT NOT NULL, alert_type_tutor_id INT NOT NULL, INDEX IDX_29AFB613EDDA8882 (type_course_id), INDEX IDX_29AFB6135D628A82 (alert_type_tutor_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_course_announcement_step_configuration (id INT AUTO_INCREMENT NOT NULL, announcement_configuration_type_id INT NOT NULL, type_course_announcement_step_creation_id INT NOT NULL, active TINYINT(1) DEFAULT NULL, INDEX IDX_82556DB275D5DC16 (announcement_configuration_type_id), INDEX IDX_82556DB26DFFE627 (type_course_announcement_step_creation_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_course_announcement_step_creation (id INT AUTO_INCREMENT NOT NULL, type_course_id INT NOT NULL, announcement_step_creation_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, position INT DEFAULT NULL, is_required TINYINT(1) DEFAULT NULL, extra JSON DEFAULT NULL, active TINYINT(1) DEFAULT NULL, INDEX IDX_86190621EDDA8882 (type_course_id), INDEX IDX_86190621556ED2C3 (announcement_step_creation_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_course_announcement_step_creation_trans (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_7100AE352C2AC5D3 (translatable_id), UNIQUE INDEX type_course_announcement_step_creation_trans_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_course_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description LONGTEXT DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_A7660E6B2C2AC5D3 (translatable_id), UNIQUE INDEX type_course_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_diploma (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) NOT NULL, extra LONGTEXT DEFAULT NULL, is_main TINYINT(1) DEFAULT NULL, apply_to SMALLINT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_C400E6A2B03A8386 (created_by_id), INDEX IDX_C400E6A2896DBBDE (updated_by_id), INDEX IDX_C400E6A2C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_identification (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, active TINYINT(1) DEFAULT NULL, mask LONGTEXT DEFAULT NULL, main TINYINT(1) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_identification_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_75F23B762C2AC5D3 (translatable_id), UNIQUE INDEX type_identification_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_money (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, symbol VARCHAR(20) NOT NULL, country VARCHAR(255) NOT NULL, fractional_unit VARCHAR(255) NOT NULL, code_iso VARCHAR(50) NOT NULL, state TINYINT(1) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_money_translation (id INT AUTO_INCREMENT NOT NULL, translatable_id INT DEFAULT NULL, name VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, locale VARCHAR(5) NOT NULL, INDEX IDX_A87D08C72C2AC5D3 (translatable_id), UNIQUE INDEX type_money_translation_unique_translation (translatable_id, locale), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE type_video (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE url_shortener (id INT AUTO_INCREMENT NOT NULL, long_url LONGTEXT NOT NULL, short_url VARCHAR(10) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user (id INT AUTO_INCREMENT NOT NULL, team_manager_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, email VARCHAR(180) NOT NULL, roles JSON NOT NULL, remote_roles JSON NOT NULL, password VARCHAR(255) NOT NULL, first_name VARCHAR(255) NOT NULL, last_name VARCHAR(255) NOT NULL, is_active TINYINT(1) NOT NULL, code VARCHAR(255) DEFAULT NULL, points INT DEFAULT NULL, avatar VARCHAR(255) DEFAULT NULL, locale VARCHAR(10) DEFAULT NULL, data_avatar VARCHAR(400) DEFAULT NULL, register_key VARCHAR(255) DEFAULT NULL, validated TINYINT(1) NOT NULL, open TINYINT(1) NOT NULL, meta JSON DEFAULT NULL, team_manager_email VARCHAR(255) DEFAULT NULL, starteam TINYINT(1) NOT NULL, custom_filters JSON DEFAULT NULL, timezone VARCHAR(150) DEFAULT NULL, locale_campus VARCHAR(255) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_8D93D649E7927C74 (email), INDEX IDX_8D93D64946E746A6 (team_manager_id), INDEX IDX_8D93D649B03A8386 (created_by_id), INDEX IDX_8D93D649896DBBDE (updated_by_id), INDEX IDX_8D93D649C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_filter (user_id INT NOT NULL, filter_id INT NOT NULL, INDEX IDX_1A964420A76ED395 (user_id), INDEX IDX_1A964420D395B25E (filter_id), PRIMARY KEY(user_id, filter_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE manager_filter (user_id INT NOT NULL, filter_id INT NOT NULL, INDEX IDX_B9E1596FA76ED395 (user_id), INDEX IDX_B9E1596FD395B25E (filter_id), PRIMARY KEY(user_id, filter_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_comments (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, text LONGTEXT NOT NULL, type TINYINT(1) NOT NULL, send_at DATETIME NOT NULL, INDEX IDX_BF13722AA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_company (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, cif VARCHAR(255) DEFAULT NULL, profile VARCHAR(255) DEFAULT NULL, code VARCHAR(255) DEFAULT NULL, external TINYINT(1) DEFAULT \'1\' NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_17B21745B03A8386 (created_by_id), INDEX IDX_17B21745896DBBDE (updated_by_id), INDEX IDX_17B21745C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_course (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, course_id INT NOT NULL, announcement_id INT DEFAULT NULL, started_at DATETIME NOT NULL, finished_at DATETIME DEFAULT NULL, valued_at DATETIME DEFAULT NULL, time_spent INT DEFAULT NULL, points NUMERIC(10, 0) DEFAULT NULL, timezone VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, timezone_created_at DATETIME DEFAULT NULL, timezone_updated_at DATETIME DEFAULT NULL, INDEX IDX_73CC7484A76ED395 (user_id), INDEX IDX_73CC7484591CC992 (course_id), INDEX IDX_73CC7484913AEA17 (announcement_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_course_chapter (id INT AUTO_INCREMENT NOT NULL, user_course_id INT NOT NULL, chapter_id INT NOT NULL, started_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, finished_at DATETIME DEFAULT NULL, data JSON DEFAULT NULL, time_spent INT DEFAULT NULL, points NUMERIC(10, 0) DEFAULT NULL, ip VARCHAR(255) DEFAULT NULL, timezone VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, timezone_created_at DATETIME DEFAULT NULL, timezone_updated_at DATETIME DEFAULT NULL, INDEX IDX_9AC76F9B59FC4476 (user_course_id), INDEX IDX_9AC76F9B579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_courses_total_time (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', time INT NOT NULL, UNIQUE INDEX UNIQ_B7D6BB96A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_extra (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, category_id INT DEFAULT NULL, department_id INT DEFAULT NULL, center_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, gender VARCHAR(255) DEFAULT NULL, birthdate DATE DEFAULT NULL, avatar VARCHAR(255) DEFAULT NULL, resume VARCHAR(255) DEFAULT NULL, division VARCHAR(255) DEFAULT NULL, pdf_cv VARCHAR(255) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_AFFDF63DA76ED395 (user_id), INDEX IDX_AFFDF63D12469DE2 (category_id), INDEX IDX_AFFDF63DAE80F5DF (department_id), INDEX IDX_AFFDF63D5932F377 (center_id), INDEX IDX_AFFDF63DB03A8386 (created_by_id), INDEX IDX_AFFDF63D896DBBDE (updated_by_id), INDEX IDX_AFFDF63DC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_fields_fundae (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, user_company_id INT DEFAULT NULL, user_professional_category_id INT DEFAULT NULL, user_work_center_id INT DEFAULT NULL, user_work_department_id INT DEFAULT NULL, user_study_level_id INT DEFAULT NULL, cv_files_manager_id INT DEFAULT NULL, social_security_number VARCHAR(255) DEFAULT NULL, gender VARCHAR(50) DEFAULT NULL, email_work VARCHAR(255) DEFAULT NULL, birthdate DATE DEFAULT NULL, incapacity TINYINT(1) DEFAULT NULL, victim_of_terrorism TINYINT(1) DEFAULT NULL, gender_violence TINYINT(1) DEFAULT NULL, dni VARCHAR(50) DEFAULT NULL, contribution_account VARCHAR(255) DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, telephone VARCHAR(30) DEFAULT NULL, UNIQUE INDEX UNIQ_DC0416CCA76ED395 (user_id), INDEX IDX_DC0416CC30FCDC3A (user_company_id), INDEX IDX_DC0416CCBC194B79 (user_professional_category_id), INDEX IDX_DC0416CC50BF847B (user_work_center_id), INDEX IDX_DC0416CC42196A10 (user_work_department_id), INDEX IDX_DC0416CC97BD8177 (user_study_level_id), INDEX IDX_DC0416CC911DD53E (cv_files_manager_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_history_download_diploma (id INT AUTO_INCREMENT NOT NULL, course_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, timezone VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, timezone_created_at DATETIME DEFAULT NULL, timezone_updated_at DATETIME DEFAULT NULL, INDEX IDX_8F67717E591CC992 (course_id), INDEX IDX_8F67717E913AEA17 (announcement_id), INDEX IDX_8F67717EB03A8386 (created_by_id), INDEX IDX_8F67717E896DBBDE (updated_by_id), INDEX IDX_8F67717EC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_identification (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, type_identification_id INT NOT NULL, identification_value VARCHAR(100) NOT NULL, INDEX IDX_2262AD54A76ED395 (user_id), INDEX IDX_2262AD54F1CF261E (type_identification_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_login (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, created_at DATETIME NOT NULL, device VARCHAR(100) DEFAULT NULL, browser VARCHAR(100) DEFAULT NULL, platform VARCHAR(255) DEFAULT NULL, timezone VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, timezone_created_at DATETIME DEFAULT NULL, timezone_updated_at DATETIME DEFAULT NULL, INDEX IDX_48CA3048A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_manage (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, centers JSON NOT NULL, countries JSON NOT NULL, UNIQUE INDEX UNIQ_4120B177A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_notification (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, is_active TINYINT(1) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_3F980AC8A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_professional_category (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_55CE8E02B03A8386 (created_by_id), INDEX IDX_55CE8E02896DBBDE (updated_by_id), INDEX IDX_55CE8E02C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_roleplay_project (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, user_course_chapter_id INT NOT NULL, project_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, answers JSON NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_48521FF4A76ED395 (user_id), UNIQUE INDEX UNIQ_48521FF47062B2C0 (user_course_chapter_id), INDEX IDX_48521FF4166D1F9C (project_id), INDEX IDX_48521FF4B03A8386 (created_by_id), INDEX IDX_48521FF4896DBBDE (updated_by_id), INDEX IDX_48521FF4C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_study_level (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_6C8B5566B03A8386 (created_by_id), INDEX IDX_6C8B5566896DBBDE (updated_by_id), INDEX IDX_6C8B5566C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_time (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, course_id INT DEFAULT NULL, announcement_id INT DEFAULT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', name VARCHAR(100) NOT NULL, time INT NOT NULL, extra JSON DEFAULT NULL, ip VARCHAR(255) DEFAULT NULL, updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', timezone VARCHAR(255) DEFAULT NULL, country VARCHAR(255) DEFAULT NULL, timezone_created_at DATETIME DEFAULT NULL, timezone_updated_at DATETIME DEFAULT NULL, INDEX IDX_1515D48CA76ED395 (user_id), INDEX IDX_1515D48C591CC992 (course_id), INDEX IDX_1515D48C913AEA17 (announcement_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_token (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, type SMALLINT NOT NULL, token VARCHAR(255) NOT NULL, revoked TINYINT(1) NOT NULL, used TINYINT(1) NOT NULL, used_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', valid_until DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', extra JSON DEFAULT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_BDF55A63A76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_vcms_project (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, project_id INT NOT NULL, user_course_chapter_id INT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, actions_data JSON NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_2AB6F06AA76ED395 (user_id), INDEX IDX_2AB6F06A166D1F9C (project_id), UNIQUE INDEX UNIQ_2AB6F06A7062B2C0 (user_course_chapter_id), INDEX IDX_2AB6F06AB03A8386 (created_by_id), INDEX IDX_2AB6F06A896DBBDE (updated_by_id), INDEX IDX_2AB6F06AC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_work_center (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_4212E493B03A8386 (created_by_id), INDEX IDX_4212E493896DBBDE (updated_by_id), INDEX IDX_4212E493C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_work_department (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, name VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, state SMALLINT NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_C4A220D6B03A8386 (created_by_id), INDEX IDX_C4A220D6896DBBDE (updated_by_id), INDEX IDX_C4A220D6C76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE vcms_project (id INT AUTO_INCREMENT NOT NULL, title VARCHAR(255) NOT NULL, slides JSON NOT NULL, view VARCHAR(40) DEFAULT NULL, progressive TINYINT(1) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE video (id INT AUTO_INCREMENT NOT NULL, type_video_id INT NOT NULL, chapter_id INT NOT NULL, name VARCHAR(255) NOT NULL, url_video VARCHAR(255) NOT NULL, origen VARCHAR(255) DEFAULT NULL, identifier VARCHAR(255) DEFAULT NULL, INDEX IDX_7CC7DA2CE43E27B2 (type_video_id), UNIQUE INDEX UNIQ_7CC7DA2C579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE videopreguntas (id INT AUTO_INCREMENT NOT NULL, videoquiz_id INT DEFAULT NULL, currenttime INT NOT NULL, image VARCHAR(255) DEFAULT NULL, texto VARCHAR(255) DEFAULT NULL, respuestas LONGTEXT NOT NULL, INDEX IDX_BE3B3F894458963B (videoquiz_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE videoquiz (id INT AUTO_INCREMENT NOT NULL, chapter_id INT DEFAULT NULL, title VARCHAR(255) NOT NULL, url VARCHAR(255) NOT NULL, video_duration INT DEFAULT NULL, INDEX IDX_13E0266C579F4768 (chapter_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE zip_file_task (id INT AUTO_INCREMENT NOT NULL, files_manager_id INT DEFAULT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, deleted_by_id INT DEFAULT NULL, type VARCHAR(20) NOT NULL, entity_id VARCHAR(10) DEFAULT NULL, params JSON NOT NULL, task VARCHAR(255) NOT NULL, status SMALLINT NOT NULL, filename LONGTEXT DEFAULT NULL, original_name LONGTEXT DEFAULT NULL, started_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', finished_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, deleted_at DATETIME DEFAULT NULL, INDEX IDX_B192226A24F5AC00 (files_manager_id), INDEX IDX_B192226AB03A8386 (created_by_id), INDEX IDX_B192226A896DBBDE (updated_by_id), INDEX IDX_B192226AC76F1F52 (deleted_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE adivina_imagen ADD CONSTRAINT FK_EF1B569E579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE alert_type_tutor ADD CONSTRAINT FK_1604A96FB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE alert_type_tutor ADD CONSTRAINT FK_1604A96F896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE alert_type_tutor ADD CONSTRAINT FK_1604A96FC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE alert_type_tutor_translation ADD CONSTRAINT FK_EC131BA42C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES alert_type_tutor (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE announcement ADD CONSTRAINT FK_4DB9D91C591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE announcement ADD CONSTRAINT FK_4DB9D91CB8778369 FOREIGN KEY (subsidizer_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement ADD CONSTRAINT FK_4DB9D91CB4991890 FOREIGN KEY (type_diploma_id) REFERENCES type_diploma (id)');
        $this->addSql('ALTER TABLE announcement ADD CONSTRAINT FK_4DB9D91CB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement ADD CONSTRAINT FK_4DB9D91C896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement ADD CONSTRAINT FK_4DB9D91CC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BD5D628A82 FOREIGN KEY (alert_type_tutor_id) REFERENCES alert_type_tutor (id)');
        $this->addSql('ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BD5BC2EE02 FOREIGN KEY (announcement_tutor_id) REFERENCES announcement_tutor (id)');
        $this->addSql('ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BDB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BD896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_alert_tutor ADD CONSTRAINT FK_DDE31BDC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3E913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3EE7886A0B FOREIGN KEY (announcement_criteria_id) REFERENCES announcement_criteria (id)');
        $this->addSql('ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_aproved_criteria ADD CONSTRAINT FK_7324EB3EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67AB913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67AB73F32DD8 FOREIGN KEY (configuration_id) REFERENCES announcement_configuration_type (id)');
        $this->addSql('ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67ABB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67AB896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_configuration ADD CONSTRAINT FK_9C1B67ABC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_configuration_type ADD CONSTRAINT FK_BD7ACF885E9A4305 FOREIGN KEY (configuration_client_announcement_id) REFERENCES configuration_client_announcement (id)');
        $this->addSql('ALTER TABLE announcement_configuration_type ADD CONSTRAINT FK_BD7ACF88B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_configuration_type ADD CONSTRAINT FK_BD7ACF88896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_configuration_type ADD CONSTRAINT FK_BD7ACF88C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_configuration_type_translation ADD CONSTRAINT FK_E60E04A22C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES announcement_configuration_type (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE announcement_criteria ADD CONSTRAINT FK_CD521172B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_criteria ADD CONSTRAINT FK_CD521172896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_criteria ADD CONSTRAINT FK_CD521172C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_criteria_translation ADD CONSTRAINT FK_B9B4419B2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES announcement_criteria (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE announcement_didatic_guide ADD CONSTRAINT FK_BAC51D60913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_didatic_guide ADD CONSTRAINT FK_BAC51D60B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_didatic_guide ADD CONSTRAINT FK_BAC51D60896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_didatic_guide ADD CONSTRAINT FK_BAC51D60C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812770AF0AD FOREIGN KEY (sessions_announcement_id) REFERENCES sessions_announcement (id)');
        $this->addSql('ALTER TABLE announcement_group ADD CONSTRAINT FK_F31681272D614D0 FOREIGN KEY (type_money_id) REFERENCES type_money (id)');
        $this->addSql('ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_group ADD CONSTRAINT FK_F316812C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_group_session ADD CONSTRAINT FK_6FBD43A4851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id)');
        $this->addSql('ALTER TABLE announcement_group_session ADD CONSTRAINT FK_6FBD43A472D614D0 FOREIGN KEY (type_money_id) REFERENCES type_money (id)');
        $this->addSql('ALTER TABLE announcement_group_session ADD CONSTRAINT FK_6FBD43A42D6D889B FOREIGN KEY (modality_id) REFERENCES announcement_modality (id)');
        $this->addSql('ALTER TABLE announcement_group_session_assistance_files ADD CONSTRAINT FK_9362DE17D6B4E314 FOREIGN KEY (announcement_group_session_id) REFERENCES announcement_group_session (id)');
        $this->addSql('ALTER TABLE announcement_inspector_access ADD CONSTRAINT FK_628C140B913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_inspector_access ADD CONSTRAINT FK_628C140B41DEE7B9 FOREIGN KEY (token_id) REFERENCES user_token (id)');
        $this->addSql('ALTER TABLE announcement_modality_translation ADD CONSTRAINT FK_ECD0153C2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES announcement_modality (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE announcement_notification ADD CONSTRAINT FK_4525C829913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_notification ADD CONSTRAINT FK_4525C829B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_notification ADD CONSTRAINT FK_4525C829896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_notification ADD CONSTRAINT FK_4525C829C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9A19FA311B FOREIGN KEY (announcement_notification_id) REFERENCES announcement_notification (id)');
        $this->addSql('ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9A851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id)');
        $this->addSql('ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9AB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9A896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_notification_group ADD CONSTRAINT FK_FF834F9AC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_observation ADD CONSTRAINT FK_BBF55BFE913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_observation ADD CONSTRAINT FK_BBF55BFEB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_observation ADD CONSTRAINT FK_BBF55BFE896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_observation ADD CONSTRAINT FK_BBF55BFEC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_observation_document ADD CONSTRAINT FK_E69ACE464D83195D FOREIGN KEY (announcement_observation_id) REFERENCES announcement_observation (id)');
        $this->addSql('ALTER TABLE announcement_observation_document ADD CONSTRAINT FK_E69ACE46B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_observation_document ADD CONSTRAINT FK_E69ACE46896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_observation_document ADD CONSTRAINT FK_E69ACE46C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_temporalization ADD CONSTRAINT FK_CEC55850C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_tutor ADD CONSTRAINT FK_FBF66A9F913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_tutor ADD CONSTRAINT FK_FBF66A9F208F64F1 FOREIGN KEY (tutor_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_tutor ADD CONSTRAINT FK_FBF66A9F851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id)');
        $this->addSql('ALTER TABLE announcement_tutor ADD CONSTRAINT FK_FBF66A9F911DD53E FOREIGN KEY (cv_files_manager_id) REFERENCES files_manager (id)');
        $this->addSql('ALTER TABLE announcement_tutor ADD CONSTRAINT FK_FBF66A9F30FCDC3A FOREIGN KEY (user_company_id) REFERENCES user_company (id)');
        $this->addSql('ALTER TABLE announcement_tutor_connection ADD CONSTRAINT FK_817BA0395BC2EE02 FOREIGN KEY (announcement_tutor_id) REFERENCES announcement_tutor (id)');
        $this->addSql('ALTER TABLE announcement_tutor_connection ADD CONSTRAINT FK_817BA039B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_tutor_connection ADD CONSTRAINT FK_817BA039896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_tutor_connection ADD CONSTRAINT FK_817BA039C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE15913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE15A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE15851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id)');
        $this->addSql('ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE1530FCDC3A FOREIGN KEY (user_company_id) REFERENCES user_company (id)');
        $this->addSql('ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE15BC194B79 FOREIGN KEY (user_professional_category_id) REFERENCES user_professional_category (id)');
        $this->addSql('ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE1550BF847B FOREIGN KEY (user_work_center_id) REFERENCES user_work_center (id)');
        $this->addSql('ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE1542196A10 FOREIGN KEY (user_work_department_id) REFERENCES user_work_department (id)');
        $this->addSql('ALTER TABLE announcement_user ADD CONSTRAINT FK_A1A2DE1597BD8177 FOREIGN KEY (user_study_level_id) REFERENCES user_study_level (id)');
        $this->addSql('ALTER TABLE announcement_user_digital_signature ADD CONSTRAINT FK_41FE20DA5BCF8E5F FOREIGN KEY (announcement_user_id) REFERENCES announcement_user (id)');
        $this->addSql('ALTER TABLE announcement_user_digital_signature ADD CONSTRAINT FK_41FE20DAD6B4E314 FOREIGN KEY (announcement_group_session_id) REFERENCES announcement_group_session (id)');
        $this->addSql('ALTER TABLE answer ADD CONSTRAINT FK_DADD4A251E27F6BF FOREIGN KEY (question_id) REFERENCES question (id)');
        $this->addSql('ALTER TABLE answer ADD CONSTRAINT FK_DADD4A25B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE answer ADD CONSTRAINT FK_DADD4A25896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE answer ADD CONSTRAINT FK_DADD4A25C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE answers_video_quiz ADD CONSTRAINT FK_1CFB516C1E27F6BF FOREIGN KEY (question_id) REFERENCES videopreguntas (id)');
        $this->addSql('ALTER TABLE api_key_request ADD CONSTRAINT FK_3336212FAFCF7D25 FOREIGN KEY (api_key_user_id) REFERENCES api_key_user (id)');
        $this->addSql('ALTER TABLE catalog_translation ADD CONSTRAINT FK_A5DD64C12C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES catalog (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE categorize ADD CONSTRAINT FK_37DFDA7579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE categorize ADD CONSTRAINT FK_37DFDA7B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE categorize ADD CONSTRAINT FK_37DFDA7896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE categorize ADD CONSTRAINT FK_37DFDA7C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE categorize_answers ADD CONSTRAINT FK_71A5F63835D01CA7 FOREIGN KEY (categorize_id) REFERENCES categorize (id)');
        $this->addSql('ALTER TABLE categorize_answers ADD CONSTRAINT FK_71A5F6383ADB05F1 FOREIGN KEY (options_id) REFERENCES categorize_options (id)');
        $this->addSql('ALTER TABLE categorize_options ADD CONSTRAINT FK_F140CAB9579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE categorize_options ADD CONSTRAINT FK_F140CAB9B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE categorize_options ADD CONSTRAINT FK_F140CAB9896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE categorize_options ADD CONSTRAINT FK_F140CAB9C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge ADD CONSTRAINT FK_D7098951913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE challenge ADD CONSTRAINT FK_D70989519CAA2B25 FOREIGN KEY (translation_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE challenge ADD CONSTRAINT FK_D7098951B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge ADD CONSTRAINT FK_D7098951896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge ADD CONSTRAINT FK_D7098951C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_answers ADD CONSTRAINT FK_BC40898331A5801E FOREIGN KEY (pregunta_id) REFERENCES challenge_questions (id)');
        $this->addSql('ALTER TABLE challenge_answers ADD CONSTRAINT FK_BC408983B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_answers ADD CONSTRAINT FK_BC408983896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_answers ADD CONSTRAINT FK_BC408983C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_answers_translation ADD CONSTRAINT FK_C1CB75B22C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES challenge_answers (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE challenge_duel ADD CONSTRAINT FK_921BA0E498A21AC6 FOREIGN KEY (challenge_id) REFERENCES challenge (id)');
        $this->addSql('ALTER TABLE challenge_duel ADD CONSTRAINT FK_921BA0E456AE248B FOREIGN KEY (user1_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_duel ADD CONSTRAINT FK_921BA0E4441B8B65 FOREIGN KEY (user2_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_duel ADD CONSTRAINT FK_921BA0E4AD76C09 FOREIGN KEY (assigned_bot_id) REFERENCES bots (id)');
        $this->addSql('ALTER TABLE challenge_duel ADD CONSTRAINT FK_921BA0E4B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_duel ADD CONSTRAINT FK_921BA0E4896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_duel ADD CONSTRAINT FK_921BA0E4C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_duel_questions ADD CONSTRAINT FK_446B904758875E FOREIGN KEY (duel_id) REFERENCES challenge_duel (id)');
        $this->addSql('ALTER TABLE challenge_duel_questions ADD CONSTRAINT FK_446B90471E27F6BF FOREIGN KEY (question_id) REFERENCES challenge_questions (id)');
        $this->addSql('ALTER TABLE challenge_duel_questions ADD CONSTRAINT FK_446B904771E425EE FOREIGN KEY (answer_user1_id) REFERENCES challenge_answers (id)');
        $this->addSql('ALTER TABLE challenge_duel_questions ADD CONSTRAINT FK_446B904763518A00 FOREIGN KEY (answer_user2_id) REFERENCES challenge_answers (id)');
        $this->addSql('ALTER TABLE challenge_duel_questions_adn ADD CONSTRAINT FK_5CCC6C92A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_duel_questions_adn ADD CONSTRAINT FK_5CCC6C9298A21AC6 FOREIGN KEY (challenge_id) REFERENCES challenge (id)');
        $this->addSql('ALTER TABLE challenge_questions ADD CONSTRAINT FK_2A4B884A92A6DBB9 FOREIGN KEY (desafio_id) REFERENCES challenge (id)');
        $this->addSql('ALTER TABLE challenge_questions ADD CONSTRAINT FK_2A4B884AB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_questions ADD CONSTRAINT FK_2A4B884A896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_questions ADD CONSTRAINT FK_2A4B884AC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_questions_translation ADD CONSTRAINT FK_8C2788B02C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES challenge_questions (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE challenge_user ADD CONSTRAINT FK_843CD1CF98A21AC6 FOREIGN KEY (challenge_id) REFERENCES challenge (id)');
        $this->addSql('ALTER TABLE challenge_user ADD CONSTRAINT FK_843CD1CFA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_user ADD CONSTRAINT FK_843CD1CFB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_user ADD CONSTRAINT FK_843CD1CF896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_user ADD CONSTRAINT FK_843CD1CFC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE challenge_user_points ADD CONSTRAINT FK_3B543DA998A21AC6 FOREIGN KEY (challenge_id) REFERENCES challenge (id)');
        $this->addSql('ALTER TABLE challenge_user_points ADD CONSTRAINT FK_3B543DA9A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chapter ADD CONSTRAINT FK_F981B52E591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE chapter ADD CONSTRAINT FK_F981B52EC54C8C93 FOREIGN KEY (type_id) REFERENCES chapter_type (id)');
        $this->addSql('ALTER TABLE chapter ADD CONSTRAINT FK_F981B52E4EC001D1 FOREIGN KEY (season_id) REFERENCES season (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE chapter ADD CONSTRAINT FK_F981B52E7631067 FOREIGN KEY (vcms_project_id) REFERENCES vcms_project (id)');
        $this->addSql('ALTER TABLE chapter ADD CONSTRAINT FK_F981B52EB6704F40 FOREIGN KEY (roleplay_project_id) REFERENCES roleplay_project (id)');
        $this->addSql('ALTER TABLE chapter ADD CONSTRAINT FK_F981B52EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chapter ADD CONSTRAINT FK_F981B52E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chapter ADD CONSTRAINT FK_F981B52EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chapter_type_translation ADD CONSTRAINT FK_DEBCD0932C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES chapter_type (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422E1844E6B7 FOREIGN KEY (server_id) REFERENCES chat_server (id)');
        $this->addSql('ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422E727ACA70 FOREIGN KEY (parent_id) REFERENCES chat_channel (id)');
        $this->addSql('ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chat_channel ADD CONSTRAINT FK_EEF7422EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chat_channel_user ADD CONSTRAINT FK_EF2C1CB672F5A1AA FOREIGN KEY (channel_id) REFERENCES chat_channel (id)');
        $this->addSql('ALTER TABLE chat_channel_user ADD CONSTRAINT FK_EF2C1CB6A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chat_message ADD CONSTRAINT FK_FAB3FC1672F5A1AA FOREIGN KEY (channel_id) REFERENCES chat_channel (id)');
        $this->addSql('ALTER TABLE chat_message ADD CONSTRAINT FK_FAB3FC16A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chat_message ADD CONSTRAINT FK_FAB3FC16FFDF7169 FOREIGN KEY (reply_to_id) REFERENCES chat_message (id)');
        $this->addSql('ALTER TABLE chat_message_like ADD CONSTRAINT FK_FB1DB2EBA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chat_message_like ADD CONSTRAINT FK_FB1DB2EB948B568F FOREIGN KEY (chat_message_id) REFERENCES chat_message (id)');
        $this->addSql('ALTER TABLE chat_message_report ADD CONSTRAINT FK_E5FE6496537A1329 FOREIGN KEY (message_id) REFERENCES chat_message (id)');
        $this->addSql('ALTER TABLE chat_message_report ADD CONSTRAINT FK_E5FE6496A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE classroom_virtual_result ADD CONSTRAINT FK_B6A56DE127076DC2 FOREIGN KEY (classroom_virtual_id) REFERENCES classroomvirtual (id)');
        $this->addSql('ALTER TABLE classroomvirtual ADD CONSTRAINT FK_5D831E2F76BD65F0 FOREIGN KEY (announcementgroup_id) REFERENCES announcement_group (id)');
        $this->addSql('ALTER TABLE classroomvirtual ADD CONSTRAINT FK_5D831E2FFE14978B FOREIGN KEY (classroomvirtual_type_id) REFERENCES classroomvirtual_type (id)');
        $this->addSql('ALTER TABLE classroomvirtual ADD CONSTRAINT FK_5D831E2FB6F28D6D FOREIGN KEY (group_session_id) REFERENCES announcement_group_session (id)');
        $this->addSql('ALTER TABLE classroomvirtual_user ADD CONSTRAINT FK_CF87B8EB4688824D FOREIGN KEY (announcementuser_id) REFERENCES announcement_user (id)');
        $this->addSql('ALTER TABLE classroomvirtual_user ADD CONSTRAINT FK_CF87B8EBA866D846 FOREIGN KEY (announcementtutor_id) REFERENCES announcement_tutor (id)');
        $this->addSql('ALTER TABLE classroomvirtual_user ADD CONSTRAINT FK_CF87B8EBF5A029F5 FOREIGN KEY (classroomvirtual_id) REFERENCES classroomvirtual (id)');
        $this->addSql('ALTER TABLE comment_task ADD CONSTRAINT FK_744879C95445F77F FOREIGN KEY (history_delivery_task_id) REFERENCES history_delivery_task (id)');
        $this->addSql('ALTER TABLE comment_task ADD CONSTRAINT FK_744879C9727ACA70 FOREIGN KEY (parent_id) REFERENCES comment_task (id)');
        $this->addSql('ALTER TABLE comment_task ADD CONSTRAINT FK_744879C9B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE comment_task ADD CONSTRAINT FK_744879C9896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE comment_task ADD CONSTRAINT FK_744879C9C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE configuration_client_announcement ADD CONSTRAINT FK_71AECC53B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE configuration_client_announcement ADD CONSTRAINT FK_71AECC53896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE configuration_client_announcement ADD CONSTRAINT FK_71AECC53C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE content ADD CONSTRAINT FK_FEC530A9579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE content ADD CONSTRAINT FK_FEC530A9B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE content ADD CONSTRAINT FK_FEC530A9896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE content ADD CONSTRAINT FK_FEC530A9C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course ADD CONSTRAINT FK_169E6FB99CAA2B25 FOREIGN KEY (translation_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE course ADD CONSTRAINT FK_169E6FB912469DE2 FOREIGN KEY (category_id) REFERENCES course_category (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE course ADD CONSTRAINT FK_169E6FB95FB14BA7 FOREIGN KEY (level_id) REFERENCES course_level (id)');
        $this->addSql('ALTER TABLE course ADD CONSTRAINT FK_169E6FB9EDDA8882 FOREIGN KEY (type_course_id) REFERENCES type_course (id)');
        $this->addSql('ALTER TABLE course ADD CONSTRAINT FK_169E6FB9B4991890 FOREIGN KEY (type_diploma_id) REFERENCES type_diploma (id)');
        $this->addSql('ALTER TABLE course ADD CONSTRAINT FK_169E6FB9B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course ADD CONSTRAINT FK_169E6FB9896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course ADD CONSTRAINT FK_169E6FB9C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_professional_category ADD CONSTRAINT FK_30476A2C591CC992 FOREIGN KEY (course_id) REFERENCES course (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_professional_category ADD CONSTRAINT FK_30476A2CA0CCFBB2 FOREIGN KEY (professional_category_id) REFERENCES professional_category (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_manager ADD CONSTRAINT FK_F2E72055591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE course_manager ADD CONSTRAINT FK_F2E72055A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_tag ADD CONSTRAINT FK_760531B1591CC992 FOREIGN KEY (course_id) REFERENCES course (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_tag ADD CONSTRAINT FK_760531B1BAD26311 FOREIGN KEY (tag_id) REFERENCES tag (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_filter ADD CONSTRAINT FK_D1FC9E50591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE course_filter ADD CONSTRAINT FK_D1FC9E50D395B25E FOREIGN KEY (filter_id) REFERENCES filter (id)');
        $this->addSql('ALTER TABLE course_category ADD CONSTRAINT FK_AFF87497727ACA70 FOREIGN KEY (parent_id) REFERENCES course_category (id)');
        $this->addSql('ALTER TABLE course_category_type_course ADD CONSTRAINT FK_5CE7DCD6628AD36 FOREIGN KEY (course_category_id) REFERENCES course_category (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_category_type_course ADD CONSTRAINT FK_5CE7DCDEDDA8882 FOREIGN KEY (type_course_id) REFERENCES type_course (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_category_translation ADD CONSTRAINT FK_84EE3C232C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES course_category (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_level ADD CONSTRAINT FK_7341CBFBB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_level ADD CONSTRAINT FK_7341CBFB896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_level ADD CONSTRAINT FK_7341CBFBC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_section ADD CONSTRAINT FK_25B07F03B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_section ADD CONSTRAINT FK_25B07F03896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_section ADD CONSTRAINT FK_25B07F03C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_section_course_category ADD CONSTRAINT FK_2FCB6D087C1ADF9 FOREIGN KEY (course_section_id) REFERENCES course_section (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_section_course_category ADD CONSTRAINT FK_2FCB6D086628AD36 FOREIGN KEY (course_category_id) REFERENCES course_category (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_section_translation ADD CONSTRAINT FK_B600CF812C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES course_section (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_segment ADD CONSTRAINT FK_1042F089ED988ED FOREIGN KEY (course_segment_category_id) REFERENCES course_segment_category (id)');
        $this->addSql('ALTER TABLE course_segment ADD CONSTRAINT FK_1042F089B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_segment ADD CONSTRAINT FK_1042F089896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_segment ADD CONSTRAINT FK_1042F089C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE course_segment_course ADD CONSTRAINT FK_959B62B44CB242E FOREIGN KEY (course_segment_id) REFERENCES course_segment (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_segment_course ADD CONSTRAINT FK_959B62B4591CC992 FOREIGN KEY (course_id) REFERENCES course (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_segment_category_translation ADD CONSTRAINT FK_858433AF2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES course_segment_category (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_segment_translation ADD CONSTRAINT FK_A8F650602C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES course_segment (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_stat ADD CONSTRAINT FK_E81A2227591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE cron_report ADD CONSTRAINT FK_B6C6A7F5BE04EA9 FOREIGN KEY (job_id) REFERENCES cron_job (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE documentation ADD CONSTRAINT FK_73D5A93BB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE documentation ADD CONSTRAINT FK_73D5A93B896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE documentation ADD CONSTRAINT FK_73D5A93BC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE email_notification ADD CONSTRAINT FK_EA479099A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE email_notification ADD CONSTRAINT FK_EA47909915F737B2 FOREIGN KEY (itinerary_id) REFERENCES itinerary (id)');
        $this->addSql('ALTER TABLE email_notification ADD CONSTRAINT FK_EA479099913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE email_notification ADD CONSTRAINT FK_EA479099BA454E5D FOREIGN KEY (forum_post_id) REFERENCES forum_post (id)');
        $this->addSql('ALTER TABLE email_recipient ADD CONSTRAINT FK_670F6462A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE email_recipient ADD CONSTRAINT FK_670F64625DA0FB8 FOREIGN KEY (template_id) REFERENCES email_template (id)');
        $this->addSql('ALTER TABLE email_template ADD CONSTRAINT FK_9C0600CAB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE email_template ADD CONSTRAINT FK_9C0600CA896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE email_template ADD CONSTRAINT FK_9C0600CAC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE export ADD CONSTRAINT FK_428C16948DB60186 FOREIGN KEY (task_id) REFERENCES task (id)');
        $this->addSql('ALTER TABLE files_history_task ADD CONSTRAINT FK_E94FD4805445F77F FOREIGN KEY (history_delivery_task_id) REFERENCES history_delivery_task (id)');
        $this->addSql('ALTER TABLE files_history_task ADD CONSTRAINT FK_E94FD480B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE files_history_task ADD CONSTRAINT FK_E94FD480896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE files_history_task ADD CONSTRAINT FK_E94FD480C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE files_manager ADD CONSTRAINT FK_5BF93AEE74034854 FOREIGN KEY (files_manager_extra_id) REFERENCES files_manager_extra (id)');
        $this->addSql('ALTER TABLE files_manager ADD CONSTRAINT FK_5BF93AEEB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE files_manager ADD CONSTRAINT FK_5BF93AEE896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE files_manager ADD CONSTRAINT FK_5BF93AEEC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE files_task ADD CONSTRAINT FK_D5B6DE44263604C3 FOREIGN KEY (task_course_id) REFERENCES task_course (id)');
        $this->addSql('ALTER TABLE files_task ADD CONSTRAINT FK_D5B6DE44B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE files_task ADD CONSTRAINT FK_D5B6DE44896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE files_task ADD CONSTRAINT FK_D5B6DE44C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE fillgaps ADD CONSTRAINT FK_47AB1DD3579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE filter ADD CONSTRAINT FK_7FC45F1D6B444833 FOREIGN KEY (filter_category_id) REFERENCES filter_category (id)');
        $this->addSql('ALTER TABLE filter ADD CONSTRAINT FK_7FC45F1D727ACA70 FOREIGN KEY (parent_id) REFERENCES filter (id)');
        $this->addSql('ALTER TABLE filter_category ADD CONSTRAINT FK_3B231C61727ACA70 FOREIGN KEY (parent_id) REFERENCES filter_category (id)');
        $this->addSql('ALTER TABLE filter_category_translation ADD CONSTRAINT FK_F5C167262C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES filter_category (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE filter_translation ADD CONSTRAINT FK_CD6B90BE2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES filter (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE forum_likes ADD CONSTRAINT FK_51BEE8AABA454E5D FOREIGN KEY (forum_post_id) REFERENCES forum_post (id)');
        $this->addSql('ALTER TABLE forum_likes ADD CONSTRAINT FK_51BEE8AAA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE forum_post ADD CONSTRAINT FK_996BCC5AA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE forum_post ADD CONSTRAINT FK_996BCC5A727ACA70 FOREIGN KEY (parent_id) REFERENCES forum_post (id)');
        $this->addSql('ALTER TABLE forum_post ADD CONSTRAINT FK_996BCC5A591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE forum_post ADD CONSTRAINT FK_996BCC5A913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE forum_post ADD CONSTRAINT FK_996BCC5AFBF32840 FOREIGN KEY (response_id) REFERENCES forum_post (id)');
        $this->addSql('ALTER TABLE forum_post ADD CONSTRAINT FK_996BCC5AB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE forum_post ADD CONSTRAINT FK_996BCC5A896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE forum_post ADD CONSTRAINT FK_996BCC5AC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE forum_report ADD CONSTRAINT FK_DC804455BA454E5D FOREIGN KEY (forum_post_id) REFERENCES forum_post (id)');
        $this->addSql('ALTER TABLE forum_report ADD CONSTRAINT FK_DC804455A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE gamesword ADD CONSTRAINT FK_2CD5DB38579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE generic_token ADD CONSTRAINT FK_360B1929B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE generic_token ADD CONSTRAINT FK_360B1929896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE generic_token ADD CONSTRAINT FK_360B1929C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE guessword ADD CONSTRAINT FK_32444B08579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE help_category_translation ADD CONSTRAINT FK_ED35F7482C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES help_category (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE help_text ADD CONSTRAINT FK_7E01925C12469DE2 FOREIGN KEY (category_id) REFERENCES help_category (id)');
        $this->addSql('ALTER TABLE help_text_translation ADD CONSTRAINT FK_857DDBF02C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES help_text (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE higher_lower ADD CONSTRAINT FK_7C0E1150579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE higher_lower ADD CONSTRAINT FK_7C0E1150B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE higher_lower ADD CONSTRAINT FK_7C0E1150896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE higher_lower ADD CONSTRAINT FK_7C0E1150C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE higher_lower_words ADD CONSTRAINT FK_177FA2D2BE9CE5EB FOREIGN KEY (higher_lower_id) REFERENCES higher_lower (id)');
        $this->addSql('ALTER TABLE history_delivery_task ADD CONSTRAINT FK_A9B9C8D8B88FF97F FOREIGN KEY (task_user_id) REFERENCES task_user (id)');
        $this->addSql('ALTER TABLE history_delivery_task ADD CONSTRAINT FK_A9B9C8D8B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE history_delivery_task ADD CONSTRAINT FK_A9B9C8D8896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE history_delivery_task ADD CONSTRAINT FK_A9B9C8D8C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE history_seen_material ADD CONSTRAINT FK_C83ABC55591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE history_seen_material ADD CONSTRAINT FK_C83ABC55A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE history_seen_material ADD CONSTRAINT FK_C83ABC556182D372 FOREIGN KEY (material_course_id) REFERENCES material_course (id)');
        $this->addSql('ALTER TABLE holes ADD CONSTRAINT FK_9C6EA1E4477D657A FOREIGN KEY (fillgap_id) REFERENCES fillgaps (id)');
        $this->addSql('ALTER TABLE integration_mapping ADD CONSTRAINT FK_24CA96D3EC8E1CAD FOREIGN KEY (integration_group_id) REFERENCES integration_group (id)');
        $this->addSql('ALTER TABLE itinerary ADD CONSTRAINT FK_FF2238F6B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE itinerary ADD CONSTRAINT FK_FF2238F6896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE itinerary ADD CONSTRAINT FK_FF2238F6C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE itinerary_filter ADD CONSTRAINT FK_C74482815F737B2 FOREIGN KEY (itinerary_id) REFERENCES itinerary (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE itinerary_filter ADD CONSTRAINT FK_C744828D395B25E FOREIGN KEY (filter_id) REFERENCES filter (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE itinerary_course ADD CONSTRAINT FK_652E788C15F737B2 FOREIGN KEY (itinerary_id) REFERENCES itinerary (id)');
        $this->addSql('ALTER TABLE itinerary_course ADD CONSTRAINT FK_652E788C591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE itinerary_manager ADD CONSTRAINT FK_ACE4518D15F737B2 FOREIGN KEY (itinerary_id) REFERENCES itinerary (id)');
        $this->addSql('ALTER TABLE itinerary_manager ADD CONSTRAINT FK_ACE4518DA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE itinerary_tags ADD CONSTRAINT FK_689FFE72D395B25E FOREIGN KEY (filter_id) REFERENCES filter (id)');
        $this->addSql('ALTER TABLE itinerary_tags ADD CONSTRAINT FK_689FFE7215F737B2 FOREIGN KEY (itinerary_id) REFERENCES itinerary (id)');
        $this->addSql('ALTER TABLE itinerary_translation ADD CONSTRAINT FK_D12AC99A2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES itinerary (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE itinerary_user ADD CONSTRAINT FK_8AB0BC1D15F737B2 FOREIGN KEY (itinerary_id) REFERENCES itinerary (id)');
        $this->addSql('ALTER TABLE itinerary_user ADD CONSTRAINT FK_8AB0BC1DA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library ADD CONSTRAINT FK_A18098BC12469DE2 FOREIGN KEY (category_id) REFERENCES library_category (id)');
        $this->addSql('ALTER TABLE library ADD CONSTRAINT FK_A18098BCB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library ADD CONSTRAINT FK_A18098BC896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library ADD CONSTRAINT FK_A18098BCC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_filter ADD CONSTRAINT FK_D426C796FE2541D7 FOREIGN KEY (library_id) REFERENCES library (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE library_filter ADD CONSTRAINT FK_D426C796D395B25E FOREIGN KEY (filter_id) REFERENCES filter (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE library_category ADD CONSTRAINT FK_20EFE8D5B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_category ADD CONSTRAINT FK_20EFE8D5896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_category ADD CONSTRAINT FK_20EFE8D5C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_category_translation ADD CONSTRAINT FK_F72523D02C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES library_category (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE library_comment ADD CONSTRAINT FK_EEB5EA5CFE2541D7 FOREIGN KEY (library_id) REFERENCES library (id)');
        $this->addSql('ALTER TABLE library_comment ADD CONSTRAINT FK_EEB5EA5C69463ADC FOREIGN KEY (allowed_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_comment ADD CONSTRAINT FK_EEB5EA5C386B8E7 FOREIGN KEY (banned_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_comment ADD CONSTRAINT FK_EEB5EA5CB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_comment ADD CONSTRAINT FK_EEB5EA5C896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_comment ADD CONSTRAINT FK_EEB5EA5CC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_file ADD CONSTRAINT FK_2A50FC7DFE2541D7 FOREIGN KEY (library_id) REFERENCES library (id)');
        $this->addSql('ALTER TABLE library_video ADD CONSTRAINT FK_4F620803FE2541D7 FOREIGN KEY (library_id) REFERENCES library (id)');
        $this->addSql('ALTER TABLE library_views ADD CONSTRAINT FK_22554EA8A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE library_views ADD CONSTRAINT FK_22554EA8FE2541D7 FOREIGN KEY (library_id) REFERENCES library (id)');
        $this->addSql('ALTER TABLE main_course_evaluation ADD CONSTRAINT FK_ABF7398B591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE material_course ADD CONSTRAINT FK_87ACE482591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE material_course ADD CONSTRAINT FK_87ACE482913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE material_course ADD CONSTRAINT FK_87ACE482B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE material_course ADD CONSTRAINT FK_87ACE482896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE material_course ADD CONSTRAINT FK_87ACE482C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE material_download_history ADD CONSTRAINT FK_7180BF5B591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE material_download_history ADD CONSTRAINT FK_7180BF5BA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE material_download_history ADD CONSTRAINT FK_7180BF5BE308AC6F FOREIGN KEY (material_id) REFERENCES material_course (id)');
        $this->addSql('ALTER TABLE material_download_history ADD CONSTRAINT FK_7180BF5BB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE material_download_history ADD CONSTRAINT FK_7180BF5B896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE material_download_history ADD CONSTRAINT FK_7180BF5BC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307FF624B39D FOREIGN KEY (sender_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307FE92F8F78 FOREIGN KEY (recipient_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE message ADD CONSTRAINT FK_B6BD307FFFDF7169 FOREIGN KEY (reply_to_id) REFERENCES message (id)');
        $this->addSql('ALTER TABLE message_attachment ADD CONSTRAINT FK_B68FF524537A1329 FOREIGN KEY (message_id) REFERENCES message (id)');
        $this->addSql('ALTER TABLE news ADD CONSTRAINT FK_1DD39950B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE news ADD CONSTRAINT FK_1DD39950896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE news ADD CONSTRAINT FK_1DD39950C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE news_course_section ADD CONSTRAINT FK_BA5A78CAB5A459A0 FOREIGN KEY (news_id) REFERENCES news (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE news_course_section ADD CONSTRAINT FK_BA5A78CA7C1ADF9 FOREIGN KEY (course_section_id) REFERENCES course_section (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE news_translation ADD CONSTRAINT FK_9D5CF3202C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES news (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE notification ADD CONSTRAINT FK_BF5476CAA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps ADD CONSTRAINT FK_5B3B66481E27F6BF FOREIGN KEY (question_id) REFERENCES nps_question (id)');
        $this->addSql('ALTER TABLE nps ADD CONSTRAINT FK_5B3B6648591CC992 FOREIGN KEY (course_id) REFERENCES user_course (id)');
        $this->addSql('ALTER TABLE nps ADD CONSTRAINT FK_5B3B6648A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps ADD CONSTRAINT FK_5B3B6648913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE nps ADD CONSTRAINT FK_5B3B6648B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps ADD CONSTRAINT FK_5B3B6648896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps ADD CONSTRAINT FK_5B3B6648C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps_question ADD CONSTRAINT FK_27D40D91B3FE509D FOREIGN KEY (survey_id) REFERENCES survey (id)');
        $this->addSql('ALTER TABLE nps_question ADD CONSTRAINT FK_27D40D91B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps_question ADD CONSTRAINT FK_27D40D91896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps_question ADD CONSTRAINT FK_27D40D91C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps_question_course ADD CONSTRAINT FK_5B1CA872AE807F50 FOREIGN KEY (nps_question_id) REFERENCES nps_question (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE nps_question_course ADD CONSTRAINT FK_5B1CA872591CC992 FOREIGN KEY (course_id) REFERENCES course (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE nps_question_announcement ADD CONSTRAINT FK_52B16383AE807F50 FOREIGN KEY (nps_question_id) REFERENCES nps_question (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE nps_question_announcement ADD CONSTRAINT FK_52B16383913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE nps_question_detail ADD CONSTRAINT FK_6384B858AE807F50 FOREIGN KEY (nps_question_id) REFERENCES nps_question (id)');
        $this->addSql('ALTER TABLE nps_question_detail ADD CONSTRAINT FK_6384B858B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps_question_detail ADD CONSTRAINT FK_6384B858896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps_question_detail ADD CONSTRAINT FK_6384B858C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE nps_question_detail_translation ADD CONSTRAINT FK_BFEB50BF2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES nps_question_detail (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE nps_question_translation ADD CONSTRAINT FK_6650B4BC2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES nps_question (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE ordenar_menormayor ADD CONSTRAINT FK_C414560B579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE pages ADD CONSTRAINT FK_2074E575B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE pages ADD CONSTRAINT FK_2074E575896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE pages ADD CONSTRAINT FK_2074E575C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE pages_translation ADD CONSTRAINT FK_7D0CA9D12C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES pages (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE parejas ADD CONSTRAINT FK_CDEC53A2579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE parejas_imagen ADD CONSTRAINT FK_4907912B8497EC37 FOREIGN KEY (parejas_id) REFERENCES parejas (id)');
        $this->addSql('ALTER TABLE pdf ADD CONSTRAINT FK_EF0DB8C579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE ppt ADD CONSTRAINT FK_D3E77D91579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE professional_category ADD CONSTRAINT FK_95CAC37C727ACA70 FOREIGN KEY (parent_id) REFERENCES professional_category (id)');
        $this->addSql('ALTER TABLE puzzle ADD CONSTRAINT FK_22A6DFDF579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE question ADD CONSTRAINT FK_B6F7494E579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE question ADD CONSTRAINT FK_B6F7494EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE question ADD CONSTRAINT FK_B6F7494E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE question ADD CONSTRAINT FK_B6F7494EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE recovery_code ADD CONSTRAINT FK_2C8D0584A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE reset_password_request ADD CONSTRAINT FK_7CE748AA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE roleplay_beginning ADD CONSTRAINT FK_E89A902B166D1F9C FOREIGN KEY (project_id) REFERENCES roleplay_project (id)');
        $this->addSql('ALTER TABLE roleplay_ending ADD CONSTRAINT FK_3A4A6E5B166D1F9C FOREIGN KEY (project_id) REFERENCES roleplay_project (id)');
        $this->addSql('ALTER TABLE roleplay_scene ADD CONSTRAINT FK_34CE7AA798FB19AE FOREIGN KEY (sequence_id) REFERENCES roleplay_sequence (id)');
        $this->addSql('ALTER TABLE roleplay_sequence ADD CONSTRAINT FK_579C74B5166D1F9C FOREIGN KEY (project_id) REFERENCES roleplay_project (id)');
        $this->addSql('ALTER TABLE roulette_word ADD CONSTRAINT FK_EDB2A53C579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE scorm ADD CONSTRAINT FK_3C42BF63579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE season ADD CONSTRAINT FK_F0E45BA9591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE section_default_front_translation ADD CONSTRAINT FK_B2D0C7942C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES section_default_front (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE sessions_announcement ADD CONSTRAINT FK_6A027C4B913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE setting ADD CONSTRAINT FK_9F74B89850DDE1BD FOREIGN KEY (setting_group_id) REFERENCES setting_group (id)');
        $this->addSql('ALTER TABLE survey ADD CONSTRAINT FK_AD5F9BFCB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE survey ADD CONSTRAINT FK_AD5F9BFC896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE survey ADD CONSTRAINT FK_AD5F9BFCC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE survey_announcement ADD CONSTRAINT FK_3D6BD54CB3FE509D FOREIGN KEY (survey_id) REFERENCES survey (id)');
        $this->addSql('ALTER TABLE survey_announcement ADD CONSTRAINT FK_3D6BD54C913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE survey_course ADD CONSTRAINT FK_3E90A7D5B3FE509D FOREIGN KEY (survey_id) REFERENCES survey (id)');
        $this->addSql('ALTER TABLE survey_course ADD CONSTRAINT FK_3E90A7D5591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE survey_translation ADD CONSTRAINT FK_C919A6A2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES survey (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE task_course ADD CONSTRAINT FK_2D37EB6A591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE task_course ADD CONSTRAINT FK_2D37EB6A913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE task_course ADD CONSTRAINT FK_2D37EB6AB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE task_course ADD CONSTRAINT FK_2D37EB6A896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE task_course ADD CONSTRAINT FK_2D37EB6AC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700263604C3 FOREIGN KEY (task_course_id) REFERENCES task_course (id)');
        $this->addSql('ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700851953B4 FOREIGN KEY (announcement_group_id) REFERENCES announcement_group (id)');
        $this->addSql('ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE task_course_group ADD CONSTRAINT FK_4CFA6700C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE task_user ADD CONSTRAINT FK_FE204232A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE task_user ADD CONSTRAINT FK_FE2042328DB60186 FOREIGN KEY (task_id) REFERENCES task_course (id)');
        $this->addSql('ALTER TABLE task_user ADD CONSTRAINT FK_FE204232B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE task_user ADD CONSTRAINT FK_FE204232896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE task_user ADD CONSTRAINT FK_FE204232C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE time_game ADD CONSTRAINT FK_8E9638A1579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE true_or_false ADD CONSTRAINT FK_CA13A6C3579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE type_course ADD CONSTRAINT FK_35490481B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE type_course ADD CONSTRAINT FK_35490481896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE type_course ADD CONSTRAINT FK_35490481C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE type_course_alerts ADD CONSTRAINT FK_29AFB613EDDA8882 FOREIGN KEY (type_course_id) REFERENCES type_course (id)');
        $this->addSql('ALTER TABLE type_course_alerts ADD CONSTRAINT FK_29AFB6135D628A82 FOREIGN KEY (alert_type_tutor_id) REFERENCES alert_type_tutor (id)');
        $this->addSql('ALTER TABLE type_course_announcement_step_configuration ADD CONSTRAINT FK_82556DB275D5DC16 FOREIGN KEY (announcement_configuration_type_id) REFERENCES announcement_configuration_type (id)');
        $this->addSql('ALTER TABLE type_course_announcement_step_configuration ADD CONSTRAINT FK_82556DB26DFFE627 FOREIGN KEY (type_course_announcement_step_creation_id) REFERENCES type_course_announcement_step_creation (id)');
        $this->addSql('ALTER TABLE type_course_announcement_step_creation ADD CONSTRAINT FK_86190621EDDA8882 FOREIGN KEY (type_course_id) REFERENCES type_course (id)');
        $this->addSql('ALTER TABLE type_course_announcement_step_creation ADD CONSTRAINT FK_86190621556ED2C3 FOREIGN KEY (announcement_step_creation_id) REFERENCES announcement_step_creation (id)');
        $this->addSql('ALTER TABLE type_course_announcement_step_creation_trans ADD CONSTRAINT FK_7100AE352C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES type_course_announcement_step_creation (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE type_course_translation ADD CONSTRAINT FK_A7660E6B2C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES type_course (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE type_diploma ADD CONSTRAINT FK_C400E6A2B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE type_diploma ADD CONSTRAINT FK_C400E6A2896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE type_diploma ADD CONSTRAINT FK_C400E6A2C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE type_identification_translation ADD CONSTRAINT FK_75F23B762C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES type_identification (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE type_money_translation ADD CONSTRAINT FK_A87D08C72C2AC5D3 FOREIGN KEY (translatable_id) REFERENCES type_money (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user ADD CONSTRAINT FK_8D93D64946E746A6 FOREIGN KEY (team_manager_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user ADD CONSTRAINT FK_8D93D649B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user ADD CONSTRAINT FK_8D93D649896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user ADD CONSTRAINT FK_8D93D649C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_filter ADD CONSTRAINT FK_1A964420A76ED395 FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_filter ADD CONSTRAINT FK_1A964420D395B25E FOREIGN KEY (filter_id) REFERENCES filter (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE manager_filter ADD CONSTRAINT FK_B9E1596FA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE manager_filter ADD CONSTRAINT FK_B9E1596FD395B25E FOREIGN KEY (filter_id) REFERENCES filter (id)');
        $this->addSql('ALTER TABLE user_comments ADD CONSTRAINT FK_BF13722AA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_company ADD CONSTRAINT FK_17B21745B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_company ADD CONSTRAINT FK_17B21745896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_company ADD CONSTRAINT FK_17B21745C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_course ADD CONSTRAINT FK_73CC7484A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_course ADD CONSTRAINT FK_73CC7484591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE user_course ADD CONSTRAINT FK_73CC7484913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE user_course_chapter ADD CONSTRAINT FK_9AC76F9B59FC4476 FOREIGN KEY (user_course_id) REFERENCES user_course (id)');
        $this->addSql('ALTER TABLE user_course_chapter ADD CONSTRAINT FK_9AC76F9B579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE user_courses_total_time ADD CONSTRAINT FK_B7D6BB96A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_extra ADD CONSTRAINT FK_AFFDF63DA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_extra ADD CONSTRAINT FK_AFFDF63D12469DE2 FOREIGN KEY (category_id) REFERENCES professional_category (id)');
        $this->addSql('ALTER TABLE user_extra ADD CONSTRAINT FK_AFFDF63DAE80F5DF FOREIGN KEY (department_id) REFERENCES department (id)');
        $this->addSql('ALTER TABLE user_extra ADD CONSTRAINT FK_AFFDF63D5932F377 FOREIGN KEY (center_id) REFERENCES center (id)');
        $this->addSql('ALTER TABLE user_extra ADD CONSTRAINT FK_AFFDF63DB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_extra ADD CONSTRAINT FK_AFFDF63D896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_extra ADD CONSTRAINT FK_AFFDF63DC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CCA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC30FCDC3A FOREIGN KEY (user_company_id) REFERENCES user_company (id)');
        $this->addSql('ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CCBC194B79 FOREIGN KEY (user_professional_category_id) REFERENCES user_professional_category (id)');
        $this->addSql('ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC50BF847B FOREIGN KEY (user_work_center_id) REFERENCES user_work_center (id)');
        $this->addSql('ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC42196A10 FOREIGN KEY (user_work_department_id) REFERENCES user_work_department (id)');
        $this->addSql('ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC97BD8177 FOREIGN KEY (user_study_level_id) REFERENCES user_study_level (id)');
        $this->addSql('ALTER TABLE user_fields_fundae ADD CONSTRAINT FK_DC0416CC911DD53E FOREIGN KEY (cv_files_manager_id) REFERENCES files_manager (id)');
        $this->addSql('ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717E591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717E913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_history_download_diploma ADD CONSTRAINT FK_8F67717EC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_identification ADD CONSTRAINT FK_2262AD54A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_identification ADD CONSTRAINT FK_2262AD54F1CF261E FOREIGN KEY (type_identification_id) REFERENCES type_identification (id)');
        $this->addSql('ALTER TABLE user_login ADD CONSTRAINT FK_48CA3048A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_manage ADD CONSTRAINT FK_4120B177A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_notification ADD CONSTRAINT FK_3F980AC8A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_professional_category ADD CONSTRAINT FK_55CE8E02B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_professional_category ADD CONSTRAINT FK_55CE8E02896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_professional_category ADD CONSTRAINT FK_55CE8E02C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_roleplay_project ADD CONSTRAINT FK_48521FF4A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_roleplay_project ADD CONSTRAINT FK_48521FF47062B2C0 FOREIGN KEY (user_course_chapter_id) REFERENCES user_course_chapter (id)');
        $this->addSql('ALTER TABLE user_roleplay_project ADD CONSTRAINT FK_48521FF4166D1F9C FOREIGN KEY (project_id) REFERENCES roleplay_project (id)');
        $this->addSql('ALTER TABLE user_roleplay_project ADD CONSTRAINT FK_48521FF4B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_roleplay_project ADD CONSTRAINT FK_48521FF4896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_roleplay_project ADD CONSTRAINT FK_48521FF4C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_study_level ADD CONSTRAINT FK_6C8B5566B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_study_level ADD CONSTRAINT FK_6C8B5566896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_study_level ADD CONSTRAINT FK_6C8B5566C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_time ADD CONSTRAINT FK_1515D48CA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_time ADD CONSTRAINT FK_1515D48C591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE user_time ADD CONSTRAINT FK_1515D48C913AEA17 FOREIGN KEY (announcement_id) REFERENCES announcement (id)');
        $this->addSql('ALTER TABLE user_token ADD CONSTRAINT FK_BDF55A63A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_vcms_project ADD CONSTRAINT FK_2AB6F06AA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_vcms_project ADD CONSTRAINT FK_2AB6F06A166D1F9C FOREIGN KEY (project_id) REFERENCES vcms_project (id)');
        $this->addSql('ALTER TABLE user_vcms_project ADD CONSTRAINT FK_2AB6F06A7062B2C0 FOREIGN KEY (user_course_chapter_id) REFERENCES user_course_chapter (id)');
        $this->addSql('ALTER TABLE user_vcms_project ADD CONSTRAINT FK_2AB6F06AB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_vcms_project ADD CONSTRAINT FK_2AB6F06A896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_vcms_project ADD CONSTRAINT FK_2AB6F06AC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_work_center ADD CONSTRAINT FK_4212E493B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_work_center ADD CONSTRAINT FK_4212E493896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_work_center ADD CONSTRAINT FK_4212E493C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_work_department ADD CONSTRAINT FK_C4A220D6B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_work_department ADD CONSTRAINT FK_C4A220D6896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE user_work_department ADD CONSTRAINT FK_C4A220D6C76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE video ADD CONSTRAINT FK_7CC7DA2CE43E27B2 FOREIGN KEY (type_video_id) REFERENCES type_video (id)');
        $this->addSql('ALTER TABLE video ADD CONSTRAINT FK_7CC7DA2C579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE videopreguntas ADD CONSTRAINT FK_BE3B3F894458963B FOREIGN KEY (videoquiz_id) REFERENCES videoquiz (id)');
        $this->addSql('ALTER TABLE videoquiz ADD CONSTRAINT FK_13E0266C579F4768 FOREIGN KEY (chapter_id) REFERENCES chapter (id)');
        $this->addSql('ALTER TABLE zip_file_task ADD CONSTRAINT FK_B192226A24F5AC00 FOREIGN KEY (files_manager_id) REFERENCES files_manager (id)');
        $this->addSql('ALTER TABLE zip_file_task ADD CONSTRAINT FK_B192226AB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE zip_file_task ADD CONSTRAINT FK_B192226A896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE zip_file_task ADD CONSTRAINT FK_B192226AC76F1F52 FOREIGN KEY (deleted_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE chapter ADD is_active TINYINT(1) DEFAULT 1');
        $this->addSql('ALTER TABLE chapter CHANGE is_active is_active TINYINT(1) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE alert_type_tutor_translation DROP FOREIGN KEY FK_EC131BA42C2AC5D3');
        $this->addSql('ALTER TABLE announcement_alert_tutor DROP FOREIGN KEY FK_DDE31BD5D628A82');
        $this->addSql('ALTER TABLE type_course_alerts DROP FOREIGN KEY FK_29AFB6135D628A82');
        $this->addSql('ALTER TABLE announcement_aproved_criteria DROP FOREIGN KEY FK_7324EB3E913AEA17');
        $this->addSql('ALTER TABLE announcement_configuration DROP FOREIGN KEY FK_9C1B67AB913AEA17');
        $this->addSql('ALTER TABLE announcement_didatic_guide DROP FOREIGN KEY FK_BAC51D60913AEA17');
        $this->addSql('ALTER TABLE announcement_group DROP FOREIGN KEY FK_F316812913AEA17');
        $this->addSql('ALTER TABLE announcement_inspector_access DROP FOREIGN KEY FK_628C140B913AEA17');
        $this->addSql('ALTER TABLE announcement_notification DROP FOREIGN KEY FK_4525C829913AEA17');
        $this->addSql('ALTER TABLE announcement_observation DROP FOREIGN KEY FK_BBF55BFE913AEA17');
        $this->addSql('ALTER TABLE announcement_temporalization DROP FOREIGN KEY FK_CEC55850913AEA17');
        $this->addSql('ALTER TABLE announcement_tutor DROP FOREIGN KEY FK_FBF66A9F913AEA17');
        $this->addSql('ALTER TABLE announcement_user DROP FOREIGN KEY FK_A1A2DE15913AEA17');
        $this->addSql('ALTER TABLE challenge DROP FOREIGN KEY FK_D7098951913AEA17');
        $this->addSql('ALTER TABLE email_notification DROP FOREIGN KEY FK_EA479099913AEA17');
        $this->addSql('ALTER TABLE forum_post DROP FOREIGN KEY FK_996BCC5A913AEA17');
        $this->addSql('ALTER TABLE material_course DROP FOREIGN KEY FK_87ACE482913AEA17');
        $this->addSql('ALTER TABLE nps DROP FOREIGN KEY FK_5B3B6648913AEA17');
        $this->addSql('ALTER TABLE nps_question_announcement DROP FOREIGN KEY FK_52B16383913AEA17');
        $this->addSql('ALTER TABLE sessions_announcement DROP FOREIGN KEY FK_6A027C4B913AEA17');
        $this->addSql('ALTER TABLE survey_announcement DROP FOREIGN KEY FK_3D6BD54C913AEA17');
        $this->addSql('ALTER TABLE task_course DROP FOREIGN KEY FK_2D37EB6A913AEA17');
        $this->addSql('ALTER TABLE user_course DROP FOREIGN KEY FK_73CC7484913AEA17');
        $this->addSql('ALTER TABLE user_history_download_diploma DROP FOREIGN KEY FK_8F67717E913AEA17');
        $this->addSql('ALTER TABLE user_time DROP FOREIGN KEY FK_1515D48C913AEA17');
        $this->addSql('ALTER TABLE announcement_configuration DROP FOREIGN KEY FK_9C1B67AB73F32DD8');
        $this->addSql('ALTER TABLE announcement_configuration_type_translation DROP FOREIGN KEY FK_E60E04A22C2AC5D3');
        $this->addSql('ALTER TABLE type_course_announcement_step_configuration DROP FOREIGN KEY FK_82556DB275D5DC16');
        $this->addSql('ALTER TABLE announcement_aproved_criteria DROP FOREIGN KEY FK_7324EB3EE7886A0B');
        $this->addSql('ALTER TABLE announcement_criteria_translation DROP FOREIGN KEY FK_B9B4419B2C2AC5D3');
        $this->addSql('ALTER TABLE announcement_group_session DROP FOREIGN KEY FK_6FBD43A4851953B4');
        $this->addSql('ALTER TABLE announcement_notification_group DROP FOREIGN KEY FK_FF834F9A851953B4');
        $this->addSql('ALTER TABLE announcement_tutor DROP FOREIGN KEY FK_FBF66A9F851953B4');
        $this->addSql('ALTER TABLE announcement_user DROP FOREIGN KEY FK_A1A2DE15851953B4');
        $this->addSql('ALTER TABLE classroomvirtual DROP FOREIGN KEY FK_5D831E2F76BD65F0');
        $this->addSql('ALTER TABLE task_course_group DROP FOREIGN KEY FK_4CFA6700851953B4');
        $this->addSql('ALTER TABLE announcement_group_session_assistance_files DROP FOREIGN KEY FK_9362DE17D6B4E314');
        $this->addSql('ALTER TABLE announcement_user_digital_signature DROP FOREIGN KEY FK_41FE20DAD6B4E314');
        $this->addSql('ALTER TABLE classroomvirtual DROP FOREIGN KEY FK_5D831E2FB6F28D6D');
        $this->addSql('ALTER TABLE announcement_group_session DROP FOREIGN KEY FK_6FBD43A42D6D889B');
        $this->addSql('ALTER TABLE announcement_modality_translation DROP FOREIGN KEY FK_ECD0153C2C2AC5D3');
        $this->addSql('ALTER TABLE announcement_notification_group DROP FOREIGN KEY FK_FF834F9A19FA311B');
        $this->addSql('ALTER TABLE announcement_observation_document DROP FOREIGN KEY FK_E69ACE464D83195D');
        $this->addSql('ALTER TABLE type_course_announcement_step_creation DROP FOREIGN KEY FK_86190621556ED2C3');
        $this->addSql('ALTER TABLE announcement_alert_tutor DROP FOREIGN KEY FK_DDE31BD5BC2EE02');
        $this->addSql('ALTER TABLE announcement_tutor_connection DROP FOREIGN KEY FK_817BA0395BC2EE02');
        $this->addSql('ALTER TABLE classroomvirtual_user DROP FOREIGN KEY FK_CF87B8EBA866D846');
        $this->addSql('ALTER TABLE announcement_user_digital_signature DROP FOREIGN KEY FK_41FE20DA5BCF8E5F');
        $this->addSql('ALTER TABLE classroomvirtual_user DROP FOREIGN KEY FK_CF87B8EB4688824D');
        $this->addSql('ALTER TABLE api_key_request DROP FOREIGN KEY FK_3336212FAFCF7D25');
        $this->addSql('ALTER TABLE challenge_duel DROP FOREIGN KEY FK_921BA0E4AD76C09');
        $this->addSql('ALTER TABLE catalog_translation DROP FOREIGN KEY FK_A5DD64C12C2AC5D3');
        $this->addSql('ALTER TABLE categorize_answers DROP FOREIGN KEY FK_71A5F63835D01CA7');
        $this->addSql('ALTER TABLE categorize_answers DROP FOREIGN KEY FK_71A5F6383ADB05F1');
        $this->addSql('ALTER TABLE user_extra DROP FOREIGN KEY FK_AFFDF63D5932F377');
        $this->addSql('ALTER TABLE challenge_duel DROP FOREIGN KEY FK_921BA0E498A21AC6');
        $this->addSql('ALTER TABLE challenge_duel_questions_adn DROP FOREIGN KEY FK_5CCC6C9298A21AC6');
        $this->addSql('ALTER TABLE challenge_questions DROP FOREIGN KEY FK_2A4B884A92A6DBB9');
        $this->addSql('ALTER TABLE challenge_user DROP FOREIGN KEY FK_843CD1CF98A21AC6');
        $this->addSql('ALTER TABLE challenge_user_points DROP FOREIGN KEY FK_3B543DA998A21AC6');
        $this->addSql('ALTER TABLE challenge_answers_translation DROP FOREIGN KEY FK_C1CB75B22C2AC5D3');
        $this->addSql('ALTER TABLE challenge_duel_questions DROP FOREIGN KEY FK_446B904771E425EE');
        $this->addSql('ALTER TABLE challenge_duel_questions DROP FOREIGN KEY FK_446B904763518A00');
        $this->addSql('ALTER TABLE challenge_duel_questions DROP FOREIGN KEY FK_446B904758875E');
        $this->addSql('ALTER TABLE challenge_answers DROP FOREIGN KEY FK_BC40898331A5801E');
        $this->addSql('ALTER TABLE challenge_duel_questions DROP FOREIGN KEY FK_446B90471E27F6BF');
        $this->addSql('ALTER TABLE challenge_questions_translation DROP FOREIGN KEY FK_8C2788B02C2AC5D3');
        $this->addSql('ALTER TABLE adivina_imagen DROP FOREIGN KEY FK_EF1B569E579F4768');
        $this->addSql('ALTER TABLE announcement_temporalization DROP FOREIGN KEY FK_CEC55850579F4768');
        $this->addSql('ALTER TABLE categorize DROP FOREIGN KEY FK_37DFDA7579F4768');
        $this->addSql('ALTER TABLE categorize_options DROP FOREIGN KEY FK_F140CAB9579F4768');
        $this->addSql('ALTER TABLE content DROP FOREIGN KEY FK_FEC530A9579F4768');
        $this->addSql('ALTER TABLE fillgaps DROP FOREIGN KEY FK_47AB1DD3579F4768');
        $this->addSql('ALTER TABLE gamesword DROP FOREIGN KEY FK_2CD5DB38579F4768');
        $this->addSql('ALTER TABLE guessword DROP FOREIGN KEY FK_32444B08579F4768');
        $this->addSql('ALTER TABLE higher_lower DROP FOREIGN KEY FK_7C0E1150579F4768');
        $this->addSql('ALTER TABLE ordenar_menormayor DROP FOREIGN KEY FK_C414560B579F4768');
        $this->addSql('ALTER TABLE parejas DROP FOREIGN KEY FK_CDEC53A2579F4768');
        $this->addSql('ALTER TABLE pdf DROP FOREIGN KEY FK_EF0DB8C579F4768');
        $this->addSql('ALTER TABLE ppt DROP FOREIGN KEY FK_D3E77D91579F4768');
        $this->addSql('ALTER TABLE puzzle DROP FOREIGN KEY FK_22A6DFDF579F4768');
        $this->addSql('ALTER TABLE question DROP FOREIGN KEY FK_B6F7494E579F4768');
        $this->addSql('ALTER TABLE roulette_word DROP FOREIGN KEY FK_EDB2A53C579F4768');
        $this->addSql('ALTER TABLE scorm DROP FOREIGN KEY FK_3C42BF63579F4768');
        $this->addSql('ALTER TABLE time_game DROP FOREIGN KEY FK_8E9638A1579F4768');
        $this->addSql('ALTER TABLE true_or_false DROP FOREIGN KEY FK_CA13A6C3579F4768');
        $this->addSql('ALTER TABLE user_course_chapter DROP FOREIGN KEY FK_9AC76F9B579F4768');
        $this->addSql('ALTER TABLE video DROP FOREIGN KEY FK_7CC7DA2C579F4768');
        $this->addSql('ALTER TABLE videoquiz DROP FOREIGN KEY FK_13E0266C579F4768');
        $this->addSql('ALTER TABLE chapter DROP FOREIGN KEY FK_F981B52EC54C8C93');
        $this->addSql('ALTER TABLE chapter_type_translation DROP FOREIGN KEY FK_DEBCD0932C2AC5D3');
        $this->addSql('ALTER TABLE chat_channel DROP FOREIGN KEY FK_EEF7422E727ACA70');
        $this->addSql('ALTER TABLE chat_channel_user DROP FOREIGN KEY FK_EF2C1CB672F5A1AA');
        $this->addSql('ALTER TABLE chat_message DROP FOREIGN KEY FK_FAB3FC1672F5A1AA');
        $this->addSql('ALTER TABLE chat_message DROP FOREIGN KEY FK_FAB3FC16FFDF7169');
        $this->addSql('ALTER TABLE chat_message_like DROP FOREIGN KEY FK_FB1DB2EB948B568F');
        $this->addSql('ALTER TABLE chat_message_report DROP FOREIGN KEY FK_E5FE6496537A1329');
        $this->addSql('ALTER TABLE chat_channel DROP FOREIGN KEY FK_EEF7422E1844E6B7');
        $this->addSql('ALTER TABLE classroom_virtual_result DROP FOREIGN KEY FK_B6A56DE127076DC2');
        $this->addSql('ALTER TABLE classroomvirtual_user DROP FOREIGN KEY FK_CF87B8EBF5A029F5');
        $this->addSql('ALTER TABLE classroomvirtual DROP FOREIGN KEY FK_5D831E2FFE14978B');
        $this->addSql('ALTER TABLE comment_task DROP FOREIGN KEY FK_744879C9727ACA70');
        $this->addSql('ALTER TABLE announcement_configuration_type DROP FOREIGN KEY FK_BD7ACF885E9A4305');
        $this->addSql('ALTER TABLE announcement DROP FOREIGN KEY FK_4DB9D91C591CC992');
        $this->addSql('ALTER TABLE challenge DROP FOREIGN KEY FK_D70989519CAA2B25');
        $this->addSql('ALTER TABLE chapter DROP FOREIGN KEY FK_F981B52E591CC992');
        $this->addSql('ALTER TABLE course DROP FOREIGN KEY FK_169E6FB99CAA2B25');
        $this->addSql('ALTER TABLE course_professional_category DROP FOREIGN KEY FK_30476A2C591CC992');
        $this->addSql('ALTER TABLE course_manager DROP FOREIGN KEY FK_F2E72055591CC992');
        $this->addSql('ALTER TABLE course_tag DROP FOREIGN KEY FK_760531B1591CC992');
        $this->addSql('ALTER TABLE course_filter DROP FOREIGN KEY FK_D1FC9E50591CC992');
        $this->addSql('ALTER TABLE course_segment_course DROP FOREIGN KEY FK_959B62B4591CC992');
        $this->addSql('ALTER TABLE course_stat DROP FOREIGN KEY FK_E81A2227591CC992');
        $this->addSql('ALTER TABLE forum_post DROP FOREIGN KEY FK_996BCC5A591CC992');
        $this->addSql('ALTER TABLE history_seen_material DROP FOREIGN KEY FK_C83ABC55591CC992');
        $this->addSql('ALTER TABLE itinerary_course DROP FOREIGN KEY FK_652E788C591CC992');
        $this->addSql('ALTER TABLE main_course_evaluation DROP FOREIGN KEY FK_ABF7398B591CC992');
        $this->addSql('ALTER TABLE material_course DROP FOREIGN KEY FK_87ACE482591CC992');
        $this->addSql('ALTER TABLE material_download_history DROP FOREIGN KEY FK_7180BF5B591CC992');
        $this->addSql('ALTER TABLE nps_question_course DROP FOREIGN KEY FK_5B1CA872591CC992');
        $this->addSql('ALTER TABLE season DROP FOREIGN KEY FK_F0E45BA9591CC992');
        $this->addSql('ALTER TABLE survey_course DROP FOREIGN KEY FK_3E90A7D5591CC992');
        $this->addSql('ALTER TABLE task_course DROP FOREIGN KEY FK_2D37EB6A591CC992');
        $this->addSql('ALTER TABLE user_course DROP FOREIGN KEY FK_73CC7484591CC992');
        $this->addSql('ALTER TABLE user_history_download_diploma DROP FOREIGN KEY FK_8F67717E591CC992');
        $this->addSql('ALTER TABLE user_time DROP FOREIGN KEY FK_1515D48C591CC992');
        $this->addSql('ALTER TABLE course DROP FOREIGN KEY FK_169E6FB912469DE2');
        $this->addSql('ALTER TABLE course_category DROP FOREIGN KEY FK_AFF87497727ACA70');
        $this->addSql('ALTER TABLE course_category_type_course DROP FOREIGN KEY FK_5CE7DCD6628AD36');
        $this->addSql('ALTER TABLE course_category_translation DROP FOREIGN KEY FK_84EE3C232C2AC5D3');
        $this->addSql('ALTER TABLE course_section_course_category DROP FOREIGN KEY FK_2FCB6D086628AD36');
        $this->addSql('ALTER TABLE course DROP FOREIGN KEY FK_169E6FB95FB14BA7');
        $this->addSql('ALTER TABLE course_section_course_category DROP FOREIGN KEY FK_2FCB6D087C1ADF9');
        $this->addSql('ALTER TABLE course_section_translation DROP FOREIGN KEY FK_B600CF812C2AC5D3');
        $this->addSql('ALTER TABLE news_course_section DROP FOREIGN KEY FK_BA5A78CA7C1ADF9');
        $this->addSql('ALTER TABLE course_segment_course DROP FOREIGN KEY FK_959B62B44CB242E');
        $this->addSql('ALTER TABLE course_segment_translation DROP FOREIGN KEY FK_A8F650602C2AC5D3');
        $this->addSql('ALTER TABLE course_segment DROP FOREIGN KEY FK_1042F089ED988ED');
        $this->addSql('ALTER TABLE course_segment_category_translation DROP FOREIGN KEY FK_858433AF2C2AC5D3');
        $this->addSql('ALTER TABLE cron_report DROP FOREIGN KEY FK_B6C6A7F5BE04EA9');
        $this->addSql('ALTER TABLE user_extra DROP FOREIGN KEY FK_AFFDF63DAE80F5DF');
        $this->addSql('ALTER TABLE email_recipient DROP FOREIGN KEY FK_670F64625DA0FB8');
        $this->addSql('ALTER TABLE announcement_tutor DROP FOREIGN KEY FK_FBF66A9F911DD53E');
        $this->addSql('ALTER TABLE user_fields_fundae DROP FOREIGN KEY FK_DC0416CC911DD53E');
        $this->addSql('ALTER TABLE zip_file_task DROP FOREIGN KEY FK_B192226A24F5AC00');
        $this->addSql('ALTER TABLE files_manager DROP FOREIGN KEY FK_5BF93AEE74034854');
        $this->addSql('ALTER TABLE holes DROP FOREIGN KEY FK_9C6EA1E4477D657A');
        $this->addSql('ALTER TABLE course_filter DROP FOREIGN KEY FK_D1FC9E50D395B25E');
        $this->addSql('ALTER TABLE filter DROP FOREIGN KEY FK_7FC45F1D727ACA70');
        $this->addSql('ALTER TABLE filter_translation DROP FOREIGN KEY FK_CD6B90BE2C2AC5D3');
        $this->addSql('ALTER TABLE itinerary_filter DROP FOREIGN KEY FK_C744828D395B25E');
        $this->addSql('ALTER TABLE itinerary_tags DROP FOREIGN KEY FK_689FFE72D395B25E');
        $this->addSql('ALTER TABLE library_filter DROP FOREIGN KEY FK_D426C796D395B25E');
        $this->addSql('ALTER TABLE user_filter DROP FOREIGN KEY FK_1A964420D395B25E');
        $this->addSql('ALTER TABLE manager_filter DROP FOREIGN KEY FK_B9E1596FD395B25E');
        $this->addSql('ALTER TABLE filter DROP FOREIGN KEY FK_7FC45F1D6B444833');
        $this->addSql('ALTER TABLE filter_category DROP FOREIGN KEY FK_3B231C61727ACA70');
        $this->addSql('ALTER TABLE filter_category_translation DROP FOREIGN KEY FK_F5C167262C2AC5D3');
        $this->addSql('ALTER TABLE email_notification DROP FOREIGN KEY FK_EA479099BA454E5D');
        $this->addSql('ALTER TABLE forum_likes DROP FOREIGN KEY FK_51BEE8AABA454E5D');
        $this->addSql('ALTER TABLE forum_post DROP FOREIGN KEY FK_996BCC5A727ACA70');
        $this->addSql('ALTER TABLE forum_post DROP FOREIGN KEY FK_996BCC5AFBF32840');
        $this->addSql('ALTER TABLE forum_report DROP FOREIGN KEY FK_DC804455BA454E5D');
        $this->addSql('ALTER TABLE help_category_translation DROP FOREIGN KEY FK_ED35F7482C2AC5D3');
        $this->addSql('ALTER TABLE help_text DROP FOREIGN KEY FK_7E01925C12469DE2');
        $this->addSql('ALTER TABLE help_text_translation DROP FOREIGN KEY FK_857DDBF02C2AC5D3');
        $this->addSql('ALTER TABLE higher_lower_words DROP FOREIGN KEY FK_177FA2D2BE9CE5EB');
        $this->addSql('ALTER TABLE comment_task DROP FOREIGN KEY FK_744879C95445F77F');
        $this->addSql('ALTER TABLE files_history_task DROP FOREIGN KEY FK_E94FD4805445F77F');
        $this->addSql('ALTER TABLE integration_mapping DROP FOREIGN KEY FK_24CA96D3EC8E1CAD');
        $this->addSql('ALTER TABLE email_notification DROP FOREIGN KEY FK_EA47909915F737B2');
        $this->addSql('ALTER TABLE itinerary_filter DROP FOREIGN KEY FK_C74482815F737B2');
        $this->addSql('ALTER TABLE itinerary_course DROP FOREIGN KEY FK_652E788C15F737B2');
        $this->addSql('ALTER TABLE itinerary_manager DROP FOREIGN KEY FK_ACE4518D15F737B2');
        $this->addSql('ALTER TABLE itinerary_tags DROP FOREIGN KEY FK_689FFE7215F737B2');
        $this->addSql('ALTER TABLE itinerary_translation DROP FOREIGN KEY FK_D12AC99A2C2AC5D3');
        $this->addSql('ALTER TABLE itinerary_user DROP FOREIGN KEY FK_8AB0BC1D15F737B2');
        $this->addSql('ALTER TABLE library_filter DROP FOREIGN KEY FK_D426C796FE2541D7');
        $this->addSql('ALTER TABLE library_comment DROP FOREIGN KEY FK_EEB5EA5CFE2541D7');
        $this->addSql('ALTER TABLE library_file DROP FOREIGN KEY FK_2A50FC7DFE2541D7');
        $this->addSql('ALTER TABLE library_video DROP FOREIGN KEY FK_4F620803FE2541D7');
        $this->addSql('ALTER TABLE library_views DROP FOREIGN KEY FK_22554EA8FE2541D7');
        $this->addSql('ALTER TABLE library DROP FOREIGN KEY FK_A18098BC12469DE2');
        $this->addSql('ALTER TABLE library_category_translation DROP FOREIGN KEY FK_F72523D02C2AC5D3');
        $this->addSql('ALTER TABLE history_seen_material DROP FOREIGN KEY FK_C83ABC556182D372');
        $this->addSql('ALTER TABLE material_download_history DROP FOREIGN KEY FK_7180BF5BE308AC6F');
        $this->addSql('ALTER TABLE message DROP FOREIGN KEY FK_B6BD307FFFDF7169');
        $this->addSql('ALTER TABLE message_attachment DROP FOREIGN KEY FK_B68FF524537A1329');
        $this->addSql('ALTER TABLE news_course_section DROP FOREIGN KEY FK_BA5A78CAB5A459A0');
        $this->addSql('ALTER TABLE news_translation DROP FOREIGN KEY FK_9D5CF3202C2AC5D3');
        $this->addSql('ALTER TABLE nps DROP FOREIGN KEY FK_5B3B66481E27F6BF');
        $this->addSql('ALTER TABLE nps_question_course DROP FOREIGN KEY FK_5B1CA872AE807F50');
        $this->addSql('ALTER TABLE nps_question_announcement DROP FOREIGN KEY FK_52B16383AE807F50');
        $this->addSql('ALTER TABLE nps_question_detail DROP FOREIGN KEY FK_6384B858AE807F50');
        $this->addSql('ALTER TABLE nps_question_translation DROP FOREIGN KEY FK_6650B4BC2C2AC5D3');
        $this->addSql('ALTER TABLE nps_question_detail_translation DROP FOREIGN KEY FK_BFEB50BF2C2AC5D3');
        $this->addSql('ALTER TABLE pages_translation DROP FOREIGN KEY FK_7D0CA9D12C2AC5D3');
        $this->addSql('ALTER TABLE parejas_imagen DROP FOREIGN KEY FK_4907912B8497EC37');
        $this->addSql('ALTER TABLE course_professional_category DROP FOREIGN KEY FK_30476A2CA0CCFBB2');
        $this->addSql('ALTER TABLE professional_category DROP FOREIGN KEY FK_95CAC37C727ACA70');
        $this->addSql('ALTER TABLE user_extra DROP FOREIGN KEY FK_AFFDF63D12469DE2');
        $this->addSql('ALTER TABLE answer DROP FOREIGN KEY FK_DADD4A251E27F6BF');
        $this->addSql('ALTER TABLE chapter DROP FOREIGN KEY FK_F981B52EB6704F40');
        $this->addSql('ALTER TABLE roleplay_beginning DROP FOREIGN KEY FK_E89A902B166D1F9C');
        $this->addSql('ALTER TABLE roleplay_ending DROP FOREIGN KEY FK_3A4A6E5B166D1F9C');
        $this->addSql('ALTER TABLE roleplay_sequence DROP FOREIGN KEY FK_579C74B5166D1F9C');
        $this->addSql('ALTER TABLE user_roleplay_project DROP FOREIGN KEY FK_48521FF4166D1F9C');
        $this->addSql('ALTER TABLE roleplay_scene DROP FOREIGN KEY FK_34CE7AA798FB19AE');
        $this->addSql('ALTER TABLE chapter DROP FOREIGN KEY FK_F981B52E4EC001D1');
        $this->addSql('ALTER TABLE section_default_front_translation DROP FOREIGN KEY FK_B2D0C7942C2AC5D3');
        $this->addSql('ALTER TABLE announcement_group DROP FOREIGN KEY FK_F316812770AF0AD');
        $this->addSql('ALTER TABLE setting DROP FOREIGN KEY FK_9F74B89850DDE1BD');
        $this->addSql('ALTER TABLE nps_question DROP FOREIGN KEY FK_27D40D91B3FE509D');
        $this->addSql('ALTER TABLE survey_announcement DROP FOREIGN KEY FK_3D6BD54CB3FE509D');
        $this->addSql('ALTER TABLE survey_course DROP FOREIGN KEY FK_3E90A7D5B3FE509D');
        $this->addSql('ALTER TABLE survey_translation DROP FOREIGN KEY FK_C919A6A2C2AC5D3');
        $this->addSql('ALTER TABLE course_tag DROP FOREIGN KEY FK_760531B1BAD26311');
        $this->addSql('ALTER TABLE export DROP FOREIGN KEY FK_428C16948DB60186');
        $this->addSql('ALTER TABLE files_task DROP FOREIGN KEY FK_D5B6DE44263604C3');
        $this->addSql('ALTER TABLE task_course_group DROP FOREIGN KEY FK_4CFA6700263604C3');
        $this->addSql('ALTER TABLE task_user DROP FOREIGN KEY FK_FE2042328DB60186');
        $this->addSql('ALTER TABLE history_delivery_task DROP FOREIGN KEY FK_A9B9C8D8B88FF97F');
        $this->addSql('ALTER TABLE course DROP FOREIGN KEY FK_169E6FB9EDDA8882');
        $this->addSql('ALTER TABLE course_category_type_course DROP FOREIGN KEY FK_5CE7DCDEDDA8882');
        $this->addSql('ALTER TABLE type_course_alerts DROP FOREIGN KEY FK_29AFB613EDDA8882');
        $this->addSql('ALTER TABLE type_course_announcement_step_creation DROP FOREIGN KEY FK_86190621EDDA8882');
        $this->addSql('ALTER TABLE type_course_translation DROP FOREIGN KEY FK_A7660E6B2C2AC5D3');
        $this->addSql('ALTER TABLE type_course_announcement_step_configuration DROP FOREIGN KEY FK_82556DB26DFFE627');
        $this->addSql('ALTER TABLE type_course_announcement_step_creation_trans DROP FOREIGN KEY FK_7100AE352C2AC5D3');
        $this->addSql('ALTER TABLE announcement DROP FOREIGN KEY FK_4DB9D91CB4991890');
        $this->addSql('ALTER TABLE course DROP FOREIGN KEY FK_169E6FB9B4991890');
        $this->addSql('ALTER TABLE type_identification_translation DROP FOREIGN KEY FK_75F23B762C2AC5D3');
        $this->addSql('ALTER TABLE user_identification DROP FOREIGN KEY FK_2262AD54F1CF261E');
        $this->addSql('ALTER TABLE announcement_group DROP FOREIGN KEY FK_F31681272D614D0');
        $this->addSql('ALTER TABLE announcement_group_session DROP FOREIGN KEY FK_6FBD43A472D614D0');
        $this->addSql('ALTER TABLE type_money_translation DROP FOREIGN KEY FK_A87D08C72C2AC5D3');
        $this->addSql('ALTER TABLE video DROP FOREIGN KEY FK_7CC7DA2CE43E27B2');
        $this->addSql('ALTER TABLE alert_type_tutor DROP FOREIGN KEY FK_1604A96FB03A8386');
        $this->addSql('ALTER TABLE alert_type_tutor DROP FOREIGN KEY FK_1604A96F896DBBDE');
        $this->addSql('ALTER TABLE alert_type_tutor DROP FOREIGN KEY FK_1604A96FC76F1F52');
        $this->addSql('ALTER TABLE announcement DROP FOREIGN KEY FK_4DB9D91CB8778369');
        $this->addSql('ALTER TABLE announcement DROP FOREIGN KEY FK_4DB9D91CB03A8386');
        $this->addSql('ALTER TABLE announcement DROP FOREIGN KEY FK_4DB9D91C896DBBDE');
        $this->addSql('ALTER TABLE announcement DROP FOREIGN KEY FK_4DB9D91CC76F1F52');
        $this->addSql('ALTER TABLE announcement_alert_tutor DROP FOREIGN KEY FK_DDE31BDB03A8386');
        $this->addSql('ALTER TABLE announcement_alert_tutor DROP FOREIGN KEY FK_DDE31BD896DBBDE');
        $this->addSql('ALTER TABLE announcement_alert_tutor DROP FOREIGN KEY FK_DDE31BDC76F1F52');
        $this->addSql('ALTER TABLE announcement_aproved_criteria DROP FOREIGN KEY FK_7324EB3EB03A8386');
        $this->addSql('ALTER TABLE announcement_aproved_criteria DROP FOREIGN KEY FK_7324EB3E896DBBDE');
        $this->addSql('ALTER TABLE announcement_aproved_criteria DROP FOREIGN KEY FK_7324EB3EC76F1F52');
        $this->addSql('ALTER TABLE announcement_configuration DROP FOREIGN KEY FK_9C1B67ABB03A8386');
        $this->addSql('ALTER TABLE announcement_configuration DROP FOREIGN KEY FK_9C1B67AB896DBBDE');
        $this->addSql('ALTER TABLE announcement_configuration DROP FOREIGN KEY FK_9C1B67ABC76F1F52');
        $this->addSql('ALTER TABLE announcement_configuration_type DROP FOREIGN KEY FK_BD7ACF88B03A8386');
        $this->addSql('ALTER TABLE announcement_configuration_type DROP FOREIGN KEY FK_BD7ACF88896DBBDE');
        $this->addSql('ALTER TABLE announcement_configuration_type DROP FOREIGN KEY FK_BD7ACF88C76F1F52');
        $this->addSql('ALTER TABLE announcement_criteria DROP FOREIGN KEY FK_CD521172B03A8386');
        $this->addSql('ALTER TABLE announcement_criteria DROP FOREIGN KEY FK_CD521172896DBBDE');
        $this->addSql('ALTER TABLE announcement_criteria DROP FOREIGN KEY FK_CD521172C76F1F52');
        $this->addSql('ALTER TABLE announcement_didatic_guide DROP FOREIGN KEY FK_BAC51D60B03A8386');
        $this->addSql('ALTER TABLE announcement_didatic_guide DROP FOREIGN KEY FK_BAC51D60896DBBDE');
        $this->addSql('ALTER TABLE announcement_didatic_guide DROP FOREIGN KEY FK_BAC51D60C76F1F52');
        $this->addSql('ALTER TABLE announcement_group DROP FOREIGN KEY FK_F316812B03A8386');
        $this->addSql('ALTER TABLE announcement_group DROP FOREIGN KEY FK_F316812896DBBDE');
        $this->addSql('ALTER TABLE announcement_group DROP FOREIGN KEY FK_F316812C76F1F52');
        $this->addSql('ALTER TABLE announcement_notification DROP FOREIGN KEY FK_4525C829B03A8386');
        $this->addSql('ALTER TABLE announcement_notification DROP FOREIGN KEY FK_4525C829896DBBDE');
        $this->addSql('ALTER TABLE announcement_notification DROP FOREIGN KEY FK_4525C829C76F1F52');
        $this->addSql('ALTER TABLE announcement_notification_group DROP FOREIGN KEY FK_FF834F9AB03A8386');
        $this->addSql('ALTER TABLE announcement_notification_group DROP FOREIGN KEY FK_FF834F9A896DBBDE');
        $this->addSql('ALTER TABLE announcement_notification_group DROP FOREIGN KEY FK_FF834F9AC76F1F52');
        $this->addSql('ALTER TABLE announcement_observation DROP FOREIGN KEY FK_BBF55BFEB03A8386');
        $this->addSql('ALTER TABLE announcement_observation DROP FOREIGN KEY FK_BBF55BFE896DBBDE');
        $this->addSql('ALTER TABLE announcement_observation DROP FOREIGN KEY FK_BBF55BFEC76F1F52');
        $this->addSql('ALTER TABLE announcement_observation_document DROP FOREIGN KEY FK_E69ACE46B03A8386');
        $this->addSql('ALTER TABLE announcement_observation_document DROP FOREIGN KEY FK_E69ACE46896DBBDE');
        $this->addSql('ALTER TABLE announcement_observation_document DROP FOREIGN KEY FK_E69ACE46C76F1F52');
        $this->addSql('ALTER TABLE announcement_temporalization DROP FOREIGN KEY FK_CEC55850B03A8386');
        $this->addSql('ALTER TABLE announcement_temporalization DROP FOREIGN KEY FK_CEC55850896DBBDE');
        $this->addSql('ALTER TABLE announcement_temporalization DROP FOREIGN KEY FK_CEC55850C76F1F52');
        $this->addSql('ALTER TABLE announcement_tutor DROP FOREIGN KEY FK_FBF66A9F208F64F1');
        $this->addSql('ALTER TABLE announcement_tutor_connection DROP FOREIGN KEY FK_817BA039B03A8386');
        $this->addSql('ALTER TABLE announcement_tutor_connection DROP FOREIGN KEY FK_817BA039896DBBDE');
        $this->addSql('ALTER TABLE announcement_tutor_connection DROP FOREIGN KEY FK_817BA039C76F1F52');
        $this->addSql('ALTER TABLE announcement_user DROP FOREIGN KEY FK_A1A2DE15A76ED395');
        $this->addSql('ALTER TABLE answer DROP FOREIGN KEY FK_DADD4A25B03A8386');
        $this->addSql('ALTER TABLE answer DROP FOREIGN KEY FK_DADD4A25896DBBDE');
        $this->addSql('ALTER TABLE answer DROP FOREIGN KEY FK_DADD4A25C76F1F52');
        $this->addSql('ALTER TABLE categorize DROP FOREIGN KEY FK_37DFDA7B03A8386');
        $this->addSql('ALTER TABLE categorize DROP FOREIGN KEY FK_37DFDA7896DBBDE');
        $this->addSql('ALTER TABLE categorize DROP FOREIGN KEY FK_37DFDA7C76F1F52');
        $this->addSql('ALTER TABLE categorize_options DROP FOREIGN KEY FK_F140CAB9B03A8386');
        $this->addSql('ALTER TABLE categorize_options DROP FOREIGN KEY FK_F140CAB9896DBBDE');
        $this->addSql('ALTER TABLE categorize_options DROP FOREIGN KEY FK_F140CAB9C76F1F52');
        $this->addSql('ALTER TABLE challenge DROP FOREIGN KEY FK_D7098951B03A8386');
        $this->addSql('ALTER TABLE challenge DROP FOREIGN KEY FK_D7098951896DBBDE');
        $this->addSql('ALTER TABLE challenge DROP FOREIGN KEY FK_D7098951C76F1F52');
        $this->addSql('ALTER TABLE challenge_answers DROP FOREIGN KEY FK_BC408983B03A8386');
        $this->addSql('ALTER TABLE challenge_answers DROP FOREIGN KEY FK_BC408983896DBBDE');
        $this->addSql('ALTER TABLE challenge_answers DROP FOREIGN KEY FK_BC408983C76F1F52');
        $this->addSql('ALTER TABLE challenge_duel DROP FOREIGN KEY FK_921BA0E456AE248B');
        $this->addSql('ALTER TABLE challenge_duel DROP FOREIGN KEY FK_921BA0E4441B8B65');
        $this->addSql('ALTER TABLE challenge_duel DROP FOREIGN KEY FK_921BA0E4B03A8386');
        $this->addSql('ALTER TABLE challenge_duel DROP FOREIGN KEY FK_921BA0E4896DBBDE');
        $this->addSql('ALTER TABLE challenge_duel DROP FOREIGN KEY FK_921BA0E4C76F1F52');
        $this->addSql('ALTER TABLE challenge_duel_questions_adn DROP FOREIGN KEY FK_5CCC6C92A76ED395');
        $this->addSql('ALTER TABLE challenge_questions DROP FOREIGN KEY FK_2A4B884AB03A8386');
        $this->addSql('ALTER TABLE challenge_questions DROP FOREIGN KEY FK_2A4B884A896DBBDE');
        $this->addSql('ALTER TABLE challenge_questions DROP FOREIGN KEY FK_2A4B884AC76F1F52');
        $this->addSql('ALTER TABLE challenge_user DROP FOREIGN KEY FK_843CD1CFA76ED395');
        $this->addSql('ALTER TABLE challenge_user DROP FOREIGN KEY FK_843CD1CFB03A8386');
        $this->addSql('ALTER TABLE challenge_user DROP FOREIGN KEY FK_843CD1CF896DBBDE');
        $this->addSql('ALTER TABLE challenge_user DROP FOREIGN KEY FK_843CD1CFC76F1F52');
        $this->addSql('ALTER TABLE challenge_user_points DROP FOREIGN KEY FK_3B543DA9A76ED395');
        $this->addSql('ALTER TABLE chapter DROP FOREIGN KEY FK_F981B52EB03A8386');
        $this->addSql('ALTER TABLE chapter DROP FOREIGN KEY FK_F981B52E896DBBDE');
        $this->addSql('ALTER TABLE chapter DROP FOREIGN KEY FK_F981B52EC76F1F52');
        $this->addSql('ALTER TABLE chat_channel DROP FOREIGN KEY FK_EEF7422EB03A8386');
        $this->addSql('ALTER TABLE chat_channel DROP FOREIGN KEY FK_EEF7422E896DBBDE');
        $this->addSql('ALTER TABLE chat_channel DROP FOREIGN KEY FK_EEF7422EC76F1F52');
        $this->addSql('ALTER TABLE chat_channel_user DROP FOREIGN KEY FK_EF2C1CB6A76ED395');
        $this->addSql('ALTER TABLE chat_message DROP FOREIGN KEY FK_FAB3FC16A76ED395');
        $this->addSql('ALTER TABLE chat_message_like DROP FOREIGN KEY FK_FB1DB2EBA76ED395');
        $this->addSql('ALTER TABLE chat_message_report DROP FOREIGN KEY FK_E5FE6496A76ED395');
        $this->addSql('ALTER TABLE comment_task DROP FOREIGN KEY FK_744879C9B03A8386');
        $this->addSql('ALTER TABLE comment_task DROP FOREIGN KEY FK_744879C9896DBBDE');
        $this->addSql('ALTER TABLE comment_task DROP FOREIGN KEY FK_744879C9C76F1F52');
        $this->addSql('ALTER TABLE configuration_client_announcement DROP FOREIGN KEY FK_71AECC53B03A8386');
        $this->addSql('ALTER TABLE configuration_client_announcement DROP FOREIGN KEY FK_71AECC53896DBBDE');
        $this->addSql('ALTER TABLE configuration_client_announcement DROP FOREIGN KEY FK_71AECC53C76F1F52');
        $this->addSql('ALTER TABLE content DROP FOREIGN KEY FK_FEC530A9B03A8386');
        $this->addSql('ALTER TABLE content DROP FOREIGN KEY FK_FEC530A9896DBBDE');
        $this->addSql('ALTER TABLE content DROP FOREIGN KEY FK_FEC530A9C76F1F52');
        $this->addSql('ALTER TABLE course DROP FOREIGN KEY FK_169E6FB9B03A8386');
        $this->addSql('ALTER TABLE course DROP FOREIGN KEY FK_169E6FB9896DBBDE');
        $this->addSql('ALTER TABLE course DROP FOREIGN KEY FK_169E6FB9C76F1F52');
        $this->addSql('ALTER TABLE course_manager DROP FOREIGN KEY FK_F2E72055A76ED395');
        $this->addSql('ALTER TABLE course_level DROP FOREIGN KEY FK_7341CBFBB03A8386');
        $this->addSql('ALTER TABLE course_level DROP FOREIGN KEY FK_7341CBFB896DBBDE');
        $this->addSql('ALTER TABLE course_level DROP FOREIGN KEY FK_7341CBFBC76F1F52');
        $this->addSql('ALTER TABLE course_section DROP FOREIGN KEY FK_25B07F03B03A8386');
        $this->addSql('ALTER TABLE course_section DROP FOREIGN KEY FK_25B07F03896DBBDE');
        $this->addSql('ALTER TABLE course_section DROP FOREIGN KEY FK_25B07F03C76F1F52');
        $this->addSql('ALTER TABLE course_segment DROP FOREIGN KEY FK_1042F089B03A8386');
        $this->addSql('ALTER TABLE course_segment DROP FOREIGN KEY FK_1042F089896DBBDE');
        $this->addSql('ALTER TABLE course_segment DROP FOREIGN KEY FK_1042F089C76F1F52');
        $this->addSql('ALTER TABLE documentation DROP FOREIGN KEY FK_73D5A93BB03A8386');
        $this->addSql('ALTER TABLE documentation DROP FOREIGN KEY FK_73D5A93B896DBBDE');
        $this->addSql('ALTER TABLE documentation DROP FOREIGN KEY FK_73D5A93BC76F1F52');
        $this->addSql('ALTER TABLE email_notification DROP FOREIGN KEY FK_EA479099A76ED395');
        $this->addSql('ALTER TABLE email_recipient DROP FOREIGN KEY FK_670F6462A76ED395');
        $this->addSql('ALTER TABLE email_template DROP FOREIGN KEY FK_9C0600CAB03A8386');
        $this->addSql('ALTER TABLE email_template DROP FOREIGN KEY FK_9C0600CA896DBBDE');
        $this->addSql('ALTER TABLE email_template DROP FOREIGN KEY FK_9C0600CAC76F1F52');
        $this->addSql('ALTER TABLE files_history_task DROP FOREIGN KEY FK_E94FD480B03A8386');
        $this->addSql('ALTER TABLE files_history_task DROP FOREIGN KEY FK_E94FD480896DBBDE');
        $this->addSql('ALTER TABLE files_history_task DROP FOREIGN KEY FK_E94FD480C76F1F52');
        $this->addSql('ALTER TABLE files_manager DROP FOREIGN KEY FK_5BF93AEEB03A8386');
        $this->addSql('ALTER TABLE files_manager DROP FOREIGN KEY FK_5BF93AEE896DBBDE');
        $this->addSql('ALTER TABLE files_manager DROP FOREIGN KEY FK_5BF93AEEC76F1F52');
        $this->addSql('ALTER TABLE files_task DROP FOREIGN KEY FK_D5B6DE44B03A8386');
        $this->addSql('ALTER TABLE files_task DROP FOREIGN KEY FK_D5B6DE44896DBBDE');
        $this->addSql('ALTER TABLE files_task DROP FOREIGN KEY FK_D5B6DE44C76F1F52');
        $this->addSql('ALTER TABLE forum_likes DROP FOREIGN KEY FK_51BEE8AAA76ED395');
        $this->addSql('ALTER TABLE forum_post DROP FOREIGN KEY FK_996BCC5AA76ED395');
        $this->addSql('ALTER TABLE forum_post DROP FOREIGN KEY FK_996BCC5AB03A8386');
        $this->addSql('ALTER TABLE forum_post DROP FOREIGN KEY FK_996BCC5A896DBBDE');
        $this->addSql('ALTER TABLE forum_post DROP FOREIGN KEY FK_996BCC5AC76F1F52');
        $this->addSql('ALTER TABLE forum_report DROP FOREIGN KEY FK_DC804455A76ED395');
        $this->addSql('ALTER TABLE generic_token DROP FOREIGN KEY FK_360B1929B03A8386');
        $this->addSql('ALTER TABLE generic_token DROP FOREIGN KEY FK_360B1929896DBBDE');
        $this->addSql('ALTER TABLE generic_token DROP FOREIGN KEY FK_360B1929C76F1F52');
        $this->addSql('ALTER TABLE higher_lower DROP FOREIGN KEY FK_7C0E1150B03A8386');
        $this->addSql('ALTER TABLE higher_lower DROP FOREIGN KEY FK_7C0E1150896DBBDE');
        $this->addSql('ALTER TABLE higher_lower DROP FOREIGN KEY FK_7C0E1150C76F1F52');
        $this->addSql('ALTER TABLE history_delivery_task DROP FOREIGN KEY FK_A9B9C8D8B03A8386');
        $this->addSql('ALTER TABLE history_delivery_task DROP FOREIGN KEY FK_A9B9C8D8896DBBDE');
        $this->addSql('ALTER TABLE history_delivery_task DROP FOREIGN KEY FK_A9B9C8D8C76F1F52');
        $this->addSql('ALTER TABLE history_seen_material DROP FOREIGN KEY FK_C83ABC55A76ED395');
        $this->addSql('ALTER TABLE itinerary DROP FOREIGN KEY FK_FF2238F6B03A8386');
        $this->addSql('ALTER TABLE itinerary DROP FOREIGN KEY FK_FF2238F6896DBBDE');
        $this->addSql('ALTER TABLE itinerary DROP FOREIGN KEY FK_FF2238F6C76F1F52');
        $this->addSql('ALTER TABLE itinerary_manager DROP FOREIGN KEY FK_ACE4518DA76ED395');
        $this->addSql('ALTER TABLE itinerary_user DROP FOREIGN KEY FK_8AB0BC1DA76ED395');
        $this->addSql('ALTER TABLE library DROP FOREIGN KEY FK_A18098BCB03A8386');
        $this->addSql('ALTER TABLE library DROP FOREIGN KEY FK_A18098BC896DBBDE');
        $this->addSql('ALTER TABLE library DROP FOREIGN KEY FK_A18098BCC76F1F52');
        $this->addSql('ALTER TABLE library_category DROP FOREIGN KEY FK_20EFE8D5B03A8386');
        $this->addSql('ALTER TABLE library_category DROP FOREIGN KEY FK_20EFE8D5896DBBDE');
        $this->addSql('ALTER TABLE library_category DROP FOREIGN KEY FK_20EFE8D5C76F1F52');
        $this->addSql('ALTER TABLE library_comment DROP FOREIGN KEY FK_EEB5EA5C69463ADC');
        $this->addSql('ALTER TABLE library_comment DROP FOREIGN KEY FK_EEB5EA5C386B8E7');
        $this->addSql('ALTER TABLE library_comment DROP FOREIGN KEY FK_EEB5EA5CB03A8386');
        $this->addSql('ALTER TABLE library_comment DROP FOREIGN KEY FK_EEB5EA5C896DBBDE');
        $this->addSql('ALTER TABLE library_comment DROP FOREIGN KEY FK_EEB5EA5CC76F1F52');
        $this->addSql('ALTER TABLE library_views DROP FOREIGN KEY FK_22554EA8A76ED395');
        $this->addSql('ALTER TABLE material_course DROP FOREIGN KEY FK_87ACE482B03A8386');
        $this->addSql('ALTER TABLE material_course DROP FOREIGN KEY FK_87ACE482896DBBDE');
        $this->addSql('ALTER TABLE material_course DROP FOREIGN KEY FK_87ACE482C76F1F52');
        $this->addSql('ALTER TABLE material_download_history DROP FOREIGN KEY FK_7180BF5BA76ED395');
        $this->addSql('ALTER TABLE material_download_history DROP FOREIGN KEY FK_7180BF5BB03A8386');
        $this->addSql('ALTER TABLE material_download_history DROP FOREIGN KEY FK_7180BF5B896DBBDE');
        $this->addSql('ALTER TABLE material_download_history DROP FOREIGN KEY FK_7180BF5BC76F1F52');
        $this->addSql('ALTER TABLE message DROP FOREIGN KEY FK_B6BD307FF624B39D');
        $this->addSql('ALTER TABLE message DROP FOREIGN KEY FK_B6BD307FE92F8F78');
        $this->addSql('ALTER TABLE news DROP FOREIGN KEY FK_1DD39950B03A8386');
        $this->addSql('ALTER TABLE news DROP FOREIGN KEY FK_1DD39950896DBBDE');
        $this->addSql('ALTER TABLE news DROP FOREIGN KEY FK_1DD39950C76F1F52');
        $this->addSql('ALTER TABLE notification DROP FOREIGN KEY FK_BF5476CAA76ED395');
        $this->addSql('ALTER TABLE nps DROP FOREIGN KEY FK_5B3B6648A76ED395');
        $this->addSql('ALTER TABLE nps DROP FOREIGN KEY FK_5B3B6648B03A8386');
        $this->addSql('ALTER TABLE nps DROP FOREIGN KEY FK_5B3B6648896DBBDE');
        $this->addSql('ALTER TABLE nps DROP FOREIGN KEY FK_5B3B6648C76F1F52');
        $this->addSql('ALTER TABLE nps_question DROP FOREIGN KEY FK_27D40D91B03A8386');
        $this->addSql('ALTER TABLE nps_question DROP FOREIGN KEY FK_27D40D91896DBBDE');
        $this->addSql('ALTER TABLE nps_question DROP FOREIGN KEY FK_27D40D91C76F1F52');
        $this->addSql('ALTER TABLE nps_question_detail DROP FOREIGN KEY FK_6384B858B03A8386');
        $this->addSql('ALTER TABLE nps_question_detail DROP FOREIGN KEY FK_6384B858896DBBDE');
        $this->addSql('ALTER TABLE nps_question_detail DROP FOREIGN KEY FK_6384B858C76F1F52');
        $this->addSql('ALTER TABLE pages DROP FOREIGN KEY FK_2074E575B03A8386');
        $this->addSql('ALTER TABLE pages DROP FOREIGN KEY FK_2074E575896DBBDE');
        $this->addSql('ALTER TABLE pages DROP FOREIGN KEY FK_2074E575C76F1F52');
        $this->addSql('ALTER TABLE question DROP FOREIGN KEY FK_B6F7494EB03A8386');
        $this->addSql('ALTER TABLE question DROP FOREIGN KEY FK_B6F7494E896DBBDE');
        $this->addSql('ALTER TABLE question DROP FOREIGN KEY FK_B6F7494EC76F1F52');
        $this->addSql('ALTER TABLE recovery_code DROP FOREIGN KEY FK_2C8D0584A76ED395');
        $this->addSql('ALTER TABLE reset_password_request DROP FOREIGN KEY FK_7CE748AA76ED395');
        $this->addSql('ALTER TABLE survey DROP FOREIGN KEY FK_AD5F9BFCB03A8386');
        $this->addSql('ALTER TABLE survey DROP FOREIGN KEY FK_AD5F9BFC896DBBDE');
        $this->addSql('ALTER TABLE survey DROP FOREIGN KEY FK_AD5F9BFCC76F1F52');
        $this->addSql('ALTER TABLE task_course DROP FOREIGN KEY FK_2D37EB6AB03A8386');
        $this->addSql('ALTER TABLE task_course DROP FOREIGN KEY FK_2D37EB6A896DBBDE');
        $this->addSql('ALTER TABLE task_course DROP FOREIGN KEY FK_2D37EB6AC76F1F52');
        $this->addSql('ALTER TABLE task_course_group DROP FOREIGN KEY FK_4CFA6700B03A8386');
        $this->addSql('ALTER TABLE task_course_group DROP FOREIGN KEY FK_4CFA6700896DBBDE');
        $this->addSql('ALTER TABLE task_course_group DROP FOREIGN KEY FK_4CFA6700C76F1F52');
        $this->addSql('ALTER TABLE task_user DROP FOREIGN KEY FK_FE204232A76ED395');
        $this->addSql('ALTER TABLE task_user DROP FOREIGN KEY FK_FE204232B03A8386');
        $this->addSql('ALTER TABLE task_user DROP FOREIGN KEY FK_FE204232896DBBDE');
        $this->addSql('ALTER TABLE task_user DROP FOREIGN KEY FK_FE204232C76F1F52');
        $this->addSql('ALTER TABLE type_course DROP FOREIGN KEY FK_35490481B03A8386');
        $this->addSql('ALTER TABLE type_course DROP FOREIGN KEY FK_35490481896DBBDE');
        $this->addSql('ALTER TABLE type_course DROP FOREIGN KEY FK_35490481C76F1F52');
        $this->addSql('ALTER TABLE type_diploma DROP FOREIGN KEY FK_C400E6A2B03A8386');
        $this->addSql('ALTER TABLE type_diploma DROP FOREIGN KEY FK_C400E6A2896DBBDE');
        $this->addSql('ALTER TABLE type_diploma DROP FOREIGN KEY FK_C400E6A2C76F1F52');
        $this->addSql('ALTER TABLE user DROP FOREIGN KEY FK_8D93D64946E746A6');
        $this->addSql('ALTER TABLE user DROP FOREIGN KEY FK_8D93D649B03A8386');
        $this->addSql('ALTER TABLE user DROP FOREIGN KEY FK_8D93D649896DBBDE');
        $this->addSql('ALTER TABLE user DROP FOREIGN KEY FK_8D93D649C76F1F52');
        $this->addSql('ALTER TABLE user_filter DROP FOREIGN KEY FK_1A964420A76ED395');
        $this->addSql('ALTER TABLE manager_filter DROP FOREIGN KEY FK_B9E1596FA76ED395');
        $this->addSql('ALTER TABLE user_comments DROP FOREIGN KEY FK_BF13722AA76ED395');
        $this->addSql('ALTER TABLE user_company DROP FOREIGN KEY FK_17B21745B03A8386');
        $this->addSql('ALTER TABLE user_company DROP FOREIGN KEY FK_17B21745896DBBDE');
        $this->addSql('ALTER TABLE user_company DROP FOREIGN KEY FK_17B21745C76F1F52');
        $this->addSql('ALTER TABLE user_course DROP FOREIGN KEY FK_73CC7484A76ED395');
        $this->addSql('ALTER TABLE user_courses_total_time DROP FOREIGN KEY FK_B7D6BB96A76ED395');
        $this->addSql('ALTER TABLE user_extra DROP FOREIGN KEY FK_AFFDF63DA76ED395');
        $this->addSql('ALTER TABLE user_extra DROP FOREIGN KEY FK_AFFDF63DB03A8386');
        $this->addSql('ALTER TABLE user_extra DROP FOREIGN KEY FK_AFFDF63D896DBBDE');
        $this->addSql('ALTER TABLE user_extra DROP FOREIGN KEY FK_AFFDF63DC76F1F52');
        $this->addSql('ALTER TABLE user_fields_fundae DROP FOREIGN KEY FK_DC0416CCA76ED395');
        $this->addSql('ALTER TABLE user_history_download_diploma DROP FOREIGN KEY FK_8F67717EB03A8386');
        $this->addSql('ALTER TABLE user_history_download_diploma DROP FOREIGN KEY FK_8F67717E896DBBDE');
        $this->addSql('ALTER TABLE user_history_download_diploma DROP FOREIGN KEY FK_8F67717EC76F1F52');
        $this->addSql('ALTER TABLE user_identification DROP FOREIGN KEY FK_2262AD54A76ED395');
        $this->addSql('ALTER TABLE user_login DROP FOREIGN KEY FK_48CA3048A76ED395');
        $this->addSql('ALTER TABLE user_manage DROP FOREIGN KEY FK_4120B177A76ED395');
        $this->addSql('ALTER TABLE user_notification DROP FOREIGN KEY FK_3F980AC8A76ED395');
        $this->addSql('ALTER TABLE user_professional_category DROP FOREIGN KEY FK_55CE8E02B03A8386');
        $this->addSql('ALTER TABLE user_professional_category DROP FOREIGN KEY FK_55CE8E02896DBBDE');
        $this->addSql('ALTER TABLE user_professional_category DROP FOREIGN KEY FK_55CE8E02C76F1F52');
        $this->addSql('ALTER TABLE user_roleplay_project DROP FOREIGN KEY FK_48521FF4A76ED395');
        $this->addSql('ALTER TABLE user_roleplay_project DROP FOREIGN KEY FK_48521FF4B03A8386');
        $this->addSql('ALTER TABLE user_roleplay_project DROP FOREIGN KEY FK_48521FF4896DBBDE');
        $this->addSql('ALTER TABLE user_roleplay_project DROP FOREIGN KEY FK_48521FF4C76F1F52');
        $this->addSql('ALTER TABLE user_study_level DROP FOREIGN KEY FK_6C8B5566B03A8386');
        $this->addSql('ALTER TABLE user_study_level DROP FOREIGN KEY FK_6C8B5566896DBBDE');
        $this->addSql('ALTER TABLE user_study_level DROP FOREIGN KEY FK_6C8B5566C76F1F52');
        $this->addSql('ALTER TABLE user_time DROP FOREIGN KEY FK_1515D48CA76ED395');
        $this->addSql('ALTER TABLE user_token DROP FOREIGN KEY FK_BDF55A63A76ED395');
        $this->addSql('ALTER TABLE user_vcms_project DROP FOREIGN KEY FK_2AB6F06AA76ED395');
        $this->addSql('ALTER TABLE user_vcms_project DROP FOREIGN KEY FK_2AB6F06AB03A8386');
        $this->addSql('ALTER TABLE user_vcms_project DROP FOREIGN KEY FK_2AB6F06A896DBBDE');
        $this->addSql('ALTER TABLE user_vcms_project DROP FOREIGN KEY FK_2AB6F06AC76F1F52');
        $this->addSql('ALTER TABLE user_work_center DROP FOREIGN KEY FK_4212E493B03A8386');
        $this->addSql('ALTER TABLE user_work_center DROP FOREIGN KEY FK_4212E493896DBBDE');
        $this->addSql('ALTER TABLE user_work_center DROP FOREIGN KEY FK_4212E493C76F1F52');
        $this->addSql('ALTER TABLE user_work_department DROP FOREIGN KEY FK_C4A220D6B03A8386');
        $this->addSql('ALTER TABLE user_work_department DROP FOREIGN KEY FK_C4A220D6896DBBDE');
        $this->addSql('ALTER TABLE user_work_department DROP FOREIGN KEY FK_C4A220D6C76F1F52');
        $this->addSql('ALTER TABLE zip_file_task DROP FOREIGN KEY FK_B192226AB03A8386');
        $this->addSql('ALTER TABLE zip_file_task DROP FOREIGN KEY FK_B192226A896DBBDE');
        $this->addSql('ALTER TABLE zip_file_task DROP FOREIGN KEY FK_B192226AC76F1F52');
        $this->addSql('ALTER TABLE announcement_tutor DROP FOREIGN KEY FK_FBF66A9F30FCDC3A');
        $this->addSql('ALTER TABLE announcement_user DROP FOREIGN KEY FK_A1A2DE1530FCDC3A');
        $this->addSql('ALTER TABLE user_fields_fundae DROP FOREIGN KEY FK_DC0416CC30FCDC3A');
        $this->addSql('ALTER TABLE nps DROP FOREIGN KEY FK_5B3B6648591CC992');
        $this->addSql('ALTER TABLE user_course_chapter DROP FOREIGN KEY FK_9AC76F9B59FC4476');
        $this->addSql('ALTER TABLE user_roleplay_project DROP FOREIGN KEY FK_48521FF47062B2C0');
        $this->addSql('ALTER TABLE user_vcms_project DROP FOREIGN KEY FK_2AB6F06A7062B2C0');
        $this->addSql('ALTER TABLE announcement_user DROP FOREIGN KEY FK_A1A2DE15BC194B79');
        $this->addSql('ALTER TABLE user_fields_fundae DROP FOREIGN KEY FK_DC0416CCBC194B79');
        $this->addSql('ALTER TABLE announcement_user DROP FOREIGN KEY FK_A1A2DE1597BD8177');
        $this->addSql('ALTER TABLE user_fields_fundae DROP FOREIGN KEY FK_DC0416CC97BD8177');
        $this->addSql('ALTER TABLE announcement_inspector_access DROP FOREIGN KEY FK_628C140B41DEE7B9');
        $this->addSql('ALTER TABLE announcement_user DROP FOREIGN KEY FK_A1A2DE1550BF847B');
        $this->addSql('ALTER TABLE user_fields_fundae DROP FOREIGN KEY FK_DC0416CC50BF847B');
        $this->addSql('ALTER TABLE announcement_user DROP FOREIGN KEY FK_A1A2DE1542196A10');
        $this->addSql('ALTER TABLE user_fields_fundae DROP FOREIGN KEY FK_DC0416CC42196A10');
        $this->addSql('ALTER TABLE chapter DROP FOREIGN KEY FK_F981B52E7631067');
        $this->addSql('ALTER TABLE user_vcms_project DROP FOREIGN KEY FK_2AB6F06A166D1F9C');
        $this->addSql('ALTER TABLE answers_video_quiz DROP FOREIGN KEY FK_1CFB516C1E27F6BF');
        $this->addSql('ALTER TABLE videopreguntas DROP FOREIGN KEY FK_BE3B3F894458963B');
        $this->addSql('ALTER TABLE chapter DROP is_active');
        $this->addSql('ALTER TABLE chapter CHANGE is_active is_active TINYINT(1) DEFAULT \'1\'');
        $this->addSql('DROP TABLE adivina_imagen');
        $this->addSql('DROP TABLE alert_type_tutor');
        $this->addSql('DROP TABLE alert_type_tutor_translation');
        $this->addSql('DROP TABLE announcement');
        $this->addSql('DROP TABLE announcement_alert_tutor');
        $this->addSql('DROP TABLE announcement_aproved_criteria');
        $this->addSql('DROP TABLE announcement_configuration');
        $this->addSql('DROP TABLE announcement_configuration_type');
        $this->addSql('DROP TABLE announcement_configuration_type_translation');
        $this->addSql('DROP TABLE announcement_criteria');
        $this->addSql('DROP TABLE announcement_criteria_translation');
        $this->addSql('DROP TABLE announcement_didatic_guide');
        $this->addSql('DROP TABLE announcement_group');
        $this->addSql('DROP TABLE announcement_group_session');
        $this->addSql('DROP TABLE announcement_group_session_assistance_files');
        $this->addSql('DROP TABLE announcement_inspector_access');
        $this->addSql('DROP TABLE announcement_modality');
        $this->addSql('DROP TABLE announcement_modality_translation');
        $this->addSql('DROP TABLE announcement_notification');
        $this->addSql('DROP TABLE announcement_notification_group');
        $this->addSql('DROP TABLE announcement_observation');
        $this->addSql('DROP TABLE announcement_observation_document');
        $this->addSql('DROP TABLE announcement_step_creation');
        $this->addSql('DROP TABLE announcement_temporalization');
        $this->addSql('DROP TABLE announcement_tutor');
        $this->addSql('DROP TABLE announcement_tutor_connection');
        $this->addSql('DROP TABLE announcement_user');
        $this->addSql('DROP TABLE announcement_user_digital_signature');
        $this->addSql('DROP TABLE answer');
        $this->addSql('DROP TABLE answers_video_quiz');
        $this->addSql('DROP TABLE api_key_request');
        $this->addSql('DROP TABLE api_key_user');
        $this->addSql('DROP TABLE bots');
        $this->addSql('DROP TABLE catalog');
        $this->addSql('DROP TABLE catalog_translation');
        $this->addSql('DROP TABLE categorize');
        $this->addSql('DROP TABLE categorize_answers');
        $this->addSql('DROP TABLE categorize_options');
        $this->addSql('DROP TABLE center');
        $this->addSql('DROP TABLE challenge');
        $this->addSql('DROP TABLE challenge_answers');
        $this->addSql('DROP TABLE challenge_answers_translation');
        $this->addSql('DROP TABLE challenge_duel');
        $this->addSql('DROP TABLE challenge_duel_questions');
        $this->addSql('DROP TABLE challenge_duel_questions_adn');
        $this->addSql('DROP TABLE challenge_questions');
        $this->addSql('DROP TABLE challenge_questions_translation');
        $this->addSql('DROP TABLE challenge_user');
        $this->addSql('DROP TABLE challenge_user_points');
        $this->addSql('DROP TABLE chapter');
        $this->addSql('DROP TABLE chapter_type');
        $this->addSql('DROP TABLE chapter_type_translation');
        $this->addSql('DROP TABLE chat_channel');
        $this->addSql('DROP TABLE chat_channel_user');
        $this->addSql('DROP TABLE chat_message');
        $this->addSql('DROP TABLE chat_message_like');
        $this->addSql('DROP TABLE chat_message_report');
        $this->addSql('DROP TABLE chat_server');
        $this->addSql('DROP TABLE classroom_virtual_result');
        $this->addSql('DROP TABLE classroomvirtual');
        $this->addSql('DROP TABLE classroomvirtual_type');
        $this->addSql('DROP TABLE classroomvirtual_user');
        $this->addSql('DROP TABLE comment_task');
        $this->addSql('DROP TABLE configuration_client_announcement');
        $this->addSql('DROP TABLE content');
        $this->addSql('DROP TABLE course');
        $this->addSql('DROP TABLE course_professional_category');
        $this->addSql('DROP TABLE course_manager');
        $this->addSql('DROP TABLE course_tag');
        $this->addSql('DROP TABLE course_filter');
        $this->addSql('DROP TABLE course_category');
        $this->addSql('DROP TABLE course_category_type_course');
        $this->addSql('DROP TABLE course_category_translation');
        $this->addSql('DROP TABLE course_level');
        $this->addSql('DROP TABLE course_section');
        $this->addSql('DROP TABLE course_section_course_category');
        $this->addSql('DROP TABLE course_section_translation');
        $this->addSql('DROP TABLE course_segment');
        $this->addSql('DROP TABLE course_segment_course');
        $this->addSql('DROP TABLE course_segment_category');
        $this->addSql('DROP TABLE course_segment_category_translation');
        $this->addSql('DROP TABLE course_segment_translation');
        $this->addSql('DROP TABLE course_stat');
        $this->addSql('DROP TABLE cron_job');
        $this->addSql('DROP TABLE cron_report');
        $this->addSql('DROP TABLE department');
        $this->addSql('DROP TABLE documentation');
        $this->addSql('DROP TABLE email_notification');
        $this->addSql('DROP TABLE email_recipient');
        $this->addSql('DROP TABLE email_template');
        $this->addSql('DROP TABLE export');
        $this->addSql('DROP TABLE files_history_task');
        $this->addSql('DROP TABLE files_manager');
        $this->addSql('DROP TABLE files_manager_extra');
        $this->addSql('DROP TABLE files_task');
        $this->addSql('DROP TABLE fillgaps');
        $this->addSql('DROP TABLE filter');
        $this->addSql('DROP TABLE filter_category');
        $this->addSql('DROP TABLE filter_category_translation');
        $this->addSql('DROP TABLE filter_translation');
        $this->addSql('DROP TABLE forum_likes');
        $this->addSql('DROP TABLE forum_post');
        $this->addSql('DROP TABLE forum_report');
        $this->addSql('DROP TABLE gamesword');
        $this->addSql('DROP TABLE generic_token');
        $this->addSql('DROP TABLE guessword');
        $this->addSql('DROP TABLE help_category');
        $this->addSql('DROP TABLE help_category_translation');
        $this->addSql('DROP TABLE help_text');
        $this->addSql('DROP TABLE help_text_translation');
        $this->addSql('DROP TABLE higher_lower');
        $this->addSql('DROP TABLE higher_lower_words');
        $this->addSql('DROP TABLE history_delivery_task');
        $this->addSql('DROP TABLE history_seen_material');
        $this->addSql('DROP TABLE holes');
        $this->addSql('DROP TABLE integration_group');
        $this->addSql('DROP TABLE integration_mapping');
        $this->addSql('DROP TABLE itinerary');
        $this->addSql('DROP TABLE itinerary_filter');
        $this->addSql('DROP TABLE itinerary_course');
        $this->addSql('DROP TABLE itinerary_manager');
        $this->addSql('DROP TABLE itinerary_tags');
        $this->addSql('DROP TABLE itinerary_translation');
        $this->addSql('DROP TABLE itinerary_user');
        $this->addSql('DROP TABLE library');
        $this->addSql('DROP TABLE library_filter');
        $this->addSql('DROP TABLE library_category');
        $this->addSql('DROP TABLE library_category_translation');
        $this->addSql('DROP TABLE library_comment');
        $this->addSql('DROP TABLE library_file');
        $this->addSql('DROP TABLE library_video');
        $this->addSql('DROP TABLE library_views');
        $this->addSql('DROP TABLE main_course_evaluation');
        $this->addSql('DROP TABLE material_course');
        $this->addSql('DROP TABLE material_download_history');
        $this->addSql('DROP TABLE meetingzoom_token');
        $this->addSql('DROP TABLE message');
        $this->addSql('DROP TABLE message_attachment');
        $this->addSql('DROP TABLE news');
        $this->addSql('DROP TABLE news_course_section');
        $this->addSql('DROP TABLE news_translation');
        $this->addSql('DROP TABLE notification');
        $this->addSql('DROP TABLE nps');
        $this->addSql('DROP TABLE nps_question');
        $this->addSql('DROP TABLE nps_question_course');
        $this->addSql('DROP TABLE nps_question_announcement');
        $this->addSql('DROP TABLE nps_question_detail');
        $this->addSql('DROP TABLE nps_question_detail_translation');
        $this->addSql('DROP TABLE nps_question_translation');
        $this->addSql('DROP TABLE ordenar_menormayor');
        $this->addSql('DROP TABLE pages');
        $this->addSql('DROP TABLE pages_translation');
        $this->addSql('DROP TABLE parejas');
        $this->addSql('DROP TABLE parejas_imagen');
        $this->addSql('DROP TABLE pdf');
        $this->addSql('DROP TABLE ppt');
        $this->addSql('DROP TABLE professional_category');
        $this->addSql('DROP TABLE puzzle');
        $this->addSql('DROP TABLE question');
        $this->addSql('DROP TABLE questions_announcement');
        $this->addSql('DROP TABLE recovery_code');
        $this->addSql('DROP TABLE refresh_tokens');
        $this->addSql('DROP TABLE reset_password_request');
        $this->addSql('DROP TABLE roleplay_beginning');
        $this->addSql('DROP TABLE roleplay_ending');
        $this->addSql('DROP TABLE roleplay_project');
        $this->addSql('DROP TABLE roleplay_scene');
        $this->addSql('DROP TABLE roleplay_sequence');
        $this->addSql('DROP TABLE roulette_word');
        $this->addSql('DROP TABLE scorm');
        $this->addSql('DROP TABLE season');
        $this->addSql('DROP TABLE section_default_front');
        $this->addSql('DROP TABLE section_default_front_translation');
        $this->addSql('DROP TABLE sessions_announcement');
        $this->addSql('DROP TABLE setting');
        $this->addSql('DROP TABLE setting_group');
        $this->addSql('DROP TABLE survey');
        $this->addSql('DROP TABLE survey_announcement');
        $this->addSql('DROP TABLE survey_course');
        $this->addSql('DROP TABLE survey_translation');
        $this->addSql('DROP TABLE tag');
        $this->addSql('DROP TABLE task');
        $this->addSql('DROP TABLE task_course');
        $this->addSql('DROP TABLE task_course_group');
        $this->addSql('DROP TABLE task_user');
        $this->addSql('DROP TABLE time_game');
        $this->addSql('DROP TABLE true_or_false');
        $this->addSql('DROP TABLE type_course');
        $this->addSql('DROP TABLE type_course_alerts');
        $this->addSql('DROP TABLE type_course_announcement_step_configuration');
        $this->addSql('DROP TABLE type_course_announcement_step_creation');
        $this->addSql('DROP TABLE type_course_announcement_step_creation_trans');
        $this->addSql('DROP TABLE type_course_translation');
        $this->addSql('DROP TABLE type_diploma');
        $this->addSql('DROP TABLE type_identification');
        $this->addSql('DROP TABLE type_identification_translation');
        $this->addSql('DROP TABLE type_money');
        $this->addSql('DROP TABLE type_money_translation');
        $this->addSql('DROP TABLE type_video');
        $this->addSql('DROP TABLE url_shortener');
        $this->addSql('DROP TABLE user');
        $this->addSql('DROP TABLE user_filter');
        $this->addSql('DROP TABLE manager_filter');
        $this->addSql('DROP TABLE user_comments');
        $this->addSql('DROP TABLE user_company');
        $this->addSql('DROP TABLE user_course');
        $this->addSql('DROP TABLE user_course_chapter');
        $this->addSql('DROP TABLE user_courses_total_time');
        $this->addSql('DROP TABLE user_extra');
        $this->addSql('DROP TABLE user_fields_fundae');
        $this->addSql('DROP TABLE user_history_download_diploma');
        $this->addSql('DROP TABLE user_identification');
        $this->addSql('DROP TABLE user_login');
        $this->addSql('DROP TABLE user_manage');
        $this->addSql('DROP TABLE user_notification');
        $this->addSql('DROP TABLE user_professional_category');
        $this->addSql('DROP TABLE user_roleplay_project');
        $this->addSql('DROP TABLE user_study_level');
        $this->addSql('DROP TABLE user_time');
        $this->addSql('DROP TABLE user_token');
        $this->addSql('DROP TABLE user_vcms_project');
        $this->addSql('DROP TABLE user_work_center');
        $this->addSql('DROP TABLE user_work_department');
        $this->addSql('DROP TABLE vcms_project');
        $this->addSql('DROP TABLE video');
        $this->addSql('DROP TABLE videopreguntas');
        $this->addSql('DROP TABLE videoquiz');
        $this->addSql('DROP TABLE zip_file_task');
    }
}
