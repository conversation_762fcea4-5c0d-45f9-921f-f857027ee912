<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\LtiToolRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=LtiToolRepository::class)
 */
class LtiTool
{
    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\Column(type="string", length=50)
     */
    private $toolsName;

    /**
     * @ORM\Column(type="string", length=45)
     */
    private $name;

    /**
     * @ORM\Column(type="string", length=200)
     */
    private $audience;

    /**
     * @ORM\Column(type="string", length=200)
     */
    private $oidcAuthenticationUrl;

    /**
     * @ORM\Column(type="string", length=200)
     */
    private $launchUrl;

    /**
     * @ORM\Column(type="string", length=200)
     */
    private $deepLinkingUrl;

    /**
     * @ORM\Column(type="string", length=45)
     */
    private $clientId;

    /**
     * @ORM\Column(type="string", length=45)
     */
    private $deploymentsIds;

    /**
     * @ORM\Column(type="string", length=200)
     */
    private $platformJwksUrl;

    /**
     * @ORM\Column(type="string", length=200)
     */
    private $toolJwksUrl;

    /**
     * @ORM\Column(type="string", length=200)
     */
    private $platform;

    /**
     * @ORM\OneToMany(targetEntity=LtiChapter::class, mappedBy="ltiTool")
     */
    private $ltiChapter;

    /**
     * @ORM\Column(type="string", length=45)
     */
    private $identifier;

    /**
     * @ORM\Column(type="string", length=45)
     */
    private $platformKeyChain;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getToolsName(): ?string
    {
        return $this->toolsName;
    }

    public function setToolsName(string $toolsName): self
    {
        $this->toolsName = $toolsName;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getAudience(): ?string
    {
        return $this->audience;
    }

    public function setAudience(string $audience): self
    {
        $this->audience = $audience;

        return $this;
    }

    public function getOidcAuthenticationUrl(): ?string
    {
        return $this->oidcAuthenticationUrl;
    }

    public function setOidcAuthenticationUrl(string $oidcAuthenticationUrl): self
    {
        $this->oidcAuthenticationUrl = $oidcAuthenticationUrl;

        return $this;
    }

    public function getLaunchUrl(): ?string
    {
        return $this->launchUrl;
    }

    public function setLaunchUrl(string $launchUrl): self
    {
        $this->launchUrl = $launchUrl;

        return $this;
    }

    public function getDeepLinkingUrl(): ?string
    {
        return $this->deepLinkingUrl;
    }

    public function setDeepLinkingUrl(string $deepLinkingUrl): self
    {
        $this->deepLinkingUrl = $deepLinkingUrl;

        return $this;
    }

    public function getClientId(): ?string
    {
        return $this->clientId;
    }

    public function setClientId(string $clientId): self
    {
        $this->clientId = $clientId;

        return $this;
    }

    public function getDeploymentsIds(): ?string
    {
        return $this->deploymentsIds;
    }

    public function setDeploymentsIds(string $deploymentsIds): self
    {
        $this->deploymentsIds = $deploymentsIds;

        return $this;
    }

    public function getPlatformJwksUrl(): ?string
    {
        return $this->platformJwksUrl;
    }

    public function setPlatformJwksUrl(string $platformJwksUrl): self
    {
        $this->platformJwksUrl = $platformJwksUrl;

        return $this;
    }

    public function getToolJwksUrl(): ?string
    {
        return $this->toolJwksUrl;
    }

    public function setToolJwksUrl(string $toolJwksUrl): self
    {
        $this->toolJwksUrl = $toolJwksUrl;

        return $this;
    }

    public function getPlatform(): ?string
    {
        return $this->platform;
    }

    public function setPlatform(string $platform): self
    {
        $this->platform = $platform;

        return $this;
    }

    /**
     * Get the value of ltiChapter.
     */
    public function getLtiChapter()
    {
        return $this->ltiChapter;
    }

    /**
     * Set the value of ltiChapter.
     *
     * @return self
     */
    public function setLtiChapter($ltiChapter)
    {
        $this->ltiChapter = $ltiChapter;

        return $this;
    }

    /**
     * Get the value of identifier.
     */
    public function getIdentifier()
    {
        return $this->identifier;
    }

    /**
     * Set the value of identifier.
     *
     * @return self
     */
    public function setIdentifier($identifier)
    {
        $this->identifier = $identifier;

        return $this;
    }

    /**
     * Get the value of platformKeyChain.
     */
    public function getPlatformKeyChain()
    {
        return $this->platformKeyChain;
    }

    /**
     * Set the value of platformKeyChain.
     *
     * @return self
     */
    public function setPlatformKeyChain($platformKeyChain)
    {
        $this->platformKeyChain = $platformKeyChain;

        return $this;
    }
}
