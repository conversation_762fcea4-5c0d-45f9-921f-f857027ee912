<div class="course-contents">
  <h2>{{ 'user.configureFields.course_content'|trans({}, 'messages', app.user.locale) }}</h2>
  <table class="table" width="100%">
    <thead class="thead-light">
      <tr>
        <th scope="row">#</th>
        <th>{{ 'course.configureFields.chapter'|trans({}, 'messages', app.user.locale) }}</th>
        <th>{{ 'user.configureFields.content_type'|trans({}, 'messages', app.user.locale) }}</th>
      </tr>
    </thead>
    <tbody>
      {% for chapter in announcement.course.chapters %}
        <tr>
          <td>{{ chapter.position }}</td>
          <td>{{ chapter.title }}</td>
          <td>{{ chapter.type }}</td>
        </tr>
      {% endfor %}
    </tbody>
  </table>

  <br />
  <br />
  {% if announcement.objectiveAndContents %}
    <div>{{ announcement.objectiveAndContents|raw }}</div>
  {% endif %}
</div>
