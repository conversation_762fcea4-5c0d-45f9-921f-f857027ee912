{% extends 'base.html.twig' %}

{% block title %}Active{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('app') }}
{% endblock %}

{% block body %}
<div id="login" class="activeAccount">
<div class="contentAccount">
    {{ include('reset_password/header.html.twig') }}
 {% if recoveryCode.state == true %}

     <!--{{ 'email.active_account.hello_new'|trans({'%name%': user.firstName}, 'email', user.locale)|raw }}-->
     {{ 'email.active_account.hello_activate_user'|trans({'%name%': user.firstName}, 'email', user.locale)|raw }}

   <div class="groupButton">
        <form method="POST" action="{{path('change_state_account')}}">
            <input type="hidden" value="{{user.id}}" name="id">
            <input type="hidden" value="{{hash}}" name="hash" class="form-control">
            <!--<button  class="button buttonWhite btn-block" type="submit"> {{ 'email.template_email.active_account'|trans({}, 'email', user.locale) }} </button>-->
            <button  class="btn btn-info" type="submit"> {{ 'email.active_account.ok'|trans({}, 'email', user.locale) }} </button>
        </form>
    </div>
    {% else %}
     <div> <h4 class="text-center">   {{ 'email.active_account.page_nof_found'|trans({}, 'email', user.locale) }}</h4></div>
{% endif %}
</div>
</div>

{% endblock %}
