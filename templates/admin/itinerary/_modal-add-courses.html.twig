<div class="modal fade " id="modal-add-courses">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div v-if="savingCourses"
                 class="saving-status d-flex w-100 align-items-center justify-content-center flex-column">
                <div class="loader-container d-flex flex-column align-items-center justify-content-center w-10">
                    <loader :is-loaded="true"></loader>
                    <span>{{ 'itinerary.saving_courses'|trans({}, 'messages',  app.user.locale) }}</span>
                </div>
            </div>
            <div class="modal-body">
                <div class="row course-container">
                    <div class="available-courses col-lg-6" @drop="removeCourse($event)" @dragover.prevent
                         @dragenter.prevent>
                        <div class="col-12 d-flex justify-content-center align-items-center">
                            <h2>{{ 'itinerary.courses.available'|trans({}, 'messages',  app.user.locale) }}</h2>
                        </div>

                        <div class="mx-0 row form-group col-md-12 p-0 gap-2">
                            <input v-model="searchQuery" type="text" class="form-control col-md"
                                   placeholder="{{ 'itinerary.find_available_courses'|trans({}, 'messages',  app.user.locale) }}">

                            <select v-if="categoryFilterOptions" class="form-select col-md"
                                    aria-label="Default select example" v-model="availableCategoryFilter">
                                <option selected :value="null">Seleccione una categoría</option>
                                <option v-for="category in categoryFilterOptions" :value="category.id">
                                    ${category.name}
                                </option>
                            </select>
                        </div>

                        <div class="drop-zone">
                            <div v-if="!pageOfCourses.length"
                                 class="loader-container d-flex align-items-center justify-content-center">
                                <loader :is-loaded="true"></loader>
                            </div>


                            <div v-else class="card course-card drag-el" v-for="course in pageOfCourses" style="cursor: grab;"
                                 :key="course.id" draggable="true" @dragstart="startDrag($event, course)">
                                <div class="course-image">
                                    <img alt="" v-bind:src="imagePath(course.image)">
                                </div>

                                <div class="course-body">
                                    <span class="course-title" data-toggle="tooltip" data-placement="top"
                                          v-bind:title="course.name">${ course.name }</span>
                                    <span class="course-code" data-toggle="tooltip" data-placement="top"
                                          v-bind:title="course.code">${ course.code }</span>
                                    <span class="course-category">${course.category}</span>
                                    <button class="add-action ml-auto btn btn-primary" @click="addCourseByClick(course.id)">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </div>
                            </div>

                        </div>
                        <div>
                            <pagination :items="availableCourses" @items-page="onSearchChangePage"
                                        :number-of-chips="5"></pagination>
                        </div>
                    </div>

                    <div class="selected-courses col-lg-6" @drop="addCourseByDrag($event)" @dragover.prevent
                         @dragenter.prevent>
                        <div class="col-12 d-flex justify-content-center align-items-center">
                                <h2>{{ 'itinerary.courses.selected'|trans({}, 'messages') }}</h2>
                            </div>

                        <div class="mx-0 row form-group col-md-12 p-0 gap-2">
                            <input v-model="selectedSearchQuery" type="text"  class="form-control col-md"
                                        placeholder="{{ 'itinerary.find_selected_courses'|trans({}, 'messages',  app.user.locale) }}">

                            <select v-if="categoryFilterOptions" class="form-select col-md"
                                    aria-label="Default select example" v-model="selectedCategoryFilter">
                                <option selected :value="null">Seleccione una categoría</option>
                                <option v-for="category in categoryFilterOptions" :value="category.id">
                                    ${category.name}
                                </option>
                            </select>
                        </div>



                        <div class="loader-container d-flex align-items-center justify-content-center">
                            <loader :is-loaded="loadingCourses"></loader>
                        </div>

                        <div class="drop-zone" v-if="!loadingCourses">
                            <div class="card course-card drag-el" v-for="(course, i) in filteredSelectedCourses" :key="course.id"  style="cursor: grab;"
                                 draggable="true" @dragstart="startDrag($event, course)">
                                <div class="course-image">
                                    <img alt="" v-bind:src="imagePath(course.image)">
                                </div>

                                <div class="course-body">
                                    <span class="course-title" data-toggle="tooltip" data-placement="top"
                                          v-bind:title="course.name">${ course.name }</span>
                                    <span class="course-code" data-toggle="tooltip" data-placement="top"
                                          v-bind:title="course.code">${ course.code }</span>
                                    <span class="course-category">${course.category}</span>
                                    <button v-if="!course.courseManaged" class="delete-action ml-auto btn btn-danger" @click="removeCourseByIndex(course)">
                                        <i class="fa fa-minus"></i>
                                    </button>
                                    {# <span class="delete-action" @click="removeCourseByIndex(course)">&times;</span> #}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button role="button" class="btn btn-default"
                        data-dismiss="modal">{{ 'stats.modal_close'|trans({}, 'messages',  app.user.locale) }}</button>
                <button role="button" class="btn btn-primary"
                        @click="saveSelectedCourses()">{{ 'common_areas.save'|trans({}, 'messages',  app.user.locale) }}</button>
            </div>
        </div>
    </div>
</div>
