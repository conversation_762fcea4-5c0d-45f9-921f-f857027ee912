<div class="content-panel pb-3 rounded-0 text-right">
    {% set i = 1 %}
  {% for chapter in chaptersType %}
    {% set nameChapter = 'chapter_type.add.' ~ chapter.code %}
    <div class="content_chapter_{{ chapter.id }} content_chapter_btn" style="display:none;">
      <button id="change-state-{{ chapter.id }}" class="action-saveAndReturn btn btn-primary action-save" type="submit" name="ea[newForm][btn]" value="saveAndReturn" form="new-Chapter-form" data-bs-toggle="modal"><span class="btn-label">{{ nameChapter|trans({}, 'chapters') }}</span></button>
    </div>
      {% set i = i + 1 %}
  {% endfor %}
</div>
