<div class="content-panel pb-3 rounded-0">
  <div class="page-actions p-4 d-flex justify-content-end">
    <span id="alert-season-empty"></span>
    {% if course.seasons|length %}
      <select id="season-filter" class="form-control mr-1 w-auto">
        {% if (course.seasons|length) > 1 %}
          <option value="" selected>{{ 'course.configureFields.all_seasons'|trans({}, 'messages', app.user.locale) }}</option>
        {% endif %}
        {% for season in course.seasons %}
          <option value="{{ season.id }}">{{ season.name }}</option>
        {% endfor %}
      </select>
    {% endif %}

    <a class="action-new btn btn-primary mb-1" href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ChapterCrudController').setAction('new').set('courseId', course.id).set('referrer', referrer) }}">{{ 'course.configureFields.add_chapter'|trans({}, 'messages', app.user.locale) }}</a>
  </div>
  {% if chapters|length %}
    <div class="content-panel-body course-chapters" data-orderurl="{{ url('chapter-order') }}">
      {% for chapter in chapters %}
        <div class="chapter" data-id="{{ chapter.id }}" data-season="{{ chapter.season.id|default('0') }}">
          <div class="card">
            <div class="{{ chapter.hasContentCompleted == false ? 'card-chapter-warning' : 'card-chapter-success' }}">
              <div class="icon tooltip-container">
                <i class="{{ chapter.hasContentCompleted == false ? 'fas fa-exclamation-triangle' : ' ' }}"></i>
                {% if chapter.hasContentCompleted == false %}
                  <span class="tooltip">{{ 'message_api.alert.chapter_content'|trans({}, 'message_api', app.user.locale) }}</span>
                {% endif %}
              </div>
            </div>

            <div class="card-header d-none">
              <span class="order badge badge-secondary">{{ chapter.position }}</span>

              <span class="badge badge-secondary pull-right">{{ chapter.type }}</span>
            </div>

            <div class="card-img {{ chapter.hasContentCompleted == false ? 'card-img-background-alert ' : 'card-img-background-default' }}" style="background-image:
                         url('{{ chapter.image ? chapter.thumbnailUrl : asset('assets/chapters/noimg.svg') }}');
                         background-size: cover;">
              <div class="order card-order">{{ chapter.position }}</div>

              <div class="card-icon">
                <img src="{{ asset('assets/chapters/') }}{{ chapter.type.icon }}" alt="{{ chapter.type }}" title="{{ chapter.type }}" style="max-width: 33px" />
                {#   {% if chapter.hasMinimumQuestion == false %}
                                            <span class="tooltip">
                                        {% trans with {'%numberMinimumQuestions%': chapter.getMinimumQuestions} %}Este juego tiene un mínimo de %numberMinimumQuestions% preguntas{% endtrans %}
                                    </span>
                                        {% endif %} #}
              </div>
            </div>

            <div class="card-body card-body-chapter">
              <p class="card-title">{{ chapter.title }}</p>

              <div>
                <h6>
                  {% if chapter.season is defined and chapter.season is not null %}
                    {{ chapter.season.name }}
                  {% else %}
                    <em>[{{ 'course.configureFields.no_seasons'|trans({}, 'messages', app.user.locale) }}]</em>
                  {% endif %}
                </h6>

                <div class="row">
                  <div class="col-md-6">
                    <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ChapterCrudController').setAction('edit').setEntityId(chapter.id).set('referrer', referrer) }}" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>

                    {% if chapter.type.playerUrl is not null %}
                    {{ chapter.type.playerUrl}}
                      {% if chapter.hasContentCompleted %}
                        <a href="#" class="btn btn-primary play btn-sm" data-title="{{ chapter.title }}" data-url="{{ chapter.playerUrlWithUserId }}" data-image="{{ chapter.image  }}"><i class="fa fa-play"></i></a>
                      {% endif %}
                    {% endif %}

                    {% if chapter.hasContentCompleted == false %}
                      <button class="btn btn-primary play btn-sm" disabled><i class="fa fa-play"></i></button>
                    {% endif %}

                    {% if chapter.type.isVcms and chapter.vcmsProject is not null %}
                      <a href="#" class="btn btn-primary btn-sm play" data-title="{{ chapter.title }}" data-url="{{ chapter.getVirtualProjectEdit }}"><i class="fa fa-pencil-square"></i></a>
                    {% endif %}

                    {% if chapter.type.isRoleplay and chapter.roleplayProject is not null %}
                      <a href="#" class="btn btn-primary btn-sm play" data-title="{{ chapter.title }}" data-url="{{ chapter.getRoleplayProjectEdit }}"><i class="fa fa-pencil-square"></i></a>
                    {% endif %}
                  </div>
                  <div class="col-md-6 text-right">
                    <a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ChapterCrudController').setAction('activeChapterAction').setEntityId(chapter.id).set('referrer', referrer) }}" class="btn btn-danger action-delete btn-sm" formaction="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ChapterCrudController').setAction('activeChapterAction').setEntityId(chapter.id).set('referrer', referrer) }}" data-bs-toggle="modal" data-bs-target="#modal-delete"><i class="fa fa-trash"></i> </a>
                    <!--<a href="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ChapterCrudController').setAction('delete').setEntityId(chapter.id).set('referrer', referrer) }}" class="btn btn-danger action-delete btn-sm" formaction="{{ ea_url().unsetAll().setController('App\\Controller\\Admin\\ChapterCrudController').setAction('delete').setEntityId(chapter.id).set('referrer', referrer) }}" data-bs-toggle="modal" data-bs-target="#modal-delete"><i class="fa fa-trash"></i></a>-->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
    <div class="content-panel-footer without-padding without-border">
      <div class="list-pagination">
        <div class="list-pagination-counter">
          <strong>{{ chapters|length }}</strong> {{ 'common_results'|trans({}, 'messages') }}
        </div>
      </div>
    </div>
  {% else %}
    <div class="mt-5">
      <div class="card text-center">
        <div class="card-header" style="height:20rem">
          <h4 style="padding-top:8rem">{{ 'no_content'|trans({}, 'messages') }}</h4>
        </div>
      </div>
    </div>
  {% endif %}
</div>
