<div class="content-panel p-3 course-contents">
    <h2>{{ 'user.configureFields.time_title'|trans({}, 'messages',  app.user.locale) }}</h2>

    <table class="table datagrid with-rounded-top " width="100%">
        <thead class="thead-light">
        <tr>
            <th>
                <span>{{'user.configureFields.date'|trans({}, 'messages',  app.user.locale) }}</span>
            </th>
            <th>
                <span>{{ 'user.configureFields.time_spent'|trans({}, 'messages',  app.user.locale) }}</span>
            </th>

        </tr>
        </thead>

        <tbody>
        {% for times in timeByDay %}
            <tr>
                <td> {{ times.date |format_datetime('full', 'none', locale= app.user.locale )}}
                </td>
                <td> {{ times.total | niceTime }}
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>