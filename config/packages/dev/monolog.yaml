monolog:
    channels: ['user_register', 'starteam', 'mass_import']
    handlers:
        user_register_handler:
            level: debug
            path: '%kernel.logs_dir%/user-register.log'
            type: stream
            channels: [ 'user_register' ]

        starteam:
            type: rotating_file
            level: info
            path: '%kernel.logs_dir%/starteam-%kernel.environment%.log'
            channels: [ 'starteam' ]

        mass_import:
            type: rotating_file
            level: debug
            path: '%kernel.logs_dir%/massive-upload/mass-import-%kernel.environment%.log'
            channels: [ 'mass_import' ]
            max_files: 30

        main:
            type: rotating_file
            path: "%kernel.logs_dir%/%kernel.environment%.log"
            level: error
            channels: ["!event", "!user_register", "!starteam", "!mass_import"]
            max_files: 30
        # uncomment to get logging in your browser
        # you may have to allow bigger header sizes in your Web server configuration
        #firephp:
        #    type: firephp
        #    level: info
        #chromephp:
        #    type: chromephp
        #    level: info
        console:
            type: console
            process_psr_3_messages: false
            channels: ["!event", "!doctrine", "!console"]
